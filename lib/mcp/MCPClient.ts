import { Client } from '@modelcontextprotocol/sdk/client/index.js'
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js'

export interface MCPTool {
  name: string
  description: string
  inputSchema: any
}

export interface MCPResource {
  uri: string
  name: string
  description?: string
  mimeType?: string
}

export class MCPClient {
  private clients: Map<string, Client> = new Map()
  private transports: Map<string, StdioClientTransport> = new Map()

  async connectToServer(serverName: string, command: string, args: string[] = [], env?: Record<string, string>) {
    try {
      console.log(`🔌 Connecting to MCP server: ${serverName}`)
      
      const transport = new StdioClientTransport({
        command,
        args,
        env: { ...process.env, ...env }
      })

      const client = new Client(
        {
          name: 'cryptoagent-pro',
          version: '1.0.0'
        },
        {
          capabilities: {
            tools: {},
            resources: {}
          }
        }
      )

      await client.connect(transport)
      
      this.clients.set(serverName, client)
      this.transports.set(serverName, transport)
      
      console.log(`✅ Connected to MCP server: ${serverName}`)
      return client
    } catch (error) {
      console.error(`❌ Failed to connect to MCP server ${serverName}:`, error)
      throw error
    }
  }

  async disconnectFromServer(serverName: string) {
    const client = this.clients.get(serverName)
    const transport = this.transports.get(serverName)
    
    if (client) {
      await client.close()
      this.clients.delete(serverName)
    }
    
    if (transport) {
      await transport.close()
      this.transports.delete(serverName)
    }
    
    console.log(`🔌 Disconnected from MCP server: ${serverName}`)
  }

  async listTools(serverName?: string): Promise<MCPTool[]> {
    if (serverName) {
      const client = this.clients.get(serverName)
      if (!client) {
        throw new Error(`MCP server ${serverName} not connected`)
      }
      
      const response = await client.listTools()
      return response.tools || []
    }

    // List tools from all connected servers
    const allTools: MCPTool[] = []
    for (const [name, client] of this.clients) {
      try {
        const response = await client.listTools()
        const tools = (response.tools || []).map(tool => ({
          ...tool,
          name: `${name}:${tool.name}` // Prefix with server name
        }))
        allTools.push(...tools)
      } catch (error) {
        console.error(`Failed to list tools from ${name}:`, error)
      }
    }
    
    return allTools
  }

  async listResources(serverName?: string): Promise<MCPResource[]> {
    if (serverName) {
      const client = this.clients.get(serverName)
      if (!client) {
        throw new Error(`MCP server ${serverName} not connected`)
      }
      
      const response = await client.listResources()
      return response.resources || []
    }

    // List resources from all connected servers
    const allResources: MCPResource[] = []
    for (const [name, client] of this.clients) {
      try {
        const response = await client.listResources()
        allResources.push(...(response.resources || []))
      } catch (error) {
        console.error(`Failed to list resources from ${name}:`, error)
      }
    }
    
    return allResources
  }

  async callTool(toolName: string, args: any = {}): Promise<any> {
    // Parse server name from tool name if prefixed
    let serverName: string | undefined
    let actualToolName = toolName
    
    if (toolName.includes(':')) {
      [serverName, actualToolName] = toolName.split(':', 2)
    }

    if (serverName) {
      const client = this.clients.get(serverName)
      if (!client) {
        throw new Error(`MCP server ${serverName} not connected`)
      }
      
      return await client.callTool({
        name: actualToolName,
        arguments: args
      })
    }

    // Try to find the tool in any connected server
    for (const [name, client] of this.clients) {
      try {
        const tools = await client.listTools()
        const tool = tools.tools?.find(t => t.name === actualToolName)
        
        if (tool) {
          return await client.callTool({
            name: actualToolName,
            arguments: args
          })
        }
      } catch (error) {
        console.error(`Failed to call tool ${actualToolName} on ${name}:`, error)
      }
    }
    
    throw new Error(`Tool ${actualToolName} not found in any connected MCP server`)
  }

  async readResource(uri: string, serverName?: string): Promise<any> {
    if (serverName) {
      const client = this.clients.get(serverName)
      if (!client) {
        throw new Error(`MCP server ${serverName} not connected`)
      }
      
      return await client.readResource({ uri })
    }

    // Try to read resource from any connected server
    for (const [name, client] of this.clients) {
      try {
        return await client.readResource({ uri })
      } catch (error) {
        console.error(`Failed to read resource ${uri} from ${name}:`, error)
      }
    }
    
    throw new Error(`Resource ${uri} not found in any connected MCP server`)
  }

  getConnectedServers(): string[] {
    return Array.from(this.clients.keys())
  }

  isConnected(serverName: string): boolean {
    return this.clients.has(serverName)
  }

  async disconnectAll() {
    const serverNames = Array.from(this.clients.keys())
    await Promise.all(serverNames.map(name => this.disconnectFromServer(name)))
  }
}

// Singleton instance
export const mcpClient = new MCPClient()
