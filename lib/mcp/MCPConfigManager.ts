import { mcpClient } from './MCPClient'

export interface MCPServerConfig {
  name: string
  command: string
  args: string[]
  env?: Record<string, string>
  enabled: boolean
  description: string
  category: 'database' | 'web' | 'filesystem' | 'api' | 'other'
  autoConnect: boolean
}

export class MCPConfigManager {
  private servers: Map<string, MCPServerConfig> = new Map()

  constructor() {
    this.initializeDefaultServers()
    this.loadConfiguration()
  }

  private initializeDefaultServers() {
    const defaultServers: MCPServerConfig[] = [
      {
        name: 'supabase',
        command: 'npx',
        args: ['-y', '@supabase/mcp-server-supabase'],
        env: {
          SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://zcyqjnbtojltgymtqjnp.supabase.co',
          SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpjeXFqbmJ0b2psdGd5bXRxam5wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM4Nzk4MzksImV4cCI6MjA2OTQ1NTgzOX0.1Xnr8HoCu2GUmk-CwTDOaH6AD29x7Tq7zbrmrrYVlEI',
          SUPABASE_ACCESS_TOKEN: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpjeXFqbmJ0b2psdGd5bXRxam5wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM4Nzk4MzksImV4cCI6MjA2OTQ1NTgzOX0.1Xnr8HoCu2GUmk-CwTDOaH6AD29x7Tq7zbrmrrYVlEI'
        },
        enabled: true,
        description: 'Supabase database integration for user management and data storage',
        category: 'database',
        autoConnect: true
      },
      {
        name: 'brave-search',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-brave-search'],
        env: {
          BRAVE_API_KEY: process.env.BRAVE_API_KEY || 'BSAfxV16je27QEThZotnM7IWlbWqwYR'
        },
        enabled: true,
        description: 'Brave Search API for web search and market research',
        category: 'web',
        autoConnect: true
      },
      {
        name: 'filesystem',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-filesystem', '/Users/<USER>/Desktop/cryptoagent-pro'],
        enabled: false,
        description: 'Local filesystem access for reading project files',
        category: 'filesystem',
        autoConnect: false
      },
      {
        name: 'github',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-github'],
        env: {
          GITHUB_PERSONAL_ACCESS_TOKEN: process.env.GITHUB_TOKEN || ''
        },
        enabled: false,
        description: 'GitHub integration for repository management',
        category: 'api',
        autoConnect: false
      },
      {
        name: 'postgres',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-postgres'],
        env: {
          POSTGRES_CONNECTION_STRING: process.env.DATABASE_URL || ''
        },
        enabled: false,
        description: 'PostgreSQL database integration',
        category: 'database',
        autoConnect: false
      }
    ]

    defaultServers.forEach(server => {
      this.servers.set(server.name, server)
    })
  }

  private loadConfiguration() {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem('mcp-config')
        if (stored) {
          const config = JSON.parse(stored)
          Object.entries(config).forEach(([name, serverConfig]) => {
            const existing = this.servers.get(name)
            if (existing) {
              this.servers.set(name, { ...existing, ...serverConfig as MCPServerConfig })
            }
          })
        }
      } catch (error) {
        console.warn('Failed to load MCP configuration:', error)
      }
    }
  }

  private saveConfiguration() {
    if (typeof window !== 'undefined') {
      try {
        const config = Object.fromEntries(this.servers)
        localStorage.setItem('mcp-config', JSON.stringify(config))
      } catch (error) {
        console.warn('Failed to save MCP configuration:', error)
      }
    }
  }

  // Server Management
  getServer(name: string): MCPServerConfig | null {
    return this.servers.get(name) || null
  }

  getAllServers(): MCPServerConfig[] {
    return Array.from(this.servers.values())
  }

  getEnabledServers(): MCPServerConfig[] {
    return Array.from(this.servers.values()).filter(server => server.enabled)
  }

  getServersByCategory(category: MCPServerConfig['category']): MCPServerConfig[] {
    return Array.from(this.servers.values()).filter(server => server.category === category)
  }

  updateServer(name: string, updates: Partial<MCPServerConfig>) {
    const existing = this.servers.get(name)
    if (existing) {
      this.servers.set(name, { ...existing, ...updates })
      this.saveConfiguration()
    }
  }

  addServer(server: MCPServerConfig) {
    this.servers.set(server.name, server)
    this.saveConfiguration()
  }

  removeServer(name: string) {
    this.servers.delete(name)
    this.saveConfiguration()
  }

  // Connection Management
  async connectToServer(name: string): Promise<boolean> {
    const server = this.servers.get(name)
    if (!server) {
      throw new Error(`Server ${name} not found`)
    }

    if (!server.enabled) {
      throw new Error(`Server ${name} is disabled`)
    }

    try {
      await mcpClient.connectToServer(name, server.command, server.args, server.env)
      return true
    } catch (error) {
      console.error(`Failed to connect to MCP server ${name}:`, error)
      return false
    }
  }

  async disconnectFromServer(name: string): Promise<void> {
    await mcpClient.disconnectFromServer(name)
  }

  async connectToEnabledServers(): Promise<{ connected: string[], failed: string[] }> {
    const enabledServers = this.getEnabledServers().filter(s => s.autoConnect)
    const connected: string[] = []
    const failed: string[] = []

    for (const server of enabledServers) {
      try {
        const success = await this.connectToServer(server.name)
        if (success) {
          connected.push(server.name)
        } else {
          failed.push(server.name)
        }
      } catch (error) {
        console.error(`Failed to connect to ${server.name}:`, error)
        failed.push(server.name)
      }
    }

    return { connected, failed }
  }

  async disconnectFromAllServers(): Promise<void> {
    await mcpClient.disconnectAll()
  }

  // Tool and Resource Management
  async getAvailableTools() {
    return await mcpClient.listTools()
  }

  async getAvailableResources() {
    return await mcpClient.listResources()
  }

  async callTool(toolName: string, args: any = {}) {
    return await mcpClient.callTool(toolName, args)
  }

  async readResource(uri: string, serverName?: string) {
    return await mcpClient.readResource(uri, serverName)
  }

  // Status and Health
  getConnectionStatus(): Record<string, boolean> {
    const status: Record<string, boolean> = {}
    for (const serverName of this.servers.keys()) {
      status[serverName] = mcpClient.isConnected(serverName)
    }
    return status
  }

  getConnectedServers(): string[] {
    return mcpClient.getConnectedServers()
  }

  // Configuration Validation
  validateServerConfig(server: MCPServerConfig): { valid: boolean, errors: string[] } {
    const errors: string[] = []

    if (!server.name) {
      errors.push('Server name is required')
    }

    if (!server.command) {
      errors.push('Command is required')
    }

    if (!server.description) {
      errors.push('Description is required')
    }

    // Validate environment variables for specific servers
    if (server.name === 'supabase') {
      if (!server.env?.SUPABASE_URL) {
        errors.push('SUPABASE_URL is required for Supabase server')
      }
      if (!server.env?.SUPABASE_ANON_KEY) {
        errors.push('SUPABASE_ANON_KEY is required for Supabase server')
      }
    }

    if (server.name === 'brave-search') {
      if (!server.env?.BRAVE_API_KEY) {
        errors.push('BRAVE_API_KEY is required for Brave Search server')
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  // Utility Methods
  async testServerConnection(name: string): Promise<{ success: boolean, message: string }> {
    try {
      const success = await this.connectToServer(name)
      if (success) {
        // Test by listing tools
        await mcpClient.listTools(name)
        return { success: true, message: 'Connection successful' }
      } else {
        return { success: false, message: 'Failed to connect' }
      }
    } catch (error) {
      return { 
        success: false, 
        message: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  // Export/Import Configuration
  exportConfiguration(): string {
    const config = Object.fromEntries(this.servers)
    return JSON.stringify(config, null, 2)
  }

  importConfiguration(configJson: string): { success: boolean, message: string } {
    try {
      const config = JSON.parse(configJson)
      
      // Validate configuration
      for (const [name, serverConfig] of Object.entries(config)) {
        const validation = this.validateServerConfig(serverConfig as MCPServerConfig)
        if (!validation.valid) {
          return { 
            success: false, 
            message: `Invalid configuration for ${name}: ${validation.errors.join(', ')}` 
          }
        }
      }

      // Import configuration
      this.servers.clear()
      Object.entries(config).forEach(([name, serverConfig]) => {
        this.servers.set(name, serverConfig as MCPServerConfig)
      })

      this.saveConfiguration()
      return { success: true, message: 'Configuration imported successfully' }
    } catch (error) {
      return { 
        success: false, 
        message: `Failed to import configuration: ${error instanceof Error ? error.message : 'Unknown error'}` 
      }
    }
  }

  // Reset to defaults
  resetToDefaults() {
    this.servers.clear()
    this.initializeDefaultServers()
    this.saveConfiguration()
  }
}

// Singleton instance
export const mcpConfigManager = new MCPConfigManager()
