'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { webSocketManager, CombinedMarketUpdate, WebSocketStatus } from '../websocket/WebSocketManager'
import { MarketDataUpdate, OrderBookUpdate, TradeUpdate } from '../websocket/MarketDataWebSocket'
import { SentimentUpdate } from '../websocket/SentimentWebSocket'

// Hook for combined market data and sentiment
export function useCombinedMarketData(symbol: string, exchanges: string[] = ['binance', 'bybit']) {
  const [data, setData] = useState<CombinedMarketUpdate | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const callbackRef = useRef<((update: CombinedMarketUpdate) => void) | null>(null)

  const handleUpdate = useCallback((update: CombinedMarketUpdate) => {
    setData(update)
    setIsLoading(false)
    setError(null)
  }, [])

  useEffect(() => {
    if (!symbol) return

    callbackRef.current = handleUpdate
    
    try {
      webSocketManager.subscribeCombined(symbol, exchanges, handleUpdate)
      
      // Set loading timeout
      const timeout = setTimeout(() => {
        if (isLoading) {
          setError('Timeout waiting for market data')
          setIsLoading(false)
        }
      }, 10000)

      return () => {
        clearTimeout(timeout)
        if (callbackRef.current) {
          webSocketManager.unsubscribeCombined(symbol, callbackRef.current)
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to subscribe to market data')
      setIsLoading(false)
    }
  }, [symbol, exchanges.join(','), handleUpdate, isLoading])

  return { data, isLoading, error }
}

// Hook for market data only
export function useMarketData(symbol: string, exchange: string) {
  const [data, setData] = useState<MarketDataUpdate | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const callbackRef = useRef<((update: MarketDataUpdate) => void) | null>(null)

  const handleUpdate = useCallback((update: MarketDataUpdate) => {
    setData(update)
    setIsLoading(false)
    setError(null)
  }, [])

  useEffect(() => {
    if (!symbol || !exchange) return

    callbackRef.current = handleUpdate
    
    try {
      webSocketManager.subscribeMarketData(symbol, exchange, handleUpdate)
      
      const timeout = setTimeout(() => {
        if (isLoading) {
          setError('Timeout waiting for market data')
          setIsLoading(false)
        }
      }, 10000)

      return () => {
        clearTimeout(timeout)
        // Note: Individual market data unsubscribe would need to be implemented
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to subscribe to market data')
      setIsLoading(false)
    }
  }, [symbol, exchange, handleUpdate, isLoading])

  return { data, isLoading, error }
}

// Hook for sentiment data only
export function useSentimentData(symbol: string) {
  const [data, setData] = useState<SentimentUpdate | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const callbackRef = useRef<((update: SentimentUpdate) => void) | null>(null)

  const handleUpdate = useCallback((update: SentimentUpdate) => {
    setData(update)
    setIsLoading(false)
    setError(null)
  }, [])

  useEffect(() => {
    if (!symbol) return

    callbackRef.current = handleUpdate
    
    try {
      webSocketManager.subscribeSentiment(symbol, handleUpdate)
      
      const timeout = setTimeout(() => {
        if (isLoading) {
          setError('Timeout waiting for sentiment data')
          setIsLoading(false)
        }
      }, 10000)

      return () => {
        clearTimeout(timeout)
        // Note: Individual sentiment unsubscribe would need to be implemented
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to subscribe to sentiment data')
      setIsLoading(false)
    }
  }, [symbol, handleUpdate, isLoading])

  return { data, isLoading, error }
}

// Hook for order book data
export function useOrderBook(symbol: string, exchange: string) {
  const [data, setData] = useState<OrderBookUpdate | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const callbackRef = useRef<((update: OrderBookUpdate) => void) | null>(null)

  const handleUpdate = useCallback((update: OrderBookUpdate) => {
    setData(update)
    setIsLoading(false)
    setError(null)
  }, [])

  useEffect(() => {
    if (!symbol || !exchange) return

    callbackRef.current = handleUpdate
    
    try {
      webSocketManager.subscribeOrderBook(symbol, exchange, handleUpdate)
      
      const timeout = setTimeout(() => {
        if (isLoading) {
          setError('Timeout waiting for order book data')
          setIsLoading(false)
        }
      }, 10000)

      return () => {
        clearTimeout(timeout)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to subscribe to order book')
      setIsLoading(false)
    }
  }, [symbol, exchange, handleUpdate, isLoading])

  return { data, isLoading, error }
}

// Hook for trade data
export function useTrades(symbol: string, exchange: string) {
  const [trades, setTrades] = useState<TradeUpdate[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const maxTrades = 50
  const callbackRef = useRef<((update: TradeUpdate) => void) | null>(null)

  const handleUpdate = useCallback((update: TradeUpdate) => {
    setTrades(prev => {
      const newTrades = [update, ...prev].slice(0, maxTrades)
      return newTrades
    })
    setIsLoading(false)
    setError(null)
  }, [])

  useEffect(() => {
    if (!symbol || !exchange) return

    callbackRef.current = handleUpdate
    
    try {
      webSocketManager.subscribeTrades(symbol, exchange, handleUpdate)
      
      const timeout = setTimeout(() => {
        if (isLoading) {
          setError('Timeout waiting for trade data')
          setIsLoading(false)
        }
      }, 10000)

      return () => {
        clearTimeout(timeout)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to subscribe to trades')
      setIsLoading(false)
    }
  }, [symbol, exchange, handleUpdate, isLoading])

  return { trades, isLoading, error }
}

// Hook for WebSocket status
export function useWebSocketStatus() {
  const [status, setStatus] = useState<WebSocketStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const updateStatus = () => {
      try {
        const currentStatus = webSocketManager.getStatus()
        setStatus(currentStatus)
        setIsLoading(false)
      } catch (error) {
        console.error('Failed to get WebSocket status:', error)
        setIsLoading(false)
      }
    }

    // Initial status
    updateStatus()

    // Update status every 5 seconds
    const interval = setInterval(updateStatus, 5000)

    return () => clearInterval(interval)
  }, [])

  return { status, isLoading }
}

// Hook for trading signals
export function useTradingSignals(symbol: string) {
  const [signals, setSignals] = useState<{
    signal: 'BUY' | 'SELL' | 'HOLD'
    confidence: number
    reasons: string[]
    timestamp: string
  } | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!symbol) return

    const updateSignals = () => {
      try {
        const tradingSignals = webSocketManager.getTradingSignals(symbol)
        setSignals(tradingSignals)
        setIsLoading(false)
      } catch (error) {
        console.error('Failed to get trading signals:', error)
        setIsLoading(false)
      }
    }

    // Initial signals
    updateSignals()

    // Update signals every 30 seconds
    const interval = setInterval(updateSignals, 30000)

    return () => clearInterval(interval)
  }, [symbol])

  return { signals, isLoading }
}

// Hook for aggregated market data
export function useAggregatedMarketData(symbol: string) {
  const [data, setData] = useState<{
    averagePrice: number
    totalVolume: number
    priceSpread: number
    exchanges: string[]
    lastUpdate: string
  } | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!symbol) return

    const updateData = () => {
      try {
        const aggregatedData = webSocketManager.getAggregatedMarketData(symbol)
        setData(aggregatedData)
        setIsLoading(false)
      } catch (error) {
        console.error('Failed to get aggregated market data:', error)
        setIsLoading(false)
      }
    }

    // Initial data
    updateData()

    // Update data every 10 seconds
    const interval = setInterval(updateData, 10000)

    return () => clearInterval(interval)
  }, [symbol])

  return { data, isLoading }
}
