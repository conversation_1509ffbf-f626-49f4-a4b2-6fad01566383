'use client'

import { useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '../supabase' // Frontend client with ANON key
import { useRouter } from 'next/navigation'

interface AuthState {
  user: User | null
  session: Session | null
  loading: boolean
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true
  })

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Error getting session:', error)
        }
        
        setAuthState({
          user: session?.user ?? null,
          session: session,
          loading: false
        })
      } catch (error) {
        console.error('Error in getInitialSession:', error)
        setAuthState({
          user: null,
          session: null,
          loading: false
        })
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email)
        
        setAuthState({
          user: session?.user ?? null,
          session: session,
          loading: false
        })
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) {
        console.error('Error signing out:', error)
      }
    } catch (error) {
      console.error('Error in signOut:', error)
    }
  }

  return {
    ...authState,
    signOut,
    isAuthenticated: !!authState.user
  }
}

// Hook for protected routes
export function useRequireAuth(redirectTo: string = '/auth/login') {
  const auth = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!auth.loading && !auth.isAuthenticated) {
      router.push(redirectTo)
    }
  }, [auth.loading, auth.isAuthenticated, router, redirectTo])

  return auth
}

// Hook to redirect authenticated users away from auth pages
export function useRedirectIfAuthenticated(redirectTo: string = '/dashboard') {
  const auth = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!auth.loading && auth.isAuthenticated) {
      router.push(redirectTo)
    }
  }, [auth.loading, auth.isAuthenticated, router, redirectTo])

  return auth
}
