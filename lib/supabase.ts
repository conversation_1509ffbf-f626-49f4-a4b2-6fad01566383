import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Validate URL format
const isValidUrl = (url: string) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Check if environment variables are set and valid
const hasValidCredentials = supabaseUrl && 
                          supabaseAnonKey && 
                          supabaseUrl !== 'your_supabase_url_here' &&
                          supabaseAnonKey !== 'your_supabase_anon_key_here' &&
                          supabaseUrl !== 'https://placeholder.supabase.co' &&
                          supabaseAnonKey !== 'placeholder-key' &&
                          isValidUrl(supabaseUrl)

if (!hasValidCredentials) {
  console.warn('⚠️ Supabase environment variables not found or invalid!')
  console.warn('Please set valid NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in .env.local')
  console.warn('You can get these from your Supabase project settings.')
}

// Only create client if we have valid credentials
let supabase: any = null
let supabaseAdmin: any = null

if (hasValidCredentials) {
  try {
    supabase = createClient(supabaseUrl!, supabaseAnonKey!, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
      }
    })

    supabaseAdmin = createClient(
      supabaseUrl!,
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )
    
    console.log('✅ Supabase client initialized successfully')
  } catch (error) {
    console.error('❌ Failed to initialize Supabase client:', error)
  }
}

// Fallback to mock client if no valid credentials or initialization failed
if (!supabase) {
  console.log('🔧 Using mock Supabase client for development')
  supabase = {
    auth: {
      getSession: async () => ({ data: { session: null }, error: null }),
      signInWithPassword: async () => ({ data: null, error: { message: 'Supabase not configured' } }),
      signUp: async () => ({ data: null, error: { message: 'Supabase not configured' } }),
      signOut: async () => ({ error: null }),
      onAuthStateChange: (callback: any) => ({ data: { subscription: { unsubscribe: () => {} } } })
    },
    from: () => ({
      select: () => ({ 
        eq: () => ({ 
          order: () => ({ 
            limit: () => ({ data: [], error: null }) 
          }) 
        }) 
      }),
      insert: () => ({ 
        select: () => ({ 
          single: () => ({ data: null, error: { message: 'Supabase not configured' } }) 
        }) 
      }),
      update: () => ({ 
        eq: () => ({ 
          select: () => ({ 
            single: () => ({ data: null, error: null }) 
          }) 
        }) 
      })
    }),
    channel: () => ({
      on: () => ({ subscribe: () => ({ unsubscribe: () => {} }) })
    })
  }
  
  supabaseAdmin = supabase
}

export { supabase, supabaseAdmin }

// Database types for better type safety
export interface TradingAgent {
  id: string
  name: string
  specialization: string
  ai_provider: string
  ai_model: string
  status: 'ACTIVE' | 'THINKING' | 'ERROR' | 'IDLE' | 'TRADING'
  performance_score: number
  current_task?: string
  last_activity: string
  config: Record<string, any>
  organization_id: string
  created_at: string
}

export interface TradingPosition {
  id: string
  agent_id: string
  exchange: string
  symbol: string
  side: 'BUY' | 'SELL'
  size: number
  entry_price: number
  current_price?: number
  pnl: number
  pnl_percentage: number
  status: 'OPEN' | 'CLOSED' | 'CANCELLED'
  opened_at: string
  closed_at?: string
  organization_id: string
}

export interface AIAnalysis {
  id: string
  agent_id: string
  analysis_type: string
  symbol: string
  timeframe: string
  raw_data: Record<string, any>
  analysis_result: Record<string, any>
  confidence_score: number
  recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL'
  ai_model_used: string
  tokens_used: number
  organization_id: string
  created_at: string
} 