export interface BacktestConfig {
  startDate: Date
  endDate: Date
  initialBalance: number
  symbols: string[]
  strategy: string
  parameters: Record<string, any>
}

export interface BacktestResult {
  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number
  totalPnl: number
  totalPnlPercent: number
  maxDrawdown: number
  sharpeRatio: number
  profitFactor: number
  averageWin: number
  averageLoss: number
  bestTrade: number
  worstTrade: number
  finalBalance: number
  trades: BacktestTrade[]
}

export interface BacktestTrade {
  id: string
  symbol: string
  side: 'buy' | 'sell'
  size: number
  entryPrice: number
  exitPrice: number
  pnl: number
  pnlPercent: number
  entryTime: Date
  exitTime: Date
  reason: string
}

export interface HistoricalData {
  symbol: string
  timestamp: Date
  open: number
  high: number
  low: number
  close: number
  volume: number
}

export class BacktestEngine {
  private config: BacktestConfig
  private historicalData: Map<string, HistoricalData[]> = new Map()
  private trades: BacktestTrade[] = []
  private currentBalance: number
  private positions: Map<string, { size: number; entryPrice: number; entryTime: Date }> = new Map()

  constructor(config: BacktestConfig) {
    this.config = config
    this.currentBalance = config.initialBalance
  }

  /**
   * Load historical data
   */
  async loadHistoricalData(): Promise<void> {
    console.log('📊 Loading historical data...')
    
    for (const symbol of this.config.symbols) {
      try {
        const data = await this.fetchHistoricalData(symbol)
        this.historicalData.set(symbol, data)
        console.log(`✅ Loaded ${data.length} candles for ${symbol}`)
      } catch (error) {
        console.error(`❌ Failed to load data for ${symbol}:`, error)
      }
    }
  }

  /**
   * Run backtest
   */
  async runBacktest(): Promise<BacktestResult> {
    console.log('🚀 Starting backtest...')
    
    const startTime = this.config.startDate.getTime()
    const endTime = this.config.endDate.getTime()
    
    // Get all timestamps from historical data
    const allTimestamps = new Set<number>()
    this.historicalData.forEach(data => {
      data.forEach(candle => {
        if (candle.timestamp.getTime() >= startTime && candle.timestamp.getTime() <= endTime) {
          allTimestamps.add(candle.timestamp.getTime())
        }
      })
    })

    // Sort timestamps
    const sortedTimestamps = Array.from(allTimestamps).sort((a, b) => a - b)

    // Process each timestamp
    for (const timestamp of sortedTimestamps) {
      const currentTime = new Date(timestamp)
      await this.processTimeframe(currentTime)
    }

    // Close remaining positions
    await this.closeAllPositions()

    return this.calculateResults()
  }

  /**
   * Process a single timeframe
   */
  private async processTimeframe(currentTime: Date): Promise<void> {
    const currentData: Record<string, HistoricalData> = {}

    // Get current data for all symbols
    this.historicalData.forEach((data, symbol) => {
      const candle = data.find(c => c.timestamp.getTime() === currentTime.getTime())
      if (candle) {
        currentData[symbol] = candle
      }
    })

    // Run strategy analysis
    const signals = await this.runStrategy(currentData, currentTime)

    // Execute signals
    for (const signal of signals) {
      await this.executeSignal(signal, currentTime)
    }
  }

  /**
   * Run trading strategy
   */
  private async runStrategy(
    currentData: Record<string, HistoricalData>,
    currentTime: Date
  ): Promise<any[]> {
    // This is a placeholder - implement your strategy logic here
    const signals: any[] = []

    for (const [symbol, data] of Object.entries(currentData)) {
      // Example: Simple moving average crossover strategy
      const shortMA = this.calculateSMA(symbol, 10, currentTime)
      const longMA = this.calculateSMA(symbol, 20, currentTime)

      if (shortMA && longMA) {
        if (shortMA > longMA && !this.positions.has(symbol)) {
          // Buy signal
          signals.push({
            symbol,
            side: 'buy',
            price: data.close,
            reason: 'MA crossover - bullish'
          })
        } else if (shortMA < longMA && this.positions.has(symbol)) {
          // Sell signal
          signals.push({
            symbol,
            side: 'sell',
            price: data.close,
            reason: 'MA crossover - bearish'
          })
        }
      }
    }

    return signals
  }

  /**
   * Execute trading signal
   */
  private async executeSignal(signal: any, currentTime: Date): Promise<void> {
    const { symbol, side, price, reason } = signal

    if (side === 'buy' && !this.positions.has(symbol)) {
      // Calculate position size (1% of balance)
      const positionSize = this.currentBalance * 0.01
      const quantity = positionSize / price

      this.positions.set(symbol, {
        size: quantity,
        entryPrice: price,
        entryTime: currentTime
      })

      this.currentBalance -= positionSize

      console.log(`📈 BUY ${symbol}: ${quantity.toFixed(4)} @ $${price}`)
    } else if (side === 'sell' && this.positions.has(symbol)) {
      const position = this.positions.get(symbol)!
      const pnl = (price - position.entryPrice) * position.size
      const pnlPercent = ((price - position.entryPrice) / position.entryPrice) * 100

      this.currentBalance += (position.size * price)

      const trade: BacktestTrade = {
        id: this.generateTradeId(),
        symbol,
        side: 'sell',
        size: position.size,
        entryPrice: position.entryPrice,
        exitPrice: price,
        pnl,
        pnlPercent,
        entryTime: position.entryTime,
        exitTime: currentTime,
        reason
      }

      this.trades.push(trade)
      this.positions.delete(symbol)

      console.log(`📉 SELL ${symbol}: ${position.size.toFixed(4)} @ $${price} (P&L: $${pnl.toFixed(2)})`)
    }
  }

  /**
   * Close all remaining positions
   */
  private async closeAllPositions(): Promise<void> {
    for (const [symbol, position] of this.positions) {
      // Get last price for the symbol
      const symbolData = this.historicalData.get(symbol)
      if (symbolData && symbolData.length > 0) {
        const lastPrice = symbolData[symbolData.length - 1].close
        const pnl = (lastPrice - position.entryPrice) * position.size
        const pnlPercent = ((lastPrice - position.entryPrice) / position.entryPrice) * 100

        this.currentBalance += (position.size * lastPrice)

        const trade: BacktestTrade = {
          id: this.generateTradeId(),
          symbol,
          side: 'sell',
          size: position.size,
          entryPrice: position.entryPrice,
          exitPrice: lastPrice,
          pnl,
          pnlPercent,
          entryTime: position.entryTime,
          exitTime: new Date(),
          reason: 'End of backtest'
        }

        this.trades.push(trade)
      }
    }

    this.positions.clear()
  }

  /**
   * Calculate backtest results
   */
  private calculateResults(): BacktestResult {
    const winningTrades = this.trades.filter(t => t.pnl > 0)
    const losingTrades = this.trades.filter(t => t.pnl < 0)

    const totalPnl = this.trades.reduce((sum, t) => sum + t.pnl, 0)
    const totalPnlPercent = (totalPnl / this.config.initialBalance) * 100

    const averageWin = winningTrades.length > 0 
      ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) / winningTrades.length 
      : 0

    const averageLoss = losingTrades.length > 0 
      ? losingTrades.reduce((sum, t) => sum + t.pnl, 0) / losingTrades.length 
      : 0

    const profitFactor = averageLoss !== 0 ? Math.abs(averageWin / averageLoss) : 0

    return {
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: this.trades.length > 0 ? (winningTrades.length / this.trades.length) * 100 : 0,
      totalPnl,
      totalPnlPercent,
      maxDrawdown: this.calculateMaxDrawdown(),
      sharpeRatio: this.calculateSharpeRatio(),
      profitFactor,
      averageWin,
      averageLoss,
      bestTrade: Math.max(...this.trades.map(t => t.pnl)),
      worstTrade: Math.min(...this.trades.map(t => t.pnl)),
      finalBalance: this.currentBalance,
      trades: this.trades
    }
  }

  /**
   * Calculate Simple Moving Average
   */
  private calculateSMA(symbol: string, period: number, currentTime: Date): number | null {
    const data = this.historicalData.get(symbol)
    if (!data) return null

    const currentIndex = data.findIndex(c => c.timestamp.getTime() === currentTime.getTime())
    if (currentIndex < period - 1) return null

    const prices = data.slice(currentIndex - period + 1, currentIndex + 1).map(c => c.close)
    return prices.reduce((sum, price) => sum + price, 0) / period
  }

  /**
   * Calculate maximum drawdown
   */
  private calculateMaxDrawdown(): number {
    let maxDrawdown = 0
    let peak = this.config.initialBalance
    let currentBalance = this.config.initialBalance

    this.trades
      .sort((a, b) => a.exitTime.getTime() - b.exitTime.getTime())
      .forEach(trade => {
        currentBalance += trade.pnl
        
        if (currentBalance > peak) {
          peak = currentBalance
        }

        const drawdown = (peak - currentBalance) / peak * 100
        if (drawdown > maxDrawdown) {
          maxDrawdown = drawdown
        }
      })

    return maxDrawdown
  }

  /**
   * Calculate Sharpe ratio
   */
  private calculateSharpeRatio(): number {
    const returns = this.trades.map(t => t.pnlPercent)

    if (returns.length === 0) return 0

    const meanReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - meanReturn, 2), 0) / returns.length
    const stdDev = Math.sqrt(variance)

    return stdDev !== 0 ? meanReturn / stdDev : 0
  }

  /**
   * Fetch historical data (placeholder)
   */
  private async fetchHistoricalData(symbol: string): Promise<HistoricalData[]> {
    // This is a placeholder - implement actual data fetching
    // You can use APIs like Alpha Vantage, Yahoo Finance, etc.
    
    const mockData: HistoricalData[] = []
    const startTime = this.config.startDate.getTime()
    const endTime = this.config.endDate.getTime()
    
    // Generate mock data
    let currentPrice = 100
    for (let time = startTime; time <= endTime; time += 24 * 60 * 60 * 1000) { // Daily data
      const change = (Math.random() - 0.5) * 10
      currentPrice += change
      
      mockData.push({
        symbol,
        timestamp: new Date(time),
        open: currentPrice,
        high: currentPrice + Math.random() * 5,
        low: currentPrice - Math.random() * 5,
        close: currentPrice,
        volume: Math.random() * 1000000
      })
    }

    return mockData
  }

  private generateTradeId(): string {
    return `backtest_trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
} 