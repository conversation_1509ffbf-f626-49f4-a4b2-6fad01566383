import { TradingEngine } from '../exchanges/TradingEngine'
import { RiskManager, RiskConfig } from './RiskManager'
import { PerformanceMonitor } from './PerformanceMonitor'
import { AlertSystem, AlertConfig } from './AlertSystem'
import { BacktestEngine, BacktestConfig } from './BacktestEngine'
import { PortfolioOptimizer, PortfolioConfig } from './PortfolioOptimizer'
import { ExchangeManager } from '../exchanges/ExchangeManager'

export interface TradingOrchestratorConfig {
  riskConfig: RiskConfig
  alertConfig: AlertConfig
  portfolioConfig: PortfolioConfig
  initialBalance: number
  autoTrading: boolean
  maxPositions: number
  updateInterval: number // milliseconds
}

export interface TradingStatus {
  isRunning: boolean
  totalPositions: number
  totalPnl: number
  dailyPnl: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  alerts: number
  lastUpdate: Date
}

export class TradingOrchestrator {
  private config: TradingOrchestratorConfig
  private tradingEngine: TradingEngine
  private riskManager: RiskManager
  private performanceMonitor: PerformanceMonitor
  private alertSystem: AlertSystem
  private portfolioOptimizer: PortfolioOptimizer
  private exchangeManager: ExchangeManager
  private isRunning: boolean = false
  private updateInterval: NodeJS.Timeout | null = null

  constructor(config: TradingOrchestratorConfig) {
    this.config = config
    this.tradingEngine = new TradingEngine()
    this.riskManager = new RiskManager(config.riskConfig)
    this.performanceMonitor = new PerformanceMonitor(config.initialBalance)
    this.alertSystem = new AlertSystem(config.alertConfig)
    this.portfolioOptimizer = new PortfolioOptimizer(config.portfolioConfig)
    this.exchangeManager = new ExchangeManager()
  }

  /**
   * Start the trading orchestrator
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ Trading orchestrator is already running')
      return
    }

    try {
      console.log('🚀 Starting trading orchestrator...')

      // Initialize all components
      await this.initializeComponents()

      // Start monitoring loop
      this.startMonitoringLoop()

      this.isRunning = true
      console.log('✅ Trading orchestrator started successfully')
    } catch (error) {
      console.error('❌ Failed to start trading orchestrator:', error)
      throw error
    }
  }

  /**
   * Stop the trading orchestrator
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      console.log('⚠️ Trading orchestrator is not running')
      return
    }

    try {
      console.log('🛑 Stopping trading orchestrator...')

      // Stop monitoring loop
      this.stopMonitoringLoop()

      // Close all positions if auto trading is enabled
      if (this.config.autoTrading) {
        await this.tradingEngine.emergencyStop()
      }

      this.isRunning = false
      console.log('✅ Trading orchestrator stopped successfully')
    } catch (error) {
      console.error('❌ Failed to stop trading orchestrator:', error)
      throw error
    }
  }

  /**
   * Initialize all trading components
   */
  private async initializeComponents(): Promise<void> {
    // Initialize exchange manager
    await this.exchangeManager.initializeExchanges()

    // Validate risk configuration
    const riskValidation = this.riskManager.validateConfig()
    if (!riskValidation.valid) {
      throw new Error(`Invalid risk config: ${riskValidation.errors.join(', ')}`)
    }

    // Validate portfolio configuration
    const portfolioValidation = this.portfolioOptimizer.validateConfig()
    if (!portfolioValidation.valid) {
      throw new Error(`Invalid portfolio config: ${portfolioValidation.errors.join(', ')}`)
    }

    // Set up default alerts
    this.setupDefaultAlerts()

    console.log('✅ All components initialized')
  }

  /**
   * Start monitoring loop
   */
  private startMonitoringLoop(): void {
    this.updateInterval = setInterval(async () => {
      try {
        await this.performUpdate()
      } catch (error) {
        console.error('❌ Error in monitoring loop:', error)
      }
    }, this.config.updateInterval)
  }

  /**
   * Stop monitoring loop
   */
  private stopMonitoringLoop(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
    }
  }

  /**
   * Perform regular update
   */
  private async performUpdate(): Promise<void> {
    // Get current market data
    const balances = await this.exchangeManager.getBalances()
    const positions = await this.exchangeManager.getPositions()
    const exchangeStatus = await this.exchangeManager.getExchangeStatus()

    // Update performance monitor
    this.updatePerformanceMonitor(positions)

    // Check risk levels
    this.checkRiskLevels(balances, positions)

    // Check alerts
    this.checkAlerts(balances, positions)

    // Optimize portfolio if needed
    if (this.config.autoTrading) {
      await this.optimizePortfolio(balances, positions)
    }

    console.log(`📊 Update completed at ${new Date().toISOString()}`)
  }

  /**
   * Update performance monitor
   */
  private updatePerformanceMonitor(positions: any[]): void {
    // Update performance metrics based on current positions
    // This is a simplified implementation
    positions.forEach(position => {
      // Update trade records in performance monitor
      // Implementation depends on your specific data structure
    })
  }

  /**
   * Check risk levels
   */
  private checkRiskLevels(balances: any[], positions: any[]): void {
    const portfolioValue = balances.reduce((sum, b) => sum + b.total, 0)
    const totalRisk = positions.reduce((sum, p) => sum + Math.abs(p.pnl), 0)
    const riskPercent = (totalRisk / portfolioValue) * 100

    // Update risk manager
    this.riskManager.updateDailyLoss(totalRisk)

    // Check if risk limits are exceeded
    const riskCheck = this.riskManager.canOpenPosition(
      portfolioValue,
      positions.map(p => ({
        positionId: p.id,
        symbol: p.symbol,
        size: p.size,
        entryPrice: p.entryPrice,
        currentPrice: p.markPrice,
        unrealizedPnl: p.pnl,
        riskPercent: riskPercent,
        stopLossPrice: p.stopLoss || 0,
        takeProfitPrice: p.takeProfit || 0
      })),
      0
    )

    if (!riskCheck.allowed) {
      console.warn(`⚠️ Risk limit exceeded: ${riskCheck.reason}`)
    }
  }

  /**
   * Check alerts
   */
  private checkAlerts(balances: any[], positions: any[]): void {
    // Get current prices
    const currentPrices: Record<string, number> = {}
    positions.forEach(p => {
      currentPrices[p.symbol] = p.markPrice
    })

    // Check price alerts
    this.alertSystem.checkPriceAlerts(currentPrices)

    // Check risk alerts
    const portfolioValue = balances.reduce((sum, b) => sum + b.total, 0)
    const totalRisk = positions.reduce((sum, p) => sum + Math.abs(p.pnl), 0)
    
    this.alertSystem.checkRiskAlerts({
      dailyLoss: totalRisk,
      drawdown: 0, // Calculate from performance monitor
      maxPositionSize: Math.max(...positions.map(p => p.size), 0),
      portfolioRisk: (totalRisk / portfolioValue) * 100
    })

    // Check performance alerts
    const metrics = this.performanceMonitor.calculateMetrics()
    this.alertSystem.checkPerformanceAlerts({
      winRate: metrics.winRate,
      totalPnl: metrics.totalPnl,
      sharpeRatio: metrics.sharpeRatio
    })
  }

  /**
   * Optimize portfolio
   */
  private async optimizePortfolio(balances: any[], positions: any[]): Promise<void> {
    try {
      // Get current allocations
      const currentAllocations: Record<string, number> = {}
      positions.forEach(p => {
        currentAllocations[p.symbol] = p.size * p.markPrice
      })

      // Check if rebalancing is needed
      const rebalanceResult = this.portfolioOptimizer.rebalancePortfolio(currentAllocations)

      if (rebalanceResult.rebalanceNeeded) {
        console.log('🔄 Portfolio rebalancing needed')

        // Execute rebalancing trades
        for (const trade of rebalanceResult.trades) {
          try {
            await this.tradingEngine.executeTrade({
              exchange: 'binance', // Default exchange
              symbol: trade.symbol,
              side: trade.side,
              amount: trade.amount,
              type: 'market'
            })

            console.log(`✅ Rebalancing trade executed: ${trade.side} ${trade.amount} ${trade.symbol}`)
          } catch (error) {
            console.error(`❌ Failed to execute rebalancing trade:`, error)
          }
        }
      }
    } catch (error) {
      console.error('❌ Portfolio optimization failed:', error)
    }
  }

  /**
   * Setup default alerts
   */
  private setupDefaultAlerts(): void {
    // Price alerts
    this.alertSystem.addPriceAlert('BTC/USDT', 'above', 50000)
    this.alertSystem.addPriceAlert('ETH/USDT', 'below', 3000)

    // Risk alerts
    this.alertSystem.addRiskAlert('daily_loss', 1000) // $1000 daily loss limit
    this.alertSystem.addRiskAlert('drawdown', 10) // 10% drawdown limit
    this.alertSystem.addRiskAlert('portfolio_risk', 20) // 20% portfolio risk limit

    // Performance alerts
    this.alertSystem.addPerformanceAlert('win_rate', 60) // 60% win rate target
    this.alertSystem.addPerformanceAlert('pnl', 1000) // $1000 profit target
    this.alertSystem.addPerformanceAlert('sharpe_ratio', 1.5) // 1.5 Sharpe ratio target
  }

  /**
   * Get trading status
   */
  getStatus(): TradingStatus {
    const metrics = this.performanceMonitor.calculateMetrics()
    const alerts = this.alertSystem.getAllAlerts()
    const totalAlerts = alerts.priceAlerts.length + alerts.riskAlerts.length + alerts.performanceAlerts.length

    // Determine risk level
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW'
    if (metrics.maxDrawdown > 20) {
      riskLevel = 'HIGH'
    } else if (metrics.maxDrawdown > 10) {
      riskLevel = 'MEDIUM'
    }

    return {
      isRunning: this.isRunning,
      totalPositions: metrics.totalTrades,
      totalPnl: metrics.totalPnl,
      dailyPnl: 0, // Calculate from performance monitor
      riskLevel,
      alerts: totalAlerts,
      lastUpdate: new Date()
    }
  }

  /**
   * Get performance report
   */
  getPerformanceReport(): string {
    return this.performanceMonitor.generateReport()
  }

  /**
   * Get all alerts
   */
  getAllAlerts() {
    return this.alertSystem.getAllAlerts()
  }

  /**
   * Add price alert
   */
  addPriceAlert(symbol: string, condition: 'above' | 'below', price: number): string {
    return this.alertSystem.addPriceAlert(symbol, condition, price)
  }

  /**
   * Remove alert
   */
  removeAlert(alertId: string): boolean {
    return this.alertSystem.removeAlert(alertId)
  }

  /**
   * Run backtest
   */
  async runBacktest(config: BacktestConfig): Promise<any> {
    const backtestEngine = new BacktestEngine(config)
    await backtestEngine.loadHistoricalData()
    return await backtestEngine.runBacktest()
  }

  /**
   * Get portfolio optimization
   */
  getPortfolioOptimization(): any {
    return this.portfolioOptimizer.optimizePortfolio()
  }

  /**
   * Get efficient frontier
   */
  getEfficientFrontier(): any {
    return this.portfolioOptimizer.getEfficientFrontier()
  }
} 