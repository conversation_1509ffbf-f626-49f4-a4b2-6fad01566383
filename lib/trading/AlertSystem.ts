export interface AlertConfig {
  priceAlerts: boolean
  riskAlerts: boolean
  performanceAlerts: boolean
  emailNotifications: boolean
  pushNotifications: boolean
  webhookUrl?: string
}

export interface PriceAlert {
  id: string
  symbol: string
  condition: 'above' | 'below'
  price: number
  triggered: boolean
  timestamp: Date
}

export interface RiskAlert {
  id: string
  type: 'daily_loss' | 'drawdown' | 'position_size' | 'portfolio_risk'
  threshold: number
  currentValue: number
  triggered: boolean
  timestamp: Date
}

export interface PerformanceAlert {
  id: string
  type: 'win_rate' | 'pnl' | 'sharpe_ratio'
  threshold: number
  currentValue: number
  triggered: boolean
  timestamp: Date
}

export class AlertSystem {
  private config: AlertConfig
  private priceAlerts: Map<string, PriceAlert> = new Map()
  private riskAlerts: Map<string, RiskAlert> = new Map()
  private performanceAlerts: Map<string, PerformanceAlert> = new Map()
  private notificationQueue: any[] = []

  constructor(config: AlertConfig) {
    this.config = config
  }

  /**
   * Add a price alert
   */
  addPriceAlert(symbol: string, condition: 'above' | 'below', price: number): string {
    const alertId = this.generateAlertId('price')
    const alert: PriceAlert = {
      id: alertId,
      symbol,
      condition,
      price,
      triggered: false,
      timestamp: new Date()
    }

    this.priceAlerts.set(alertId, alert)
    return alertId
  }

  /**
   * Add a risk alert
   */
  addRiskAlert(type: RiskAlert['type'], threshold: number): string {
    const alertId = this.generateAlertId('risk')
    const alert: RiskAlert = {
      id: alertId,
      type,
      threshold,
      currentValue: 0,
      triggered: false,
      timestamp: new Date()
    }

    this.riskAlerts.set(alertId, alert)
    return alertId
  }

  /**
   * Add a performance alert
   */
  addPerformanceAlert(type: PerformanceAlert['type'], threshold: number): string {
    const alertId = this.generateAlertId('performance')
    const alert: PerformanceAlert = {
      id: alertId,
      type,
      threshold,
      currentValue: 0,
      triggered: false,
      timestamp: new Date()
    }

    this.performanceAlerts.set(alertId, alert)
    return alertId
  }

  /**
   * Check price alerts
   */
  checkPriceAlerts(currentPrices: Record<string, number>): void {
    this.priceAlerts.forEach((alert, alertId) => {
      if (alert.triggered) return

      const currentPrice = currentPrices[alert.symbol]
      if (!currentPrice) return

      let shouldTrigger = false
      if (alert.condition === 'above' && currentPrice >= alert.price) {
        shouldTrigger = true
      } else if (alert.condition === 'below' && currentPrice <= alert.price) {
        shouldTrigger = true
      }

      if (shouldTrigger) {
        alert.triggered = true
        this.sendNotification({
          type: 'price_alert',
          title: `Price Alert: ${alert.symbol}`,
          message: `${alert.symbol} is ${alert.condition} $${alert.price}`,
          data: alert
        })
      }
    })
  }

  /**
   * Check risk alerts
   */
  checkRiskAlerts(riskData: {
    dailyLoss: number
    drawdown: number
    maxPositionSize: number
    portfolioRisk: number
  }): void {
    this.riskAlerts.forEach((alert, alertId) => {
      if (alert.triggered) return

      let currentValue = 0
      switch (alert.type) {
        case 'daily_loss':
          currentValue = riskData.dailyLoss
          break
        case 'drawdown':
          currentValue = riskData.drawdown
          break
        case 'position_size':
          currentValue = riskData.maxPositionSize
          break
        case 'portfolio_risk':
          currentValue = riskData.portfolioRisk
          break
      }

      alert.currentValue = currentValue

      if (currentValue >= alert.threshold) {
        alert.triggered = true
        this.sendNotification({
          type: 'risk_alert',
          title: `Risk Alert: ${alert.type}`,
          message: `${alert.type} has reached ${currentValue}%`,
          data: alert
        })
      }
    })
  }

  /**
   * Check performance alerts
   */
  checkPerformanceAlerts(performanceData: {
    winRate: number
    totalPnl: number
    sharpeRatio: number
  }): void {
    this.performanceAlerts.forEach((alert, alertId) => {
      if (alert.triggered) return

      let currentValue = 0
      switch (alert.type) {
        case 'win_rate':
          currentValue = performanceData.winRate
          break
        case 'pnl':
          currentValue = performanceData.totalPnl
          break
        case 'sharpe_ratio':
          currentValue = performanceData.sharpeRatio
          break
      }

      alert.currentValue = currentValue

      if (currentValue >= alert.threshold) {
        alert.triggered = true
        this.sendNotification({
          type: 'performance_alert',
          title: `Performance Alert: ${alert.type}`,
          message: `${alert.type} has reached ${currentValue}`,
          data: alert
        })
      }
    })
  }

  /**
   * Send notification
   */
  private async sendNotification(notification: {
    type: string
    title: string
    message: string
    data: any
  }): Promise<void> {
    this.notificationQueue.push(notification)

    try {
      // Send email notification
      if (this.config.emailNotifications) {
        await this.sendEmailNotification(notification)
      }

      // Send push notification
      if (this.config.pushNotifications) {
        await this.sendPushNotification(notification)
      }

      // Send webhook
      if (this.config.webhookUrl) {
        await this.sendWebhookNotification(notification)
      }

      console.log(`🔔 Alert sent: ${notification.title}`)
    } catch (error) {
      console.error('❌ Failed to send notification:', error)
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(notification: any): Promise<void> {
    // Implement email sending logic
    // You can use services like SendGrid, Mailgun, etc.
    console.log(`📧 Email notification: ${notification.title}`)
  }

  /**
   * Send push notification
   */
  private async sendPushNotification(notification: any): Promise<void> {
    // Implement push notification logic
    // You can use services like Firebase, OneSignal, etc.
    console.log(`📱 Push notification: ${notification.title}`)
  }

  /**
   * Send webhook notification
   */
  private async sendWebhookNotification(notification: any): Promise<void> {
    if (!this.config.webhookUrl) return

    try {
      await fetch(this.config.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(notification)
      })
    } catch (error) {
      console.error('❌ Webhook notification failed:', error)
    }
  }

  /**
   * Get all alerts
   */
  getAllAlerts(): {
    priceAlerts: PriceAlert[]
    riskAlerts: RiskAlert[]
    performanceAlerts: PerformanceAlert[]
  } {
    return {
      priceAlerts: Array.from(this.priceAlerts.values()),
      riskAlerts: Array.from(this.riskAlerts.values()),
      performanceAlerts: Array.from(this.performanceAlerts.values())
    }
  }

  /**
   * Remove alert
   */
  removeAlert(alertId: string): boolean {
    if (this.priceAlerts.has(alertId)) {
      this.priceAlerts.delete(alertId)
      return true
    }
    if (this.riskAlerts.has(alertId)) {
      this.riskAlerts.delete(alertId)
      return true
    }
    if (this.performanceAlerts.has(alertId)) {
      this.performanceAlerts.delete(alertId)
      return true
    }
    return false
  }

  /**
   * Clear all alerts
   */
  clearAllAlerts(): void {
    this.priceAlerts.clear()
    this.riskAlerts.clear()
    this.performanceAlerts.clear()
  }

  /**
   * Get notification queue
   */
  getNotificationQueue(): any[] {
    return this.notificationQueue
  }

  /**
   * Clear notification queue
   */
  clearNotificationQueue(): void {
    this.notificationQueue = []
  }

  private generateAlertId(type: string): string {
    return `${type}_alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
} 