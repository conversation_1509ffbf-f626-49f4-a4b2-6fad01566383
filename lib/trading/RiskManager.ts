export interface RiskConfig {
  maxPortfolioRisk: number // Maximum % of portfolio at risk
  maxPositionSize: number // Maximum % of portfolio per position
  maxDailyLoss: number // Maximum daily loss in USD
  stopLossPercent: number // Default stop loss percentage
  takeProfitPercent: number // Default take profit percentage
  maxOpenPositions: number // Maximum number of open positions
}

export interface PositionRisk {
  positionId: string
  symbol: string
  size: number
  entryPrice: number
  currentPrice: number
  unrealizedPnl: number
  riskPercent: number
  stopLossPrice: number
  takeProfitPrice: number
}

export class RiskManager {
  private config: RiskConfig
  private dailyLoss: number = 0
  private dailyLossResetTime: number = Date.now()

  constructor(config: RiskConfig) {
    this.config = config
  }

  /**
   * Calculate position size based on risk management rules
   */
  calculatePositionSize(
    portfolioValue: number,
    entryPrice: number,
    stopLossPrice: number,
    riskAmount: number
  ): number {
    // Calculate risk per share
    const riskPerShare = Math.abs(entryPrice - stopLossPrice)
    
    if (riskPerShare === 0) {
      throw new Error('Stop loss cannot be equal to entry price')
    }

    // Calculate position size based on risk amount
    const positionSize = riskAmount / riskPerShare

    // Apply position size limits
    const maxPositionValue = portfolioValue * (this.config.maxPositionSize / 100)
    const maxPositionSize = maxPositionValue / entryPrice

    return Math.min(positionSize, maxPositionSize)
  }

  /**
   * Check if a new position can be opened
   */
  canOpenPosition(
    portfolioValue: number,
    currentPositions: PositionRisk[],
    newPositionValue: number
  ): { allowed: boolean; reason?: string } {
    // Check daily loss limit
    if (this.dailyLoss >= this.config.maxDailyLoss) {
      return { allowed: false, reason: 'Daily loss limit reached' }
    }

    // Check maximum open positions
    if (currentPositions.length >= this.config.maxOpenPositions) {
      return { allowed: false, reason: 'Maximum open positions reached' }
    }

    // Check portfolio risk limit
    const totalRiskValue = currentPositions.reduce((sum, pos) => sum + pos.size * pos.entryPrice, 0) + newPositionValue
    const portfolioRiskPercent = (totalRiskValue / portfolioValue) * 100

    if (portfolioRiskPercent > this.config.maxPortfolioRisk) {
      return { allowed: false, reason: 'Portfolio risk limit exceeded' }
    }

    return { allowed: true }
  }

  /**
   * Calculate stop loss and take profit prices
   */
  calculateStopLossAndTakeProfit(
    entryPrice: number,
    side: 'buy' | 'sell'
  ): { stopLoss: number; takeProfit: number } {
    const stopLossPercent = this.config.stopLossPercent / 100
    const takeProfitPercent = this.config.takeProfitPercent / 100

    if (side === 'buy') {
      return {
        stopLoss: entryPrice * (1 - stopLossPercent),
        takeProfit: entryPrice * (1 + takeProfitPercent)
      }
    } else {
      return {
        stopLoss: entryPrice * (1 + stopLossPercent),
        takeProfit: entryPrice * (1 - takeProfitPercent)
      }
    }
  }

  /**
   * Update daily loss tracking
   */
  updateDailyLoss(loss: number): void {
    // Reset daily loss if it's a new day
    const now = Date.now()
    const oneDay = 24 * 60 * 60 * 1000
    
    if (now - this.dailyLossResetTime > oneDay) {
      this.dailyLoss = 0
      this.dailyLossResetTime = now
    }

    this.dailyLoss += loss
  }

  /**
   * Check if position should be closed due to risk
   */
  shouldClosePosition(position: PositionRisk): { shouldClose: boolean; reason?: string } {
    // Check stop loss
    if (position.currentPrice <= position.stopLossPrice) {
      return { shouldClose: true, reason: 'Stop loss triggered' }
    }

    // Check take profit
    if (position.currentPrice >= position.takeProfitPrice) {
      return { shouldClose: true, reason: 'Take profit triggered' }
    }

    // Check if position is too risky
    if (position.riskPercent > this.config.maxPortfolioRisk) {
      return { shouldClose: true, reason: 'Position risk too high' }
    }

    return { shouldClose: false }
  }

  /**
   * Get risk summary
   */
  getRiskSummary(positions: PositionRisk[], portfolioValue: number): {
    totalRisk: number
    totalUnrealizedPnl: number
    portfolioRiskPercent: number
    dailyLoss: number
    maxDailyLoss: number
  } {
    const totalRisk = positions.reduce((sum, pos) => sum + (pos.size * pos.entryPrice), 0)
    const totalUnrealizedPnl = positions.reduce((sum, pos) => sum + pos.unrealizedPnl, 0)
    const portfolioRiskPercent = (totalRisk / portfolioValue) * 100

    return {
      totalRisk,
      totalUnrealizedPnl,
      portfolioRiskPercent,
      dailyLoss: this.dailyLoss,
      maxDailyLoss: this.config.maxDailyLoss
    }
  }

  /**
   * Validate risk configuration
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (this.config.maxPortfolioRisk <= 0 || this.config.maxPortfolioRisk > 100) {
      errors.push('maxPortfolioRisk must be between 0 and 100')
    }

    if (this.config.maxPositionSize <= 0 || this.config.maxPositionSize > 100) {
      errors.push('maxPositionSize must be between 0 and 100')
    }

    if (this.config.maxDailyLoss <= 0) {
      errors.push('maxDailyLoss must be greater than 0')
    }

    if (this.config.stopLossPercent <= 0 || this.config.stopLossPercent > 100) {
      errors.push('stopLossPercent must be between 0 and 100')
    }

    if (this.config.takeProfitPercent <= 0 || this.config.takeProfitPercent > 100) {
      errors.push('takeProfitPercent must be between 0 and 100')
    }

    if (this.config.maxOpenPositions <= 0) {
      errors.push('maxOpenPositions must be greater than 0')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
} 