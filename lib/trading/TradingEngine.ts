import { supabase, TradingPosition } from '../supabase'

export interface OrderRequest {
  symbol: string
  side: 'BUY' | 'SELL'
  size: number
  price?: number
  type: 'MARKET' | 'LIMIT' | 'STOP'
  agent_id: string
  organization_id: string
}

export interface OrderResult {
  success: boolean
  order_id?: string
  error?: string
  position?: TradingPosition
}

export class TradingEngine {
  private isRunning: boolean = false
  private autoMode: boolean = false

  constructor() {
    console.log('🤖 Trading Engine initialized')
  }

  /**
   * Start the trading engine
   */
  async start(): Promise<void> {
    this.isRunning = true
    console.log('🚀 Trading Engine started')
    
    // Start monitoring positions
    this.startPositionMonitoring()
  }

  /**
   * Stop the trading engine
   */
  async stop(): Promise<void> {
    this.isRunning = false
    console.log('🛑 Trading Engine stopped')
  }

  /**
   * Enable/disable auto trading mode
   */
  async setAutoMode(enabled: boolean): Promise<void> {
    this.autoMode = enabled
    console.log(`🤖 Auto trading mode ${enabled ? 'enabled' : 'disabled'}`)
    
    // Update environment variable
    process.env.ENABLE_AUTO_MODE = enabled.toString()
  }

  /**
   * Place a new order
   */
  async placeOrder(orderRequest: OrderRequest): Promise<OrderResult> {
    try {
      console.log(`📊 Placing ${orderRequest.side} order for ${orderRequest.symbol}`)

      // Validate order
      const validation = this.validateOrder(orderRequest)
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error
        }
      }

      // Create position record
      const position: Partial<TradingPosition> = {
        agent_id: orderRequest.agent_id,
        exchange: 'binance', // Default exchange
        symbol: orderRequest.symbol,
        side: orderRequest.side,
        size: orderRequest.size,
        entry_price: orderRequest.price || await this.getCurrentPrice(orderRequest.symbol),
        status: 'OPEN',
        organization_id: orderRequest.organization_id
      }

      // Insert position into database
      const { data: newPosition, error } = await supabase
        .from('trading_positions')
        .insert(position)
        .select()
        .single()

      if (error) {
        console.error('❌ Failed to create position:', error)
        return {
          success: false,
          error: error.message
        }
      }

      // Simulate order execution (in production, this would call exchange API)
      await this.simulateOrderExecution(newPosition)

      // Update position status to OPEN
      await supabase
        .from('trading_positions')
        .update({ status: 'OPEN' })
        .eq('id', newPosition.id)

      console.log(`✅ Order placed successfully: ${newPosition.id}`)

      return {
        success: true,
        order_id: newPosition.id,
        position: newPosition as TradingPosition
      }

    } catch (error) {
      console.error('❌ Order placement error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Close a position
   */
  async closePosition(positionId: string, reason: string = 'Manual close'): Promise<OrderResult> {
    try {
      console.log(`🔒 Closing position: ${positionId}`)

      // Get position details
      const { data: position, error: fetchError } = await supabase
        .from('trading_positions')
        .select('*')
        .eq('id', positionId)
        .single()

      if (fetchError || !position) {
        return {
          success: false,
          error: 'Position not found'
        }
      }

      // Calculate P&L
      const currentPrice = await this.getCurrentPrice(position.symbol)
      const pnl = position.side === 'BUY' 
        ? (currentPrice - position.entry_price) * position.size
        : (position.entry_price - currentPrice) * position.size

      const pnlPercentage = (pnl / (position.entry_price * position.size)) * 100

      // Update position
      const { error: updateError } = await supabase
        .from('trading_positions')
        .update({
          status: 'CLOSED',
          current_price: currentPrice,
          pnl: pnl,
          pnl_percentage: pnlPercentage,
          closed_at: new Date().toISOString()
        })
        .eq('id', positionId)

      if (updateError) {
        return {
          success: false,
          error: updateError.message
        }
      }

      // Log the close action
      await supabase
        .from('trading_logs')
        .insert({
          agent_id: position.agent_id,
          log_level: 'INFO',
          message: `Position closed: ${position.symbol} - P&L: $${pnl.toFixed(2)} (${pnlPercentage.toFixed(2)}%)`,
          metadata: { position_id: positionId, reason },
          organization_id: position.organization_id
        })

      console.log(`✅ Position closed: ${positionId} - P&L: $${pnl.toFixed(2)}`)

      return {
        success: true,
        order_id: positionId
      }

    } catch (error) {
      console.error('❌ Close position error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get current price for a symbol
   */
  private async getCurrentPrice(symbol: string): Promise<number> {
    // In production, this would fetch from exchange API
    // For now, return mock prices
    const mockPrices: Record<string, number> = {
      'BTC/USDT': 45000 + Math.random() * 2000,
      'ETH/USDT': 2800 + Math.random() * 200,
      'ADA/USDT': 0.5 + Math.random() * 0.1,
      'SOL/USDT': 100 + Math.random() * 10,
      'DOT/USDT': 7 + Math.random() * 1
    }

    return mockPrices[symbol] || 100
  }

  /**
   * Validate order parameters
   */
  private validateOrder(order: OrderRequest): { valid: boolean; error?: string } {
    if (!order.symbol || !order.side || !order.size) {
      return { valid: false, error: 'Missing required order parameters' }
    }

    if (order.size <= 0) {
      return { valid: false, error: 'Order size must be greater than 0' }
    }

    if (order.type === 'LIMIT' && !order.price) {
      return { valid: false, error: 'Limit orders require a price' }
    }

    return { valid: true }
  }

  /**
   * Simulate order execution (replace with real exchange API calls)
   */
  private async simulateOrderExecution(position: any): Promise<void> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log(`📡 Simulating order execution for ${position.symbol}`)
  }

  /**
   * Start position monitoring
   */
  private startPositionMonitoring(): void {
    setInterval(async () => {
      if (!this.isRunning) return

      try {
        // Get all open positions
        const { data: positions, error } = await supabase
          .from('trading_positions')
          .select('*')
          .eq('status', 'OPEN')

        if (error) {
          console.error('❌ Error fetching positions:', error)
          return
        }

        // Update current prices and P&L
        for (const position of positions || []) {
          const currentPrice = await this.getCurrentPrice(position.symbol)
          const pnl = position.side === 'BUY' 
            ? (currentPrice - position.entry_price) * position.size
            : (position.entry_price - currentPrice) * position.size
          const pnlPercentage = (pnl / (position.entry_price * position.size)) * 100

          // Update position
          await supabase
            .from('trading_positions')
            .update({
              current_price: currentPrice,
              pnl: pnl,
              pnl_percentage: pnlPercentage
            })
            .eq('id', position.id)

          // Check stop-loss (example: -5% loss)
          if (pnlPercentage < -5) {
            console.log(`🛑 Stop-loss triggered for ${position.symbol}: ${pnlPercentage.toFixed(2)}%`)
            await this.closePosition(position.id, 'Stop-loss triggered')
          }
        }
      } catch (error) {
        console.error('❌ Position monitoring error:', error)
      }
    }, 30000) // Check every 30 seconds
  }

  /**
   * Get engine status
   */
  getStatus(): { running: boolean; autoMode: boolean } {
    return {
      running: this.isRunning,
      autoMode: this.autoMode
    }
  }
}

// Export singleton instance
export const tradingEngine = new TradingEngine() 