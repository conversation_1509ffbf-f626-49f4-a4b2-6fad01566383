export interface Asset {
  symbol: string
  weight: number
  expectedReturn: number
  volatility: number
  correlation: Record<string, number>
}

export interface PortfolioConfig {
  targetReturn: number
  maxRisk: number
  rebalanceThreshold: number
  maxAllocation: number
  minAllocation: number
}

export interface OptimizationResult {
  weights: Record<string, number>
  expectedReturn: number
  expectedRisk: number
  sharpeRatio: number
  allocations: Record<string, number>
}

export class PortfolioOptimizer {
  private assets: Asset[] = []
  private config: PortfolioConfig

  constructor(config: PortfolioConfig) {
    this.config = config
  }

  /**
   * Add asset to portfolio
   */
  addAsset(asset: Asset): void {
    this.assets.push(asset)
  }

  /**
   * Remove asset from portfolio
   */
  removeAsset(symbol: string): void {
    this.assets = this.assets.filter(a => a.symbol !== symbol)
  }

  /**
   * Optimize portfolio using Modern Portfolio Theory
   */
  optimizePortfolio(): OptimizationResult {
    if (this.assets.length === 0) {
      throw new Error('No assets in portfolio')
    }

    // Calculate covariance matrix
    const covarianceMatrix = this.calculateCovarianceMatrix()

    // Find optimal weights using efficient frontier
    const weights = this.findOptimalWeights(covarianceMatrix)

    // Calculate portfolio metrics
    const expectedReturn = this.calculateExpectedReturn(weights)
    const expectedRisk = this.calculateExpectedRisk(weights, covarianceMatrix)
    const sharpeRatio = expectedRisk > 0 ? expectedReturn / expectedRisk : 0

    return {
      weights,
      expectedReturn,
      expectedRisk,
      sharpeRatio,
      allocations: this.calculateAllocations(weights)
    }
  }

  /**
   * Rebalance portfolio
   */
  rebalancePortfolio(currentAllocations: Record<string, number>): {
    trades: Array<{ symbol: string; side: 'buy' | 'sell'; amount: number }>
    rebalanceNeeded: boolean
  } {
    const optimalResult = this.optimizePortfolio()
    const trades: Array<{ symbol: string; side: 'buy' | 'sell'; amount: number }> = []
    let rebalanceNeeded = false

    // Check if rebalancing is needed
    for (const [symbol, currentWeight] of Object.entries(currentAllocations)) {
      const optimalWeight = optimalResult.weights[symbol] || 0
      const difference = Math.abs(optimalWeight - currentWeight)

      if (difference > this.config.rebalanceThreshold) {
        rebalanceNeeded = true
        break
      }
    }

    if (rebalanceNeeded) {
      // Calculate required trades
      for (const [symbol, currentWeight] of Object.entries(currentAllocations)) {
        const optimalWeight = optimalResult.weights[symbol] || 0
        const difference = optimalWeight - currentWeight

        if (Math.abs(difference) > this.config.rebalanceThreshold) {
          trades.push({
            symbol,
            side: difference > 0 ? 'buy' : 'sell',
            amount: Math.abs(difference)
          })
        }
      }
    }

    return { trades, rebalanceNeeded }
  }

  /**
   * Calculate covariance matrix
   */
  private calculateCovarianceMatrix(): number[][] {
    const n = this.assets.length
    const covarianceMatrix: number[][] = Array(n).fill(0).map(() => Array(n).fill(0))

    for (let i = 0; i < n; i++) {
      for (let j = 0; j < n; j++) {
        if (i === j) {
          // Variance
          covarianceMatrix[i][j] = Math.pow(this.assets[i].volatility, 2)
        } else {
          // Covariance
          const correlation = this.assets[i].correlation[this.assets[j].symbol] || 0
          covarianceMatrix[i][j] = correlation * this.assets[i].volatility * this.assets[j].volatility
        }
      }
    }

    return covarianceMatrix
  }

  /**
   * Find optimal weights using efficient frontier
   */
  private findOptimalWeights(covarianceMatrix: number[][]): Record<string, number> {
    const n = this.assets.length
    const weights: Record<string, number> = {}

    // Simple equal weight allocation as starting point
    // In a real implementation, you would use more sophisticated optimization algorithms
    const equalWeight = 1 / n

    for (let i = 0; i < n; i++) {
      const symbol = this.assets[i].symbol
      let weight = equalWeight

      // Apply constraints
      weight = Math.max(weight, this.config.minAllocation)
      weight = Math.min(weight, this.config.maxAllocation)

      weights[symbol] = weight
    }

    // Normalize weights to sum to 1
    const totalWeight = Object.values(weights).reduce((sum, w) => sum + w, 0)
    for (const symbol in weights) {
      weights[symbol] /= totalWeight
    }

    return weights
  }

  /**
   * Calculate expected return
   */
  private calculateExpectedReturn(weights: Record<string, number>): number {
    let expectedReturn = 0

    for (const asset of this.assets) {
      expectedReturn += weights[asset.symbol] * asset.expectedReturn
    }

    return expectedReturn
  }

  /**
   * Calculate expected risk (standard deviation)
   */
  private calculateExpectedRisk(weights: Record<string, number>, covarianceMatrix: number[][]): number {
    const n = this.assets.length
    let portfolioVariance = 0

    for (let i = 0; i < n; i++) {
      for (let j = 0; j < n; j++) {
        const weightI = weights[this.assets[i].symbol]
        const weightJ = weights[this.assets[j].symbol]
        portfolioVariance += weightI * weightJ * covarianceMatrix[i][j]
      }
    }

    return Math.sqrt(portfolioVariance)
  }

  /**
   * Calculate allocations in currency terms
   */
  private calculateAllocations(weights: Record<string, number>): Record<string, number> {
    const allocations: Record<string, number> = {}

    for (const [symbol, weight] of Object.entries(weights)) {
      allocations[symbol] = weight * 100 // Convert to percentage
    }

    return allocations
  }

  /**
   * Get efficient frontier
   */
  getEfficientFrontier(): Array<{ return: number; risk: number; weights: Record<string, number> }> {
    const frontier: Array<{ return: number; risk: number; weights: Record<string, number> }> = []
    const covarianceMatrix = this.calculateCovarianceMatrix()

    // Generate different risk levels
    const riskLevels = Array.from({ length: 20 }, (_, i) => i * 0.01 + 0.01)

    for (const targetRisk of riskLevels) {
      try {
        // This is a simplified version - in practice, you'd use quadratic programming
        const weights = this.findOptimalWeightsForRisk(targetRisk, covarianceMatrix)
        const expectedReturn = this.calculateExpectedReturn(weights)
        const actualRisk = this.calculateExpectedRisk(weights, covarianceMatrix)

        frontier.push({
          return: expectedReturn,
          risk: actualRisk,
          weights
        })
      } catch (error) {
        // Skip invalid risk levels
        continue
      }
    }

    return frontier.sort((a, b) => a.risk - b.risk)
  }

  /**
   * Find optimal weights for a given risk level
   */
  private findOptimalWeightsForRisk(targetRisk: number, covarianceMatrix: number[][]): Record<string, number> {
    // Simplified implementation - in practice, use quadratic programming
    const weights: Record<string, number> = {}

    // Equal weight allocation as fallback
    const equalWeight = 1 / this.assets.length
    for (const asset of this.assets) {
      weights[asset.symbol] = equalWeight
    }

    return weights
  }

  /**
   * Get portfolio statistics
   */
  getPortfolioStats(): {
    totalAssets: number
    averageReturn: number
    averageVolatility: number
    diversificationScore: number
  } {
    if (this.assets.length === 0) {
      return {
        totalAssets: 0,
        averageReturn: 0,
        averageVolatility: 0,
        diversificationScore: 0
      }
    }

    const totalAssets = this.assets.length
    const averageReturn = this.assets.reduce((sum, a) => sum + a.expectedReturn, 0) / totalAssets
    const averageVolatility = this.assets.reduce((sum, a) => sum + a.volatility, 0) / totalAssets

    // Calculate diversification score (1 - average correlation)
    let totalCorrelation = 0
    let correlationCount = 0

    for (let i = 0; i < this.assets.length; i++) {
      for (let j = i + 1; j < this.assets.length; j++) {
        const correlation = this.assets[i].correlation[this.assets[j].symbol] || 0
        totalCorrelation += Math.abs(correlation)
        correlationCount++
      }
    }

    const averageCorrelation = correlationCount > 0 ? totalCorrelation / correlationCount : 0
    const diversificationScore = 1 - averageCorrelation

    return {
      totalAssets,
      averageReturn,
      averageVolatility,
      diversificationScore
    }
  }

  /**
   * Validate portfolio configuration
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (this.config.targetReturn < 0 || this.config.targetReturn > 1) {
      errors.push('targetReturn must be between 0 and 1')
    }

    if (this.config.maxRisk < 0 || this.config.maxRisk > 1) {
      errors.push('maxRisk must be between 0 and 1')
    }

    if (this.config.rebalanceThreshold < 0 || this.config.rebalanceThreshold > 1) {
      errors.push('rebalanceThreshold must be between 0 and 1')
    }

    if (this.config.maxAllocation < 0 || this.config.maxAllocation > 1) {
      errors.push('maxAllocation must be between 0 and 1')
    }

    if (this.config.minAllocation < 0 || this.config.minAllocation > 1) {
      errors.push('minAllocation must be between 0 and 1')
    }

    if (this.config.minAllocation > this.config.maxAllocation) {
      errors.push('minAllocation cannot be greater than maxAllocation')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
} 