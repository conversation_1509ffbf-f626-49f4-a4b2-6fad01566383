export interface TradeRecord {
  id: string
  symbol: string
  side: 'buy' | 'sell'
  size: number
  entryPrice: number
  exitPrice?: number
  pnl?: number
  pnlPercent?: number
  timestamp: Date
  exchange: string
  agent: string
  status: 'open' | 'closed' | 'cancelled'
}

export interface PerformanceMetrics {
  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number
  totalPnl: number
  totalPnlPercent: number
  averageWin: number
  averageLoss: number
  profitFactor: number
  maxDrawdown: number
  sharpeRatio: number
  bestTrade: number
  worstTrade: number
}

export interface AgentPerformance {
  agentId: string
  totalTrades: number
  winRate: number
  totalPnl: number
  averagePnl: number
  bestSymbol: string
  worstSymbol: string
}

export class PerformanceMonitor {
  private trades: TradeRecord[] = []
  private startDate: Date = new Date()
  private initialBalance: number = 0

  constructor(initialBalance: number) {
    this.initialBalance = initialBalance
  }

  /**
   * Record a new trade
   */
  recordTrade(trade: Omit<TradeRecord, 'id' | 'timestamp'>): string {
    const tradeRecord: TradeRecord = {
      ...trade,
      id: this.generateTradeId(),
      timestamp: new Date()
    }

    this.trades.push(tradeRecord)
    return tradeRecord.id
  }

  /**
   * Update trade with exit information
   */
  closeTrade(tradeId: string, exitPrice: number, pnl: number): void {
    const trade = this.trades.find(t => t.id === tradeId)
    if (!trade) {
      throw new Error(`Trade ${tradeId} not found`)
    }

    trade.exitPrice = exitPrice
    trade.pnl = pnl
    trade.pnlPercent = (pnl / (trade.size * trade.entryPrice)) * 100
    trade.status = 'closed'
  }

  /**
   * Calculate performance metrics
   */
  calculateMetrics(): PerformanceMetrics {
    const closedTrades = this.trades.filter(t => t.status === 'closed')
    const winningTrades = closedTrades.filter(t => (t.pnl || 0) > 0)
    const losingTrades = closedTrades.filter(t => (t.pnl || 0) < 0)

    const totalPnl = closedTrades.reduce((sum, t) => sum + (t.pnl || 0), 0)
    const totalPnlPercent = (totalPnl / this.initialBalance) * 100

    const averageWin = winningTrades.length > 0 
      ? winningTrades.reduce((sum, t) => sum + (t.pnl || 0), 0) / winningTrades.length 
      : 0

    const averageLoss = losingTrades.length > 0 
      ? losingTrades.reduce((sum, t) => sum + (t.pnl || 0), 0) / losingTrades.length 
      : 0

    const profitFactor = averageLoss !== 0 ? Math.abs(averageWin / averageLoss) : 0

    return {
      totalTrades: closedTrades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: closedTrades.length > 0 ? (winningTrades.length / closedTrades.length) * 100 : 0,
      totalPnl,
      totalPnlPercent,
      averageWin,
      averageLoss,
      profitFactor,
      maxDrawdown: this.calculateMaxDrawdown(),
      sharpeRatio: this.calculateSharpeRatio(),
      bestTrade: Math.max(...closedTrades.map(t => t.pnl || 0)),
      worstTrade: Math.min(...closedTrades.map(t => t.pnl || 0))
    }
  }

  /**
   * Calculate agent performance
   */
  calculateAgentPerformance(): AgentPerformance[] {
    const agentGroups = new Map<string, TradeRecord[]>()
    
    this.trades.forEach(trade => {
      if (!agentGroups.has(trade.agent)) {
        agentGroups.set(trade.agent, [])
      }
      agentGroups.get(trade.agent)!.push(trade)
    })

    return Array.from(agentGroups.entries()).map(([agentId, trades]) => {
      const closedTrades = trades.filter(t => t.status === 'closed')
      const winningTrades = closedTrades.filter(t => (t.pnl || 0) > 0)
      const totalPnl = closedTrades.reduce((sum, t) => sum + (t.pnl || 0), 0)

      // Find best and worst symbols
      const symbolPerformance = new Map<string, number>()
      closedTrades.forEach(trade => {
        const current = symbolPerformance.get(trade.symbol) || 0
        symbolPerformance.set(trade.symbol, current + (trade.pnl || 0))
      })

      const bestSymbol = Array.from(symbolPerformance.entries())
        .sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A'
      
      const worstSymbol = Array.from(symbolPerformance.entries())
        .sort(([,a], [,b]) => a - b)[0]?.[0] || 'N/A'

      return {
        agentId,
        totalTrades: closedTrades.length,
        winRate: closedTrades.length > 0 ? (winningTrades.length / closedTrades.length) * 100 : 0,
        totalPnl,
        averagePnl: closedTrades.length > 0 ? totalPnl / closedTrades.length : 0,
        bestSymbol,
        worstSymbol
      }
    })
  }

  /**
   * Calculate maximum drawdown
   */
  private calculateMaxDrawdown(): number {
    let maxDrawdown = 0
    let peak = this.initialBalance
    let currentBalance = this.initialBalance

    this.trades
      .filter(t => t.status === 'closed')
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
      .forEach(trade => {
        currentBalance += trade.pnl || 0
        
        if (currentBalance > peak) {
          peak = currentBalance
        }

        const drawdown = (peak - currentBalance) / peak * 100
        if (drawdown > maxDrawdown) {
          maxDrawdown = drawdown
        }
      })

    return maxDrawdown
  }

  /**
   * Calculate Sharpe ratio
   */
  private calculateSharpeRatio(): number {
    const returns = this.trades
      .filter(t => t.status === 'closed')
      .map(t => (t.pnl || 0) / (t.size * t.entryPrice))

    if (returns.length === 0) return 0

    const meanReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - meanReturn, 2), 0) / returns.length
    const stdDev = Math.sqrt(variance)

    return stdDev !== 0 ? meanReturn / stdDev : 0
  }

  /**
   * Generate performance report
   */
  generateReport(): string {
    const metrics = this.calculateMetrics()
    const agentPerformance = this.calculateAgentPerformance()

    return `
📊 PERFORMANCE REPORT
====================

📈 Overall Performance:
- Total Trades: ${metrics.totalTrades}
- Win Rate: ${metrics.winRate.toFixed(2)}%
- Total P&L: $${metrics.totalPnl.toFixed(2)} (${metrics.totalPnlPercent.toFixed(2)}%)
- Profit Factor: ${metrics.profitFactor.toFixed(2)}
- Max Drawdown: ${metrics.maxDrawdown.toFixed(2)}%
- Sharpe Ratio: ${metrics.sharpeRatio.toFixed(2)}

💰 P&L Analysis:
- Average Win: $${metrics.averageWin.toFixed(2)}
- Average Loss: $${metrics.averageLoss.toFixed(2)}
- Best Trade: $${metrics.bestTrade.toFixed(2)}
- Worst Trade: $${metrics.worstTrade.toFixed(2)}

🤖 Agent Performance:
${agentPerformance.map(agent => `
${agent.agentId}:
  - Trades: ${agent.totalTrades}
  - Win Rate: ${agent.winRate.toFixed(2)}%
  - Total P&L: $${agent.totalPnl.toFixed(2)}
  - Best Symbol: ${agent.bestSymbol}
  - Worst Symbol: ${agent.worstSymbol}
`).join('')}

📅 Period: ${this.startDate.toLocaleDateString()} - ${new Date().toLocaleDateString()}
    `.trim()
  }

  /**
   * Get recent trades
   */
  getRecentTrades(limit: number = 10): TradeRecord[] {
    return this.trades
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit)
  }

  /**
   * Get trades by symbol
   */
  getTradesBySymbol(symbol: string): TradeRecord[] {
    return this.trades.filter(t => t.symbol === symbol)
  }

  /**
   * Get trades by agent
   */
  getTradesByAgent(agentId: string): TradeRecord[] {
    return this.trades.filter(t => t.agent === agentId)
  }

  private generateTradeId(): string {
    return `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
} 