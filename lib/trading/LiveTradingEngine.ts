import * as ccxt from 'ccxt'
import { exchangeConfigManager } from '../exchanges/ExchangeConfigManager'
import { aiConfigManager } from '../ai/AIConfigManager'
import { TechnicalAnalysisAgent } from '../agents/TechnicalAnalysisAgent'
import { SentimentAnalysisAgentV3 } from '../agents/SentimentAnalysisAgentV3'

export interface TradingSignal {
  symbol: string
  action: 'BUY' | 'SELL' | 'HOLD'
  confidence: number
  price: number
  quantity: number
  stopLoss?: number
  takeProfit?: number
  reasoning: string
  timestamp: string
  source: 'AI' | 'MANUAL' | 'STRATEGY'
}

export interface Order {
  id: string
  exchangeId: string
  symbol: string
  side: 'buy' | 'sell'
  type: 'market' | 'limit' | 'stop' | 'stop_limit'
  amount: number
  price?: number
  stopPrice?: number
  status: 'pending' | 'open' | 'closed' | 'canceled' | 'failed'
  filled: number
  remaining: number
  cost: number
  fee?: number
  timestamp: string
  lastUpdate: string
  exchangeOrderId?: string
  clientOrderId?: string
}

export interface Position {
  symbol: string
  exchangeId: string
  side: 'long' | 'short'
  size: number
  entryPrice: number
  currentPrice: number
  unrealizedPnl: number
  realizedPnl: number
  percentage: number
  timestamp: string
}

export interface TradingStats {
  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number
  totalPnl: number
  totalVolume: number
  averageWin: number
  averageLoss: number
  profitFactor: number
  sharpeRatio: number
  maxDrawdown: number
}

export class LiveTradingEngine {
  private exchanges: Map<string, ccxt.Exchange> = new Map()
  private activeOrders: Map<string, Order> = new Map()
  private positions: Map<string, Position> = new Map()
  private tradingSignals: TradingSignal[] = []
  private isRunning: boolean = false
  private technicalAgent: TechnicalAnalysisAgent
  private sentimentAgent: SentimentAnalysisAgentV3
  private stats: TradingStats = {
    totalTrades: 0,
    winningTrades: 0,
    losingTrades: 0,
    winRate: 0,
    totalPnl: 0,
    totalVolume: 0,
    averageWin: 0,
    averageLoss: 0,
    profitFactor: 0,
    sharpeRatio: 0,
    maxDrawdown: 0
  }

  constructor() {
    this.technicalAgent = new TechnicalAnalysisAgent({
      id: 'live-technical',
      name: 'Live Technical Analysis',
      specialization: 'Real-time technical analysis for live trading',
      aiProvider: 'openrouter',
      aiModel: 'anthropic/claude-3.5-sonnet'
    })

    this.sentimentAgent = new SentimentAnalysisAgentV3({
      id: 'live-sentiment',
      name: 'Live Sentiment Analysis',
      specialization: 'Real-time sentiment analysis for live trading',
      aiProvider: 'openrouter',
      aiModel: 'openai/gpt-4o'
    })

    this.initializeExchanges()
  }

  private async initializeExchanges() {
    const enabledExchanges = exchangeConfigManager.getEnabledExchanges()
    
    for (const exchangeConfig of enabledExchanges) {
      if (exchangeConfig.credentials) {
        try {
          const exchangeClass = ccxt[exchangeConfig.name as keyof typeof ccxt] as any
          if (exchangeClass) {
            const exchange = new exchangeClass({
              apiKey: exchangeConfig.credentials.apiKey,
              secret: exchangeConfig.credentials.apiSecret,
              passphrase: exchangeConfig.credentials.passphrase,
              sandbox: exchangeConfig.testMode,
              enableRateLimit: true
            })

            await exchange.loadMarkets()
            this.exchanges.set(exchangeConfig.id, exchange)
            console.log(`✅ Initialized exchange: ${exchangeConfig.displayName}`)
          }
        } catch (error) {
          console.error(`❌ Failed to initialize ${exchangeConfig.displayName}:`, error)
        }
      }
    }
  }

  // Start the trading engine
  async start() {
    if (this.isRunning) {
      throw new Error('Trading engine is already running')
    }

    console.log('🚀 Starting Live Trading Engine...')
    this.isRunning = true

    // Start monitoring and signal generation
    this.startSignalGeneration()
    this.startOrderMonitoring()
    
    console.log('✅ Live Trading Engine started')
  }

  // Stop the trading engine
  async stop() {
    console.log('🛑 Stopping Live Trading Engine...')
    this.isRunning = false
    
    // Cancel all pending orders
    await this.cancelAllOrders()
    
    console.log('✅ Live Trading Engine stopped')
  }

  // Generate trading signals using AI agents
  private async startSignalGeneration() {
    const generateSignals = async () => {
      if (!this.isRunning) return

      try {
        const symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT'] // Configurable symbols
        
        for (const symbol of symbols) {
          await this.generateSignalForSymbol(symbol)
        }
      } catch (error) {
        console.error('❌ Error generating signals:', error)
      }

      // Schedule next signal generation
      if (this.isRunning) {
        setTimeout(generateSignals, 60000) // Every minute
      }
    }

    generateSignals()
  }

  private async generateSignalForSymbol(symbol: string) {
    try {
      // Get market data from the first available exchange
      const exchange = Array.from(this.exchanges.values())[0]
      if (!exchange) return

      const ticker = await exchange.fetchTicker(symbol)
      const ohlcv = await exchange.fetchOHLCV(symbol, '1h', undefined, 100)
      
      // Prepare market data for technical analysis
      const marketData = {
        symbol,
        currentPrice: ticker.last || 0,
        volume: ticker.baseVolume || 0,
        ohlcv,
        indicators: {
          rsi: this.calculateRSI(ohlcv),
          macd: this.calculateMACD(ohlcv),
          bollinger: this.calculateBollinger(ohlcv),
          movingAverages: this.calculateMovingAverages(ohlcv)
        }
      }

      // Get AI analysis
      const [technicalAnalysis, sentimentAnalysis] = await Promise.all([
        this.technicalAgent.analyze(marketData),
        this.sentimentAgent.analyze(symbol)
      ])

      // Combine analyses to generate trading signal
      const signal = this.combineAnalyses(symbol, technicalAnalysis, sentimentAnalysis, ticker.last || 0)
      
      if (signal && signal.action !== 'HOLD') {
        this.tradingSignals.push(signal)
        console.log(`📊 Generated signal for ${symbol}: ${signal.action} (${signal.confidence}% confidence)`)
        
        // Execute signal if auto-trading is enabled
        await this.executeSignal(signal)
      }

    } catch (error) {
      console.error(`❌ Error generating signal for ${symbol}:`, error)
    }
  }

  private combineAnalyses(symbol: string, technical: any, sentiment: any, currentPrice: number): TradingSignal | null {
    // Combine technical and sentiment analysis
    const technicalScore = this.getScoreFromRecommendation(technical.recommendation)
    const sentimentScore = sentiment.data?.sentimentScore || 0
    
    // Weight the scores (70% technical, 30% sentiment)
    const combinedScore = (technicalScore * 0.7) + (sentimentScore * 0.3)
    const confidence = Math.min(technical.confidence, sentiment.confidence || 50)

    let action: 'BUY' | 'SELL' | 'HOLD' = 'HOLD'
    
    if (combinedScore > 20 && confidence > 60) {
      action = 'BUY'
    } else if (combinedScore < -20 && confidence > 60) {
      action = 'SELL'
    }

    if (action === 'HOLD') return null

    return {
      symbol,
      action,
      confidence,
      price: currentPrice,
      quantity: this.calculatePositionSize(symbol, currentPrice),
      stopLoss: technical.data?.stopLoss,
      takeProfit: technical.data?.takeProfit,
      reasoning: `Technical: ${technical.reasoning}. Sentiment: ${sentiment.reasoning}`,
      timestamp: new Date().toISOString(),
      source: 'AI'
    }
  }

  private getScoreFromRecommendation(recommendation: string): number {
    const scores: Record<string, number> = {
      'STRONG_BUY': 50,
      'BUY': 25,
      'NEUTRAL': 0,
      'SELL': -25,
      'STRONG_SELL': -50
    }
    return scores[recommendation] || 0
  }

  private calculatePositionSize(symbol: string, price: number): number {
    // Get trading settings for the exchange
    const settings = exchangeConfigManager.getTradingSettings('binance') // Default to binance
    if (!settings) return 0

    // Calculate position size based on risk management
    const maxPositionValue = (settings.maxPositionSize / 100) * 10000 // Assume $10k portfolio
    return Math.min(maxPositionValue / price, settings.maxOrderSize / price)
  }

  // Execute a trading signal
  async executeSignal(signal: TradingSignal) {
    try {
      // Check if paper trading is enabled
      const settings = exchangeConfigManager.getTradingSettings('binance')
      if (settings?.enablePaperTrading) {
        console.log(`📝 Paper trade: ${signal.action} ${signal.quantity} ${signal.symbol} at ${signal.price}`)
        return
      }

      // Find best exchange for this symbol
      const exchange = await this.findBestExchange(signal.symbol)
      if (!exchange) {
        console.log(`❌ No exchange available for ${signal.symbol}`)
        return
      }

      // Create order
      const order = await this.createOrder(exchange.id, signal)
      if (order) {
        this.activeOrders.set(order.id, order)
        console.log(`✅ Order placed: ${order.id}`)
      }

    } catch (error) {
      console.error('❌ Error executing signal:', error)
    }
  }

  private async findBestExchange(symbol: string): Promise<{ id: string, exchange: ccxt.Exchange } | null> {
    // Find exchange with best liquidity/price for the symbol
    for (const [id, exchange] of this.exchanges) {
      try {
        const ticker = await exchange.fetchTicker(symbol)
        if (ticker && ticker.bid && ticker.ask) {
          return { id, exchange }
        }
      } catch (error) {
        continue
      }
    }
    return null
  }

  private async createOrder(exchangeId: string, signal: TradingSignal): Promise<Order | null> {
    const exchange = this.exchanges.get(exchangeId)
    if (!exchange) return null

    try {
      const settings = exchangeConfigManager.getTradingSettings(exchangeId)
      const orderType = settings?.defaultOrderType || 'limit'
      
      const orderParams: any = {
        symbol: signal.symbol,
        type: orderType,
        side: signal.action.toLowerCase(),
        amount: signal.quantity
      }

      if (orderType === 'limit') {
        orderParams.price = signal.price
      }

      const result = await exchange.createOrder(
        signal.symbol,
        orderType,
        signal.action.toLowerCase(),
        signal.quantity,
        orderType === 'limit' ? signal.price : undefined
      )

      const order: Order = {
        id: `${exchangeId}-${Date.now()}`,
        exchangeId,
        symbol: signal.symbol,
        side: signal.action.toLowerCase() as 'buy' | 'sell',
        type: orderType as any,
        amount: signal.quantity,
        price: signal.price,
        status: 'pending',
        filled: 0,
        remaining: signal.quantity,
        cost: 0,
        timestamp: new Date().toISOString(),
        lastUpdate: new Date().toISOString(),
        exchangeOrderId: result.id,
        clientOrderId: result.clientOrderId
      }

      return order
    } catch (error) {
      console.error('❌ Error creating order:', error)
      return null
    }
  }

  // Monitor active orders
  private async startOrderMonitoring() {
    const monitorOrders = async () => {
      if (!this.isRunning) return

      for (const [orderId, order] of this.activeOrders) {
        await this.updateOrderStatus(order)
      }

      if (this.isRunning) {
        setTimeout(monitorOrders, 10000) // Every 10 seconds
      }
    }

    monitorOrders()
  }

  private async updateOrderStatus(order: Order) {
    try {
      const exchange = this.exchanges.get(order.exchangeId)
      if (!exchange || !order.exchangeOrderId) return

      const updatedOrder = await exchange.fetchOrder(order.exchangeOrderId, order.symbol)
      
      order.status = updatedOrder.status as any
      order.filled = updatedOrder.filled || 0
      order.remaining = updatedOrder.remaining || 0
      order.cost = updatedOrder.cost || 0
      order.fee = updatedOrder.fee?.cost
      order.lastUpdate = new Date().toISOString()

      if (order.status === 'closed') {
        this.activeOrders.delete(order.id)
        this.updateStats(order)
        console.log(`✅ Order completed: ${order.id}`)
      }

    } catch (error) {
      console.error('❌ Error updating order status:', error)
    }
  }

  private async cancelAllOrders() {
    for (const [orderId, order] of this.activeOrders) {
      try {
        const exchange = this.exchanges.get(order.exchangeId)
        if (exchange && order.exchangeOrderId) {
          await exchange.cancelOrder(order.exchangeOrderId, order.symbol)
          console.log(`❌ Cancelled order: ${orderId}`)
        }
      } catch (error) {
        console.error('❌ Error cancelling order:', error)
      }
    }
    this.activeOrders.clear()
  }

  private updateStats(order: Order) {
    this.stats.totalTrades++
    this.stats.totalVolume += order.cost

    // Calculate PnL (simplified)
    const pnl = order.side === 'buy' ? 0 : order.cost // Would need more complex calculation
    this.stats.totalPnl += pnl

    if (pnl > 0) {
      this.stats.winningTrades++
    } else if (pnl < 0) {
      this.stats.losingTrades++
    }

    this.stats.winRate = (this.stats.winningTrades / this.stats.totalTrades) * 100
  }

  // Helper methods for technical indicators (simplified)
  private calculateRSI(ohlcv: number[][]): number {
    // Simplified RSI calculation
    return 50 + Math.random() * 40 - 20 // Mock implementation
  }

  private calculateMACD(ohlcv: number[][]): any {
    return { macd: 0, signal: 0, histogram: 0 } // Mock implementation
  }

  private calculateBollinger(ohlcv: number[][]): any {
    const closes = ohlcv.map(candle => candle[4])
    const latest = closes[closes.length - 1]
    return { upper: latest * 1.02, middle: latest, lower: latest * 0.98 }
  }

  private calculateMovingAverages(ohlcv: number[][]): any {
    const closes = ohlcv.map(candle => candle[4])
    const latest = closes[closes.length - 1]
    return { sma20: latest, sma50: latest, ema12: latest, ema26: latest }
  }

  // Public getters
  getActiveOrders(): Order[] {
    return Array.from(this.activeOrders.values())
  }

  getPositions(): Position[] {
    return Array.from(this.positions.values())
  }

  getRecentSignals(limit: number = 10): TradingSignal[] {
    return this.tradingSignals.slice(-limit)
  }

  getStats(): TradingStats {
    return { ...this.stats }
  }

  isEngineRunning(): boolean {
    return this.isRunning
  }

  // Manual trading methods
  async placeManualOrder(
    exchangeId: string,
    symbol: string,
    side: 'buy' | 'sell',
    amount: number,
    price?: number,
    type: 'market' | 'limit' = 'limit'
  ): Promise<Order | null> {
    const signal: TradingSignal = {
      symbol,
      action: side.toUpperCase() as 'BUY' | 'SELL',
      confidence: 100,
      price: price || 0,
      quantity: amount,
      reasoning: 'Manual order',
      timestamp: new Date().toISOString(),
      source: 'MANUAL'
    }

    return await this.createOrder(exchangeId, signal)
  }

  async cancelOrder(orderId: string): Promise<boolean> {
    const order = this.activeOrders.get(orderId)
    if (!order) return false

    try {
      const exchange = this.exchanges.get(order.exchangeId)
      if (exchange && order.exchangeOrderId) {
        await exchange.cancelOrder(order.exchangeOrderId, order.symbol)
        this.activeOrders.delete(orderId)
        return true
      }
    } catch (error) {
      console.error('❌ Error cancelling order:', error)
    }
    return false
  }

  // Portfolio methods
  async getPortfolioBalance(): Promise<Record<string, number>> {
    const totalBalance: Record<string, number> = {}

    for (const [exchangeId, exchange] of this.exchanges) {
      try {
        const balance = await exchange.fetchBalance()
        for (const [currency, amount] of Object.entries(balance.total || {})) {
          totalBalance[currency] = (totalBalance[currency] || 0) + (amount as number)
        }
      } catch (error) {
        console.error(`❌ Error fetching balance from ${exchangeId}:`, error)
      }
    }

    return totalBalance
  }
}

// Singleton instance
export const liveTradingEngine = new LiveTradingEngine()
