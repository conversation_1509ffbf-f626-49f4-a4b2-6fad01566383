import crypto from 'crypto'

export interface ExchangeConfig {
  id: string
  name: string
  apiKey: string
  secretKey: string
  passphrase?: string // Voor KuCoin
  sandbox: boolean
  testnet: boolean
  rateLimit?: number // Requests per minute
  maxOrderSize?: number // Maximum order size in USD
}

// Encryption key for API keys (in production, use environment variable)
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-secret-key-32-chars-long'

export function encryptApiKey(apiKey: string): string {
  const cipher = crypto.createCipher('aes-256-cbc', ENCRYPTION_KEY)
  let encrypted = cipher.update(apiKey, 'utf8', 'hex')
  encrypted += cipher.final('hex')
  return encrypted
}

export function decryptApiKey(encryptedApiKey: string): string {
  const decipher = crypto.createDecipher('aes-256-cbc', ENCRYPTION_KEY)
  let decrypted = decipher.update(encryptedApiKey, 'hex', 'utf8')
  decrypted += decipher.final('utf8')
  return decrypted
}

export const EXCHANGE_CONFIGS: Record<string, ExchangeConfig> = {
  binance: {
    id: 'binance',
    name: 'Binance',
    apiKey: process.env.BINANCE_API_KEY || '',
    secretKey: process.env.BINANCE_SECRET_KEY || '',
    sandbox: process.env.SANDBOX_MODE === 'true',
    testnet: false,
    rateLimit: 1200, // Binance allows 1200 requests per minute
    maxOrderSize: 100000 // $100k max order
  },
  mexc: {
    id: 'mexc',
    name: 'MEXC',
    apiKey: process.env.MEXC_API_KEY || '',
    secretKey: process.env.MEXC_SECRET_KEY || '',
    sandbox: process.env.SANDBOX_MODE === 'true',
    testnet: false,
    rateLimit: 600, // MEXC allows 600 requests per minute
    maxOrderSize: 50000 // $50k max order
  },
  kucoin: {
    id: 'kucoin',
    name: 'KuCoin',
    apiKey: process.env.KUCOIN_API_KEY || '',
    secretKey: process.env.KUCOIN_SECRET_KEY || '',
    passphrase: process.env.KUCOIN_PASSPHRASE || '',
    sandbox: process.env.SANDBOX_MODE === 'true',
    testnet: false,
    rateLimit: 1800, // KuCoin allows 1800 requests per minute
    maxOrderSize: 75000 // $75k max order
  },
  bybit: {
    id: 'bybit',
    name: 'Bybit',
    apiKey: process.env.BYBIT_API_KEY || '',
    secretKey: process.env.BYBIT_SECRET_KEY || '',
    sandbox: process.env.SANDBOX_MODE === 'true',
    testnet: false,
    rateLimit: 1000, // Bybit allows 1000 requests per minute
    maxOrderSize: 100000 // $100k max order
  }
}

export const SUPPORTED_EXCHANGES = Object.keys(EXCHANGE_CONFIGS)

export function getExchangeConfig(exchangeId: string): ExchangeConfig | null {
  return EXCHANGE_CONFIGS[exchangeId] || null
}

export function validateExchangeConfig(exchangeId: string): boolean {
  const config = getExchangeConfig(exchangeId)
  if (!config) return false
  
  // Check if API keys are configured and not empty
  const hasValidKeys = !!(config.apiKey && config.secretKey && 
                         config.apiKey.trim() !== '' && config.secretKey.trim() !== '')
  
  // Additional validation for KuCoin
  if (exchangeId === 'kucoin' && (!config.passphrase || config.passphrase.trim() === '')) {
    return false
  }
  
  return hasValidKeys
}

export function getExchangeStatus(exchangeId: string): 'CONNECTED' | 'DISCONNECTED' | 'ERROR' {
  const config = getExchangeConfig(exchangeId)
  if (!config) return 'ERROR'
  
  if (!validateExchangeConfig(exchangeId)) {
    return 'DISCONNECTED'
  }
  
  return 'CONNECTED'
}

// Rate limiting utility
export class RateLimiter {
  private requests: Map<string, number[]> = new Map()
  private limits: Map<string, number> = new Map()

  constructor() {
    // Initialize rate limits for each exchange
    Object.entries(EXCHANGE_CONFIGS).forEach(([exchangeId, config]) => {
      this.limits.set(exchangeId, config.rateLimit || 600)
    })
  }

  canMakeRequest(exchangeId: string): boolean {
    const now = Date.now()
    const oneMinuteAgo = now - 60000
    const requests = this.requests.get(exchangeId) || []
    
    // Remove old requests
    const recentRequests = requests.filter(time => time > oneMinuteAgo)
    this.requests.set(exchangeId, recentRequests)
    
    const limit = this.limits.get(exchangeId) || 600
    return recentRequests.length < limit
  }

  recordRequest(exchangeId: string): void {
    const requests = this.requests.get(exchangeId) || []
    requests.push(Date.now())
    this.requests.set(exchangeId, requests)
  }

  getRemainingRequests(exchangeId: string): number {
    const limit = this.limits.get(exchangeId) || 600
    const requests = this.requests.get(exchangeId) || []
    const now = Date.now()
    const oneMinuteAgo = now - 60000
    const recentRequests = requests.filter(time => time > oneMinuteAgo)
    
    return Math.max(0, limit - recentRequests.length)
  }
} 