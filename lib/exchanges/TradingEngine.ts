import { ExchangeManager, ExchangeConfig } from './ExchangeManager'

export class TradingEngine {
  private exchangeManager: ExchangeManager
  private isEnabled: boolean = false
  private maxRetries: number = 3
  private retryDelay: number = 1000

  constructor() {
    this.exchangeManager = new ExchangeManager()
  }

  async enableTrading(configs: ExchangeConfig[]) {
    try {
      console.log('🚀 Enabling trading engine...')
      
      // Validate all configs first
      for (const config of configs) {
        if (!this.validateConfig(config)) {
          throw new Error(`Invalid config for ${config.id}`)
        }
      }

      // Initialize exchanges with retry logic
      for (const config of configs) {
        await this.initializeExchangeWithRetry(config)
      }

      this.isEnabled = true
      console.log('✅ Trading Engine ENABLED')
    } catch (error) {
      console.error('❌ Failed to enable trading:', error)
      throw error
    }
  }

  private validateConfig(config: ExchangeConfig): boolean {
    return !!(config.apiKey && config.secretKey && config.id)
  }

  private async initializeExchangeWithRetry(config: ExchangeConfig): Promise<void> {
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        await this.exchangeManager.initializeExchange(config)
        console.log(`✅ ${config.id} initialized successfully`)
        return
      } catch (error) {
        console.warn(`⚠️ Attempt ${attempt} failed for ${config.id}:`, error)
        if (attempt === this.maxRetries) {
          throw new Error(`Failed to initialize ${config.id} after ${this.maxRetries} attempts`)
        }
        await this.delay(this.retryDelay * attempt)
      }
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  async disableTrading() {
    this.isEnabled = false
    console.log('🛑 Trading Engine DISABLED')
  }

  async executeTrade(params: {
    exchange: string
    symbol: string
    side: 'buy' | 'sell'
    amount: number
    type: 'market' | 'limit'
    price?: number
    stopLoss?: number
    takeProfit?: number
  }) {
    if (!this.isEnabled) {
      throw new Error('Trading is disabled')
    }

    // Validate trade parameters
    this.validateTradeParams(params)

    try {
      // Check balance before placing order
      await this.checkBalance(params.exchange, params.amount, params.symbol)

      // 1. Place main order
      const order = await this.exchangeManager.placeOrder(
        params.exchange,
        params.symbol,
        params.type,
        params.side,
        params.amount,
        params.price
      )

      console.log(`✅ Order placed: ${order.id}`)

      // 2. Set stop loss if provided
      if (params.stopLoss) {
        const stopLossOrder = await this.exchangeManager.placeOrder(
          params.exchange,
          params.symbol,
          'limit',
          params.side === 'buy' ? 'sell' : 'buy',
          params.amount,
          params.stopLoss
        )
        console.log(`🛡️ Stop loss set: ${stopLossOrder.id}`)
      }

      // 3. Set take profit if provided
      if (params.takeProfit) {
        const takeProfitOrder = await this.exchangeManager.placeOrder(
          params.exchange,
          params.symbol,
          'limit',
          params.side === 'buy' ? 'sell' : 'buy',
          params.amount,
          params.takeProfit
        )
        console.log(`🎯 Take profit set: ${takeProfitOrder.id}`)
      }

      return order
    } catch (error) {
      console.error('❌ Trade execution failed:', error)
      throw error
    }
  }

  private validateTradeParams(params: any): void {
    if (!params.exchange || !params.symbol || !params.side || !params.amount) {
      throw new Error('Missing required trade parameters')
    }

    if (params.amount <= 0) {
      throw new Error('Amount must be greater than 0')
    }

    if (params.price && params.price <= 0) {
      throw new Error('Price must be greater than 0')
    }

    if (!['buy', 'sell'].includes(params.side)) {
      throw new Error('Side must be buy or sell')
    }

    if (!['market', 'limit'].includes(params.type)) {
      throw new Error('Type must be market or limit')
    }
  }

  private async checkBalance(exchange: string, amount: number, symbol: string): Promise<void> {
    const balances = await this.exchangeManager.getBalances()
    const exchangeBalance = balances.find(b => b.exchange === exchange)
    
    if (!exchangeBalance || exchangeBalance.free < amount) {
      throw new Error(`Insufficient balance on ${exchange}`)
    }
  }

  async emergencyStop() {
    console.log('🚨 EMERGENCY STOP ACTIVATED')
    
    try {
      // Cancel all open orders
      const exchanges = this.exchangeManager.getSupportedExchanges()
      
      for (const exchange of exchanges) {
        if (this.exchangeManager.isExchangeConnected(exchange)) {
          await this.exchangeManager.cancelAllOrders(exchange)
          console.log(`❌ Cancelled all orders on ${exchange}`)
        }
      }
      
      this.isEnabled = false
      console.log('✅ Emergency stop completed')
    } catch (error) {
      console.error('❌ Emergency stop failed:', error)
      throw error
    }
  }

  getStatus(): { enabled: boolean; exchanges: string[] } {
    return {
      enabled: this.isEnabled,
      exchanges: this.exchangeManager.getSupportedExchanges()
    }
  }
} 