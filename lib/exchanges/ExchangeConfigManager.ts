export interface ExchangeCredentials {
  apiKey: string
  apiSecret: string
  passphrase?: string // For some exchanges like KuCoin
  testnet?: boolean
  subAccount?: string
}

export interface ExchangeConfig {
  id: string
  name: string
  displayName: string
  description: string
  website: string
  fees: {
    maker: number
    taker: number
  }
  limits: {
    minOrder: number
    maxOrder: number
    minNotional: number
  }
  features: {
    spot: boolean
    futures: boolean
    margin: boolean
    lending: boolean
    staking: boolean
  }
  credentials?: ExchangeCredentials
  enabled: boolean
  testMode: boolean
  rateLimit: {
    requestsPerSecond: number
    requestsPerMinute: number
  }
  supportedPairs: string[]
  lastConnected?: string
  connectionStatus: 'connected' | 'disconnected' | 'error' | 'testing'
  errorMessage?: string
}

export interface TradingSettings {
  exchangeId: string
  defaultOrderType: 'market' | 'limit' | 'stop' | 'stop_limit'
  defaultTimeInForce: 'GTC' | 'IOC' | 'FOK'
  maxPositionSize: number // Percentage of portfolio
  maxDailyLoss: number // Percentage
  enableStopLoss: boolean
  defaultStopLoss: number // Percentage
  enableTakeProfit: boolean
  defaultTakeProfit: number // Percentage
  slippage: number // Percentage
  minOrderSize: number // USD
  maxOrderSize: number // USD
  enablePaperTrading: boolean
  confirmOrders: boolean
}

export class ExchangeConfigManager {
  private exchanges: Map<string, ExchangeConfig> = new Map()
  private tradingSettings: Map<string, TradingSettings> = new Map()
  private encryptionKey: string = 'your-encryption-key' // Should be from env

  constructor() {
    this.initializeExchanges()
    this.loadConfigurations()
  }

  private initializeExchanges() {
    const defaultExchanges: ExchangeConfig[] = [
      {
        id: 'binance',
        name: 'binance',
        displayName: 'Binance',
        description: 'World\'s largest cryptocurrency exchange',
        website: 'https://www.binance.com',
        fees: { maker: 0.001, taker: 0.001 },
        limits: { minOrder: 0.00001, maxOrder: 9000000, minNotional: 10 },
        features: { spot: true, futures: true, margin: true, lending: true, staking: true },
        enabled: false,
        testMode: true,
        rateLimit: { requestsPerSecond: 10, requestsPerMinute: 1200 },
        supportedPairs: ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT'],
        connectionStatus: 'disconnected'
      },
      {
        id: 'bybit',
        name: 'bybit',
        displayName: 'Bybit',
        description: 'Leading derivatives exchange',
        website: 'https://www.bybit.com',
        fees: { maker: 0.0001, taker: 0.0006 },
        limits: { minOrder: 0.00001, maxOrder: 1000000, minNotional: 1 },
        features: { spot: true, futures: true, margin: true, lending: false, staking: false },
        enabled: false,
        testMode: true,
        rateLimit: { requestsPerSecond: 10, requestsPerMinute: 600 },
        supportedPairs: ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT'],
        connectionStatus: 'disconnected'
      },
      {
        id: 'mexc',
        name: 'mexc',
        displayName: 'MEXC',
        description: 'Global digital asset trading platform',
        website: 'https://www.mexc.com',
        fees: { maker: 0.002, taker: 0.002 },
        limits: { minOrder: 0.00001, maxOrder: 1000000, minNotional: 5 },
        features: { spot: true, futures: true, margin: true, lending: false, staking: true },
        enabled: false,
        testMode: true,
        rateLimit: { requestsPerSecond: 20, requestsPerMinute: 1200 },
        supportedPairs: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT'],
        connectionStatus: 'disconnected'
      },
      {
        id: 'kucoin',
        name: 'kucoin',
        displayName: 'KuCoin',
        description: 'The People\'s Exchange',
        website: 'https://www.kucoin.com',
        fees: { maker: 0.001, taker: 0.001 },
        limits: { minOrder: 0.00001, maxOrder: 1000000, minNotional: 1 },
        features: { spot: true, futures: true, margin: true, lending: true, staking: true },
        enabled: false,
        testMode: true,
        rateLimit: { requestsPerSecond: 10, requestsPerMinute: 600 },
        supportedPairs: ['BTC-USDT', 'ETH-USDT', 'ADA-USDT', 'DOT-USDT', 'LINK-USDT'],
        connectionStatus: 'disconnected'
      },
      {
        id: 'okx',
        name: 'okx',
        displayName: 'OKX',
        description: 'Leading crypto trading platform',
        website: 'https://www.okx.com',
        fees: { maker: 0.0008, taker: 0.001 },
        limits: { minOrder: 0.00001, maxOrder: 1000000, minNotional: 1 },
        features: { spot: true, futures: true, margin: true, lending: true, staking: true },
        enabled: false,
        testMode: true,
        rateLimit: { requestsPerSecond: 20, requestsPerMinute: 1200 },
        supportedPairs: ['BTC-USDT', 'ETH-USDT', 'SOL-USDT', 'ADA-USDT', 'DOT-USDT'],
        connectionStatus: 'disconnected'
      }
    ]

    defaultExchanges.forEach(exchange => {
      this.exchanges.set(exchange.id, exchange)
      
      // Initialize default trading settings
      this.tradingSettings.set(exchange.id, {
        exchangeId: exchange.id,
        defaultOrderType: 'limit',
        defaultTimeInForce: 'GTC',
        maxPositionSize: 10, // 10% of portfolio
        maxDailyLoss: 5, // 5% daily loss limit
        enableStopLoss: true,
        defaultStopLoss: 2, // 2% stop loss
        enableTakeProfit: true,
        defaultTakeProfit: 5, // 5% take profit
        slippage: 0.5, // 0.5% slippage
        minOrderSize: 10, // $10 minimum
        maxOrderSize: 1000, // $1000 maximum
        enablePaperTrading: true,
        confirmOrders: true
      })
    })
  }

  private loadConfigurations() {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem('exchange-config')
        if (stored) {
          const config = JSON.parse(stored)
          
          if (config.exchanges) {
            Object.entries(config.exchanges).forEach(([id, exchangeConfig]) => {
              const existing = this.exchanges.get(id)
              if (existing) {
                this.exchanges.set(id, { ...existing, ...exchangeConfig as ExchangeConfig })
              }
            })
          }
          
          if (config.tradingSettings) {
            Object.entries(config.tradingSettings).forEach(([id, settings]) => {
              this.tradingSettings.set(id, settings as TradingSettings)
            })
          }
        }
      } catch (error) {
        console.warn('Failed to load exchange configuration:', error)
      }
    }
  }

  private saveConfigurations() {
    if (typeof window !== 'undefined') {
      try {
        const config = {
          exchanges: Object.fromEntries(this.exchanges),
          tradingSettings: Object.fromEntries(this.tradingSettings)
        }
        localStorage.setItem('exchange-config', JSON.stringify(config))
      } catch (error) {
        console.warn('Failed to save exchange configuration:', error)
      }
    }
  }

  // Exchange Management
  getExchange(exchangeId: string): ExchangeConfig | null {
    return this.exchanges.get(exchangeId) || null
  }

  getAllExchanges(): ExchangeConfig[] {
    return Array.from(this.exchanges.values())
  }

  getEnabledExchanges(): ExchangeConfig[] {
    return Array.from(this.exchanges.values()).filter(exchange => exchange.enabled)
  }

  updateExchange(exchangeId: string, updates: Partial<ExchangeConfig>) {
    const existing = this.exchanges.get(exchangeId)
    if (existing) {
      this.exchanges.set(exchangeId, { ...existing, ...updates })
      this.saveConfigurations()
    }
  }

  setCredentials(exchangeId: string, credentials: ExchangeCredentials) {
    const exchange = this.exchanges.get(exchangeId)
    if (exchange) {
      // In production, encrypt the credentials
      exchange.credentials = credentials
      exchange.enabled = true
      this.exchanges.set(exchangeId, exchange)
      this.saveConfigurations()
    }
  }

  removeCredentials(exchangeId: string) {
    const exchange = this.exchanges.get(exchangeId)
    if (exchange) {
      delete exchange.credentials
      exchange.enabled = false
      exchange.connectionStatus = 'disconnected'
      this.exchanges.set(exchangeId, exchange)
      this.saveConfigurations()
    }
  }

  // Trading Settings Management
  getTradingSettings(exchangeId: string): TradingSettings | null {
    return this.tradingSettings.get(exchangeId) || null
  }

  updateTradingSettings(exchangeId: string, settings: Partial<TradingSettings>) {
    const existing = this.tradingSettings.get(exchangeId)
    if (existing) {
      this.tradingSettings.set(exchangeId, { ...existing, ...settings })
      this.saveConfigurations()
    }
  }

  // Connection Testing
  async testConnection(exchangeId: string): Promise<{
    success: boolean
    message: string
    data?: any
  }> {
    const exchange = this.exchanges.get(exchangeId)
    if (!exchange || !exchange.credentials) {
      return {
        success: false,
        message: 'Exchange not configured or missing credentials'
      }
    }

    try {
      exchange.connectionStatus = 'testing'
      this.exchanges.set(exchangeId, exchange)

      // Test API connection
      const response = await fetch('/api/exchanges/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          exchangeId,
          credentials: exchange.credentials,
          testMode: exchange.testMode
        })
      })

      const result = await response.json()

      if (result.success) {
        exchange.connectionStatus = 'connected'
        exchange.lastConnected = new Date().toISOString()
        delete exchange.errorMessage
      } else {
        exchange.connectionStatus = 'error'
        exchange.errorMessage = result.message
      }

      this.exchanges.set(exchangeId, exchange)
      this.saveConfigurations()

      return result
    } catch (error) {
      exchange.connectionStatus = 'error'
      exchange.errorMessage = error instanceof Error ? error.message : 'Unknown error'
      this.exchanges.set(exchangeId, exchange)
      this.saveConfigurations()

      return {
        success: false,
        message: `Connection test failed: ${exchange.errorMessage}`
      }
    }
  }

  // Validation
  validateConfiguration(exchangeId: string): {
    valid: boolean
    errors: string[]
    warnings: string[]
  } {
    const exchange = this.exchanges.get(exchangeId)
    const settings = this.tradingSettings.get(exchangeId)
    const errors: string[] = []
    const warnings: string[] = []

    if (!exchange) {
      errors.push('Exchange configuration not found')
      return { valid: false, errors, warnings }
    }

    if (!exchange.credentials) {
      errors.push('API credentials not configured')
    } else {
      if (!exchange.credentials.apiKey) errors.push('API key is required')
      if (!exchange.credentials.apiSecret) errors.push('API secret is required')
      if (exchange.id === 'kucoin' && !exchange.credentials.passphrase) {
        errors.push('Passphrase is required for KuCoin')
      }
    }

    if (!settings) {
      warnings.push('Trading settings not configured, using defaults')
    } else {
      if (settings.maxPositionSize > 50) {
        warnings.push('Position size over 50% is risky')
      }
      if (settings.maxDailyLoss > 10) {
        warnings.push('Daily loss limit over 10% is risky')
      }
      if (!settings.enableStopLoss) {
        warnings.push('Stop loss is disabled - high risk')
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }

  // Portfolio Integration
  getPortfolioDistribution(): { [exchangeId: string]: number } {
    // This would integrate with portfolio manager
    const enabled = this.getEnabledExchanges()
    const distribution: { [exchangeId: string]: number } = {}

    enabled.forEach(exchange => {
      distribution[exchange.id] = 100 / enabled.length // Equal distribution for now
    })

    return distribution
  }

  // Security
  private encryptCredentials(credentials: ExchangeCredentials): string {
    // In production, use proper encryption
    return btoa(JSON.stringify(credentials))
  }

  private decryptCredentials(encrypted: string): ExchangeCredentials {
    // In production, use proper decryption
    return JSON.parse(atob(encrypted))
  }

  // Reset to defaults
  resetExchange(exchangeId: string) {
    this.initializeExchanges()
    this.saveConfigurations()
  }

  resetAllExchanges() {
    this.exchanges.clear()
    this.tradingSettings.clear()
    this.initializeExchanges()
    this.saveConfigurations()
  }
}

// Singleton instance
export const exchangeConfigManager = new ExchangeConfigManager()
