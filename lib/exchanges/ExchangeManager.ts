import ccxt from 'ccxt'
import { getExchangeConfig, validateExchangeConfig, type ExchangeConfig } from './config'

export interface ExchangeBalance {
  exchange: string
  total: number
  free: number
  used: number
  currency: string
}

export interface ExchangePosition {
  exchange: string
  symbol: string
  side: 'long' | 'short'
  size: number
  entryPrice: number
  markPrice: number
  pnl: number
  pnlPercent: number
}

export class ExchangeManager {
  private exchanges: Map<string, ccxt.Exchange> = new Map()

  constructor() {
    this.initializeExchanges()
  }

  private initializeExchanges() {
    const exchanges = ['binance', 'mexc', 'kucoin', 'bybit']
    
    exchanges.forEach(exchangeId => {
      const config = getExchangeConfig(exchangeId)
      if (!config || !validateExchangeConfig(exchangeId)) {
        console.log(`⚠️  ${exchangeId} not configured`)
        return
      }

      try {
        const exchange = this.createExchangeInstance(config)
        this.exchanges.set(exchangeId, exchange)
        console.log(`✅ ${exchangeId} initialized`)
      } catch (error) {
        console.error(`❌ Failed to initialize ${exchangeId}:`, error)
      }
    })
  }

  private createExchangeInstance(config: ExchangeConfig): ccxt.Exchange {
    const exchangeClass = ccxt[config.id as keyof typeof ccxt] as any
    
    if (!exchangeClass) {
      throw new Error(`Exchange ${config.id} not supported by CCXT`)
    }

    const exchangeConfig: any = {
      apiKey: config.apiKey,
      secret: config.secretKey,
      sandbox: config.sandbox,
      testnet: config.testnet,
      enableRateLimit: true
    }

    // KuCoin requires passphrase
    if (config.id === 'kucoin' && config.passphrase) {
      exchangeConfig.password = config.passphrase
    }

    return new exchangeClass(exchangeConfig)
  }

  async getBalances(): Promise<ExchangeBalance[]> {
    const balances: ExchangeBalance[] = []

    for (const [exchangeId, exchange] of this.exchanges) {
      try {
        const balance = await exchange.fetchBalance()
        
        // Convert to our format
        Object.keys(balance.total).forEach(currency => {
          if (balance.total[currency] > 0) {
            balances.push({
              exchange: exchangeId,
              total: balance.total[currency],
              free: balance.free[currency],
              used: balance.used[currency],
              currency
            })
          }
        })
      } catch (error) {
        console.error(`❌ Failed to fetch balance for ${exchangeId}:`, error)
      }
    }

    return balances
  }

  async getPositions(): Promise<ExchangePosition[]> {
    const positions: ExchangePosition[] = []

    for (const [exchangeId, exchange] of this.exchanges) {
      try {
        // Not all exchanges support fetchPositions
        if (exchange.has['fetchPositions']) {
          const exchangePositions = await exchange.fetchPositions()
          
          exchangePositions.forEach((pos: any) => {
            if (pos.size > 0) {
              positions.push({
                exchange: exchangeId,
                symbol: pos.symbol,
                side: pos.side,
                size: pos.size,
                entryPrice: pos.entryPrice,
                markPrice: pos.markPrice,
                pnl: pos.unrealizedPnl,
                pnlPercent: pos.percentage
              })
            }
          })
        }
      } catch (error) {
        console.error(`❌ Failed to fetch positions for ${exchangeId}:`, error)
      }
    }

    return positions
  }

  async placeOrder(
    exchangeId: string,
    symbol: string,
    side: 'buy' | 'sell',
    amount: number,
    price?: number
  ) {
    const exchange = this.exchanges.get(exchangeId)
    if (!exchange) {
      throw new Error(`Exchange ${exchangeId} not initialized`)
    }

    try {
      const order = await exchange.createOrder(
        symbol,
        price ? 'limit' : 'market',
        side,
        amount,
        price
      )
      
      console.log(`✅ Order placed on ${exchangeId}:`, order)
      return order
    } catch (error) {
      console.error(`❌ Failed to place order on ${exchangeId}:`, error)
      throw error
    }
  }

  async getExchangeStatus(): Promise<Record<string, 'CONNECTED' | 'DISCONNECTED' | 'ERROR'>> {
    const status: Record<string, 'CONNECTED' | 'DISCONNECTED' | 'ERROR'> = {}
    
    for (const [exchangeId, exchange] of this.exchanges) {
      try {
        // Test connection by fetching markets
        await exchange.loadMarkets()
        status[exchangeId] = 'CONNECTED'
      } catch (error) {
        console.error(`❌ ${exchangeId} connection failed:`, error)
        status[exchangeId] = 'ERROR'
      }
    }

    return status
  }

  getSupportedExchanges(): string[] {
    return Array.from(this.exchanges.keys())
  }

  isExchangeConnected(exchangeId: string): boolean {
    return this.exchanges.has(exchangeId)
  }
}

// Singleton instance
export const exchangeManager = new ExchangeManager() 