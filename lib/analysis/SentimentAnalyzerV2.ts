export interface SentimentData {
  source: 'twitter' | 'reddit' | 'news' | 'telegram' | 'discord'
  timestamp: Date
  sentiment: 'positive' | 'negative' | 'neutral'
  score: number // -1 to 1
  volume: number
  keywords: string[]
  text: string
  author?: string
  engagement?: number
  verified?: boolean
}

export interface SentimentAnalysisResult {
  overallSentiment: 'bullish' | 'bearish' | 'neutral'
  sentimentScore: number // -100 to 100
  confidence: number // 0 to 1
  sources: {
    twitter: SentimentData[]
    reddit: SentimentData[]
    news: SentimentData[]
    telegram: SentimentData[]
    discord: SentimentData[]
  }
  volume: {
    twitter: number
    reddit: number
    news: number
    telegram: number
    discord: number
  }
  trendingTopics: string[]
  influencers: {
    twitter: string[]
    reddit: string[]
    telegram: string[]
    discord: string[]
  }
  marketMood: {
    fear: number // 0-100
    greed: number // 0-100
    volatility: number // 0-100
  }
  timeSeries: {
    hourly: { timestamp: Date; score: number }[]
    daily: { timestamp: Date; score: number }[]
  }
}

export interface SentimentConfig {
  // API Keys
  twitterApiKey?: string
  twitterApiSecret?: string
  twitterBearerToken?: string
  redditClientId?: string
  redditClientSecret?: string
  redditUsername?: string
  redditPassword?: string
  newsApiKey?: string
  telegramBotToken?: string
  discordWebhookUrl?: string
  
  // Analysis Settings
  sentimentWeight: number // 0-1
  confidenceThreshold: number // 0-1
  maxDataPoints: number
  cacheDuration: number // minutes
  
  // Rate Limiting
  requestsPerMinute: number
  requestsPerHour: number
  
  // Advanced Features
  enableAIAnalysis: boolean
  enableEmotionDetection: boolean
  enableSarcasmDetection: boolean
  enableContextAnalysis: boolean
}

export class SentimentAnalyzerV2 {
  private config: SentimentConfig
  private cache: Map<string, { data: SentimentAnalysisResult; timestamp: number }>
  private rateLimitTracker: Map<string, { count: number; resetTime: number }>
  private emotionKeywords: Map<string, { emotion: string; intensity: number }>
  private contextPatterns: RegExp[]

  constructor(config?: Partial<SentimentConfig>) {
    this.config = {
      // Default configuration
      sentimentWeight: 0.3,
      confidenceThreshold: 0.6,
      maxDataPoints: 1000,
      cacheDuration: 15,
      requestsPerMinute: 60,
      requestsPerHour: 1000,
      enableAIAnalysis: false,
      enableEmotionDetection: true,
      enableSarcasmDetection: true,
      enableContextAnalysis: true,
      ...config
    }

    this.cache = new Map()
    this.rateLimitTracker = new Map()
    this.initializeEmotionKeywords()
    this.initializeContextPatterns()
  }

  /**
   * Initialize emotion keywords with intensity scores
   */
  private initializeEmotionKeywords(): void {
    this.emotionKeywords = new Map([
      // Fear emotions
      ['panic', { emotion: 'fear', intensity: 0.9 }],
      ['terrified', { emotion: 'fear', intensity: 0.95 }],
      ['scared', { emotion: 'fear', intensity: 0.7 }],
      ['worried', { emotion: 'fear', intensity: 0.5 }],
      
      // Greed emotions
      ['fomo', { emotion: 'greed', intensity: 0.8 }],
      ['moon', { emotion: 'greed', intensity: 0.9 }],
      ['lambo', { emotion: 'greed', intensity: 0.7 }],
      ['rich', { emotion: 'greed', intensity: 0.6 }],
      
      // Excitement
      ['pump', { emotion: 'excitement', intensity: 0.8 }],
      ['rally', { emotion: 'excitement', intensity: 0.7 }],
      ['surge', { emotion: 'excitement', intensity: 0.8 }],
      
      // Disappointment
      ['dump', { emotion: 'disappointment', intensity: 0.8 }],
      ['crash', { emotion: 'disappointment', intensity: 0.9 }],
      ['rekt', { emotion: 'disappointment', intensity: 0.7 }]
    ])
  }

  /**
   * Initialize context patterns for better analysis
   */
  private initializeContextPatterns(): void {
    this.contextPatterns = [
      // Sarcasm patterns
      /\b(sure|yeah|right)\b.*\b(moon|pump|crash)\b/gi,
      /\b(obviously|clearly)\b.*\b(bullish|bearish)\b/gi,
      
      // Question patterns
      /\b(why|how|what|when)\b.*\b(going|happening|doing)\b/gi,
      
      // Conditional patterns
      /\b(if|when|unless)\b.*\b(then|will|would)\b/gi,
      
      // Time-based patterns
      /\b(yesterday|today|tomorrow|next|last)\b/gi,
      
      // Price action patterns
      /\b(support|resistance|breakout|breakdown)\b/gi,
      
      // Market structure patterns
      /\b(bull|bear)\s*(flag|wedge|triangle|channel)\b/gi
    ]
  }

  /**
   * Enhanced sentiment analysis with emotion detection
   */
  private analyzeTextSentiment(text: string): { 
    sentiment: 'positive' | 'negative' | 'neutral'
    score: number
    emotions: { [key: string]: number }
    context: string[]
    sarcasm: boolean
  } {
    const lowerText = text.toLowerCase()
    
    // Enhanced keyword lists with weights
    const positiveWords = new Map([
      ['bullish', 0.8], ['moon', 0.9], ['pump', 0.7], ['rally', 0.7],
      ['surge', 0.8], ['gain', 0.6], ['profit', 0.7], ['buy', 0.5],
      ['hodl', 0.6], ['diamond hands', 0.8], ['to the moon', 0.9],
      ['mooning', 0.8], ['green', 0.5], ['up', 0.4], ['rise', 0.6],
      ['strong', 0.6], ['breakout', 0.7], ['breakthrough', 0.8],
      ['adoption', 0.7], ['institutional', 0.6], ['partnership', 0.6],
      ['accumulate', 0.7], ['dca', 0.6], ['long', 0.5]
    ])

    const negativeWords = new Map([
      ['bearish', 0.8], ['dump', 0.8], ['crash', 0.9], ['sell', 0.5],
      ['panic', 0.9], ['fear', 0.7], ['loss', 0.6], ['red', 0.5],
      ['down', 0.4], ['correction', 0.6], ['bear market', 0.8],
      ['sell off', 0.7], ['decline', 0.6], ['weak', 0.5], ['breakdown', 0.7],
      ['regulation', 0.4], ['ban', 0.8], ['hack', 0.9], ['scam', 0.9],
      ['bubble', 0.7], ['rekt', 0.8], ['liquidated', 0.9], ['short', 0.5]
    ])

    // Calculate weighted scores
    let positiveScore = 0
    let negativeScore = 0
    const emotions: { [key: string]: number } = {}
    const context: string[] = []

    // Analyze positive words
    positiveWords.forEach((weight, word) => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi')
      const matches = lowerText.match(regex)
      if (matches) {
        positiveScore += matches.length * weight
      }
    })

    // Analyze negative words
    negativeWords.forEach((weight, word) => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi')
      const matches = lowerText.match(regex)
      if (matches) {
        negativeScore += matches.length * weight
      }
    })

    // Emotion detection
    this.emotionKeywords.forEach((data, keyword) => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi')
      const matches = lowerText.match(regex)
      if (matches) {
        emotions[data.emotion] = (emotions[data.emotion] || 0) + (matches.length * data.intensity)
      }
    })

    // Context analysis
    this.contextPatterns.forEach((pattern, index) => {
      const matches = lowerText.match(pattern)
      if (matches) {
        context.push(`pattern_${index}`)
      }
    })

    // Sarcasm detection
    const sarcasm = this.detectSarcasm(lowerText)

    // Normalize scores
    const totalWords = text.split(' ').length
    const normalizedPositive = positiveScore / Math.max(totalWords, 1)
    const normalizedNegative = negativeScore / Math.max(totalWords, 1)
    const netScore = normalizedPositive - normalizedNegative

    // Determine sentiment
    let sentiment: 'positive' | 'negative' | 'neutral'
    if (netScore > 0.01) {
      sentiment = 'positive'
    } else if (netScore < -0.01) {
      sentiment = 'negative'
    } else {
      sentiment = 'neutral'
    }

    return {
      sentiment,
      score: Math.max(-1, Math.min(1, netScore * 10)), // Scale to -1 to 1
      emotions,
      context,
      sarcasm
    }
  }

  /**
   * Detect sarcasm in text
   */
  private detectSarcasm(text: string): boolean {
    const sarcasmIndicators = [
      /\b(sure|yeah|right)\b.*\b(moon|pump|crash)\b/gi,
      /\b(obviously|clearly)\b.*\b(bullish|bearish)\b/gi,
      /\b(wow|amazing)\b.*\b(dump|crash)\b/gi,
      /\b(great|perfect)\b.*\b(loss|rekt)\b/gi,
      /\b(😂|🤣|😅|😆)\b/gi, // Emoji indicators
      /\b(lol|rofl|lmao)\b/gi
    ]

    return sarcasmIndicators.some(pattern => pattern.test(text))
  }

  /**
   * Enhanced keyword extraction with categories
   */
  private extractKeywords(text: string): { keywords: string[], categories: { [key: string]: string[] } } {
    const keywordCategories = {
      cryptocurrencies: ['bitcoin', 'ethereum', 'btc', 'eth', 'crypto', 'altcoin', 'token'],
      trading: ['trading', 'buy', 'sell', 'long', 'short', 'position', 'entry', 'exit'],
      market: ['market', 'price', 'volume', 'liquidity', 'spread', 'bid', 'ask'],
      technical: ['support', 'resistance', 'breakout', 'breakdown', 'trend', 'pattern'],
      defi: ['defi', 'yield', 'apy', 'staking', 'liquidity', 'pool', 'farm'],
      nft: ['nft', 'mint', 'floor', 'rarity', 'collection'],
      blockchain: ['blockchain', 'smart contract', 'gas', 'fees', 'mining'],
      sentiment: ['bullish', 'bearish', 'moon', 'dump', 'pump', 'crash']
    }

    const lowerText = text.toLowerCase()
    const foundKeywords: string[] = []
    const categories: { [key: string]: string[] } = {}

    Object.entries(keywordCategories).forEach(([category, keywords]) => {
      const foundInCategory: string[] = []
      keywords.forEach(keyword => {
        if (lowerText.includes(keyword)) {
          foundKeywords.push(keyword)
          foundInCategory.push(keyword)
        }
      })
      if (foundInCategory.length > 0) {
        categories[category] = foundInCategory
      }
    })

    return { keywords: foundKeywords, categories }
  }

  /**
   * Calculate market mood indicators
   */
  private calculateMarketMood(data: SentimentData[]): { fear: number; greed: number; volatility: number } {
    const emotions = data.flatMap(item => {
      const analysis = this.analyzeTextSentiment(item.text)
      return Object.entries(analysis.emotions)
    })

    const fearScore = emotions
      .filter(([emotion]) => emotion === 'fear')
      .reduce((sum, [, intensity]) => sum + intensity, 0)

    const greedScore = emotions
      .filter(([emotion]) => emotion === 'greed')
      .reduce((sum, [, intensity]) => sum + intensity, 0)

    // Calculate volatility based on sentiment variance
    const scores = data.map(item => item.score)
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length
    const volatility = Math.min(100, Math.sqrt(variance) * 100)

    return {
      fear: Math.min(100, (fearScore / Math.max(data.length, 1)) * 100),
      greed: Math.min(100, (greedScore / Math.max(data.length, 1)) * 100),
      volatility
    }
  }

  /**
   * Rate limiting check
   */
  private checkRateLimit(api: string): boolean {
    const now = Date.now()
    const key = `${api}_${Math.floor(now / 60000)}` // Per minute
    const current = this.rateLimitTracker.get(key) || { count: 0, resetTime: now + 60000 }
    
    if (now > current.resetTime) {
      current.count = 0
      current.resetTime = now + 60000
    }

    const limit = this.config.requestsPerMinute
    if (current.count >= limit) {
      return false
    }

    current.count++
    this.rateLimitTracker.set(key, current)
    return true
  }

  /**
   * Cache management
   */
  private getCachedResult(symbol: string): SentimentAnalysisResult | null {
    const cached = this.cache.get(symbol)
    if (!cached) return null

    const age = Date.now() - cached.timestamp
    const maxAge = this.config.cacheDuration * 60 * 1000 // Convert to milliseconds

    if (age > maxAge) {
      this.cache.delete(symbol)
      return null
    }

    return cached.data
  }

  private setCachedResult(symbol: string, data: SentimentAnalysisResult): void {
    this.cache.set(symbol, { data, timestamp: Date.now() })
  }

  /**
   * Main sentiment analysis method
   */
  async analyzeSentiment(symbol: string): Promise<SentimentAnalysisResult> {
    console.log(`🔍 Analyzing sentiment for ${symbol}...`)

    // Check cache first
    const cached = this.getCachedResult(symbol)
    if (cached) {
      console.log(`✅ Returning cached result for ${symbol}`)
      return cached
    }

    // Check rate limits
    if (!this.checkRateLimit('sentiment')) {
      throw new Error('Rate limit exceeded')
    }

    try {
      // Collect data from all sources (simplified for demo)
      const mockData = this.generateMockData(symbol)
      
      // Analyze sentiment
      const result = this.combineSentimentData(mockData, symbol)
      
      // Cache the result
      this.setCachedResult(symbol, result)
      
      console.log(`✅ Sentiment analysis complete for ${symbol}`)
      return result

    } catch (error) {
      console.error(`❌ Sentiment analysis failed for ${symbol}:`, error)
      throw error
    }
  }

  /**
   * Generate mock data for testing
   */
  private generateMockData(symbol: string): SentimentData[] {
    const mockTexts = [
      `${symbol} is looking very bullish today! Moon incoming! 🚀`,
      `${symbol} price is crashing hard, this is a bear market`,
      `${symbol} showing strong support at current levels`,
      `${symbol} volume is increasing, institutional adoption growing`,
      `${symbol} facing resistance, might dump soon`,
      `${symbol} breaking out of the triangle pattern`,
      `${symbol} fundamentals are solid, long term bullish`,
      `${symbol} technical analysis shows bearish divergence`
    ]

    return mockTexts.map((text, index) => {
      const analysis = this.analyzeTextSentiment(text)
      const keywordData = this.extractKeywords(text)
      
      return {
        source: ['twitter', 'reddit', 'news'][index % 3] as 'twitter' | 'reddit' | 'news',
        timestamp: new Date(Date.now() - index * 3600000), // Spread over hours
        sentiment: analysis.sentiment,
        score: analysis.score,
        volume: Math.floor(Math.random() * 1000) + 10,
        keywords: keywordData.keywords,
        text,
        author: `user_${index}`,
        engagement: Math.floor(Math.random() * 100) + 1,
        verified: index % 3 === 0
      }
    })
  }

  /**
   * Combine sentiment data with enhanced analysis
   */
  private combineSentimentData(data: SentimentData[], symbol: string): SentimentAnalysisResult {
    // Calculate weighted sentiment scores
    const weightedScore = this.calculateWeightedScore(data)
    
    // Determine overall sentiment
    let overallSentiment: 'bullish' | 'bearish' | 'neutral'
    if (weightedScore > 20) {
      overallSentiment = 'bullish'
    } else if (weightedScore < -20) {
      overallSentiment = 'bearish'
    } else {
      overallSentiment = 'neutral'
    }

    // Calculate confidence
    const confidence = this.calculateConfidence(data)

    // Extract trending topics
    const trendingTopics = this.extractTrendingTopics(data)

    // Calculate market mood
    const marketMood = this.calculateMarketMood(data)

    // Generate time series data
    const timeSeries = this.generateTimeSeries(data)

    // Group data by source
    const sources = {
      twitter: data.filter(item => item.source === 'twitter'),
      reddit: data.filter(item => item.source === 'reddit'),
      news: data.filter(item => item.source === 'news'),
      telegram: [] as SentimentData[],
      discord: [] as SentimentData[]
    }

    return {
      overallSentiment,
      sentimentScore: weightedScore,
      confidence,
      sources,
      volume: {
        twitter: sources.twitter.length,
        reddit: sources.reddit.length,
        news: sources.news.length,
        telegram: 0,
        discord: 0
      },
      trendingTopics,
      influencers: {
        twitter: [],
        reddit: [],
        telegram: [],
        discord: []
      },
      marketMood,
      timeSeries
    }
  }

  /**
   * Calculate weighted sentiment score
   */
  private calculateWeightedScore(data: SentimentData[]): number {
    if (data.length === 0) return 0

    const totalVolume = data.reduce((sum, item) => sum + item.volume, 0)
    if (totalVolume === 0) return 0

    const weightedScore = data.reduce((sum, item) => {
      const weight = item.volume / totalVolume
      return sum + (item.score * weight)
    }, 0)

    return weightedScore * 100 // Convert to -100 to 100 scale
  }

  /**
   * Calculate confidence based on data quality
   */
  private calculateConfidence(data: SentimentData[]): number {
    if (data.length === 0) return 0

    // Base confidence on data volume
    let confidence = Math.min(data.length / 50, 1)

    // Boost confidence for verified sources
    const verifiedCount = data.filter(item => item.verified).length
    if (verifiedCount > 0) {
      confidence += 0.2
    }

    // Boost confidence for recent data
    const now = new Date()
    const recentData = data.filter(
      item => now.getTime() - item.timestamp.getTime() < 24 * 60 * 60 * 1000
    )
    if (recentData.length > 0) {
      confidence += 0.1
    }

    return Math.min(confidence, 1)
  }

  /**
   * Extract trending topics
   */
  private extractTrendingTopics(data: SentimentData[]): string[] {
    const allKeywords = data.flatMap(item => item.keywords)
    const keywordCount: Record<string, number> = {}
    
    allKeywords.forEach(keyword => {
      keywordCount[keyword] = (keywordCount[keyword] || 0) + 1
    })

    return Object.entries(keywordCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([keyword]) => keyword)
  }

  /**
   * Generate time series data
   */
  private generateTimeSeries(data: SentimentData[]): { hourly: { timestamp: Date; score: number }[], daily: { timestamp: Date; score: number }[] } {
    // Group by hour and day
    const hourlyGroups = new Map<number, SentimentData[]>()
    const dailyGroups = new Map<number, SentimentData[]>()

    data.forEach(item => {
      const hour = Math.floor(item.timestamp.getTime() / (60 * 60 * 1000))
      const day = Math.floor(item.timestamp.getTime() / (24 * 60 * 60 * 1000))
      
      hourlyGroups.set(hour, [...(hourlyGroups.get(hour) || []), item])
      dailyGroups.set(day, [...(dailyGroups.get(day) || []), item])
    })

    const hourly = Array.from(hourlyGroups.entries()).map(([hour, items]) => ({
      timestamp: new Date(hour * 60 * 60 * 1000),
      score: this.calculateWeightedScore(items)
    }))

    const daily = Array.from(dailyGroups.entries()).map(([day, items]) => ({
      timestamp: new Date(day * 24 * 60 * 60 * 1000),
      score: this.calculateWeightedScore(items)
    }))

    return { hourly, daily }
  }

  /**
   * Get sentiment weight
   */
  getSentimentWeight(): number {
    return this.config.sentimentWeight
  }

  /**
   * Check if sentiment analysis is enabled
   */
  isEnabled(): boolean {
    return process.env.SENTIMENT_ANALYSIS_ENABLED === 'true'
  }

  /**
   * Get configuration
   */
  getConfig(): SentimentConfig {
    return { ...this.config }
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<SentimentConfig>): void {
    this.config = { ...this.config, ...updates }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
} 