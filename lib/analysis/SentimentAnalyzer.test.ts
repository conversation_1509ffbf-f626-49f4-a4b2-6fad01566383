import { Sentiment<PERSON><PERSON>y<PERSON>, <PERSON>time<PERSON><PERSON><PERSON>, SentimentAnalysisResult } from './SentimentAnalyzer'

describe('SentimentAnalyzer', () => {
  let analyzer: SentimentAnalyzer

  beforeEach(() => {
    // Mock environment variables for testing
    process.env.TWITTER_API_KEY = 'test-twitter-key'
    process.env.TWITTER_API_SECRET = 'test-twitter-secret'
    process.env.TWITTER_BEARER_TOKEN = 'test-bearer-token'
    process.env.REDDIT_CLIENT_ID = 'test-reddit-client-id'
    process.env.REDDIT_CLIENT_SECRET = 'test-reddit-client-secret'
    process.env.REDDIT_USERNAME = 'test-reddit-username'
    process.env.REDDIT_PASSWORD = 'test-reddit-password'
    process.env.NEWS_API_KEY = 'test-news-api-key'
    process.env.SENTIMENT_WEIGHT = '0.3'
    process.env.SENTIMENT_ANALYSIS_ENABLED = 'true'

    analyzer = new SentimentAnalyzer()
  })

  afterEach(() => {
    // Clean up environment variables
    delete process.env.TWITTER_API_KEY
    delete process.env.TWITTER_API_SECRET
    delete process.env.TWITTER_BEARER_TOKEN
    delete process.env.REDDIT_CLIENT_ID
    delete process.env.REDDIT_CLIENT_SECRET
    delete process.env.REDDIT_USERNAME
    delete process.env.REDDIT_PASSWORD
    delete process.env.NEWS_API_KEY
    delete process.env.SENTIMENT_WEIGHT
    delete process.env.SENTIMENT_ANALYSIS_ENABLED
  })

  describe('Constructor', () => {
    it('should initialize with environment variables', () => {
      expect(analyzer).toBeInstanceOf(SentimentAnalyzer)
      expect(analyzer.getSentimentWeight()).toBe(0.3)
      expect(analyzer.isEnabled()).toBe(true)
    })

    it('should handle missing environment variables', () => {
      delete process.env.TWITTER_API_KEY
      delete process.env.SENTIMENT_WEIGHT
      
      const newAnalyzer = new SentimentAnalyzer()
      expect(newAnalyzer.getSentimentWeight()).toBe(0.3) // Default value
    })
  })

  describe('Text Sentiment Analysis', () => {
    it('should identify positive sentiment', () => {
      const positiveText = 'Bitcoin is bullish and going to the moon! This is a great investment opportunity.'
      const result = (analyzer as any).analyzeTextSentiment(positiveText)
      
      expect(result.sentiment).toBe('positive')
      expect(result.score).toBeGreaterThan(0)
    })

    it('should identify negative sentiment', () => {
      const negativeText = 'Bitcoin is crashing and dumping hard. This is a bear market and everyone is selling.'
      const result = (analyzer as any).analyzeTextSentiment(negativeText)
      
      expect(result.sentiment).toBe('negative')
      expect(result.score).toBeLessThan(0)
    })

    it('should identify neutral sentiment', () => {
      const neutralText = 'Bitcoin price is stable today. The market shows normal trading activity.'
      const result = (analyzer as any).analyzeTextSentiment(neutralText)
      
      expect(result.sentiment).toBe('neutral')
      expect(result.score).toBe(0)
    })
  })

  describe('Keyword Extraction', () => {
    it('should extract relevant keywords', () => {
      const text = 'Bitcoin trading volume is high and the crypto market is showing strong liquidity.'
      const keywords = (analyzer as any).extractKeywords(text)
      
      expect(keywords).toContain('bitcoin')
      expect(keywords).toContain('trading')
      expect(keywords).toContain('crypto')
      expect(keywords).toContain('market')
      expect(keywords).toContain('liquidity')
    })

    it('should return empty array for text without keywords', () => {
      const text = 'This is just a regular sentence without any crypto keywords.'
      const keywords = (analyzer as any).extractKeywords(text)
      
      expect(keywords).toEqual([])
    })
  })

  describe('Sentiment Data Processing', () => {
    it('should calculate weighted sentiment score', () => {
      const mockData: SentimentData[] = [
        {
          source: 'twitter',
          timestamp: new Date(),
          sentiment: 'positive',
          score: 0.8,
          volume: 100,
          keywords: ['bitcoin'],
          text: 'Bitcoin is bullish!'
        },
        {
          source: 'twitter',
          timestamp: new Date(),
          sentiment: 'negative',
          score: -0.3,
          volume: 50,
          keywords: ['crash'],
          text: 'Bitcoin is crashing'
        }
      ]

      const score = (analyzer as any).calculateWeightedScore(mockData)
      expect(score).toBeGreaterThan(0) // Should be positive due to higher volume positive tweet
    })

    it('should handle empty data', () => {
      const score = (analyzer as any).calculateWeightedScore([])
      expect(score).toBe(0)
    })
  })

  describe('Trending Topics', () => {
    it('should extract trending topics from data', () => {
      const twitterData: SentimentData[] = [
        {
          source: 'twitter',
          timestamp: new Date(),
          sentiment: 'positive',
          score: 0.5,
          volume: 10,
          keywords: ['bitcoin', 'trading'],
          text: 'Bitcoin trading is great'
        }
      ]

      const redditData: SentimentData[] = [
        {
          source: 'reddit',
          timestamp: new Date(),
          sentiment: 'neutral',
          score: 0,
          volume: 5,
          keywords: ['bitcoin', 'market'],
          text: 'Bitcoin market discussion'
        }
      ]

      const topics = (analyzer as any).extractTrendingTopics(twitterData, redditData, [])
      
      expect(topics).toContain('bitcoin')
      expect(topics.length).toBeGreaterThan(0)
    })
  })

  describe('Confidence Calculation', () => {
    it('should calculate confidence based on data volume', () => {
      const twitterData: SentimentData[] = [
        {
          source: 'twitter',
          timestamp: new Date(),
          sentiment: 'positive',
          score: 0.5,
          volume: 10,
          keywords: ['bitcoin'],
          text: 'Bitcoin is great'
        }
      ]

      const confidence = (analyzer as any).calculateConfidence(twitterData, [], [])
      expect(confidence).toBeGreaterThan(0)
      expect(confidence).toBeLessThanOrEqual(1)
    })

    it('should return 0 confidence for no data', () => {
      const confidence = (analyzer as any).calculateConfidence([], [], [])
      expect(confidence).toBe(0)
    })
  })

  describe('Integration Tests', () => {
    it('should combine sentiment data correctly', () => {
      const twitterData: SentimentData[] = [
        {
          source: 'twitter',
          timestamp: new Date(),
          sentiment: 'positive',
          score: 0.8,
          volume: 100,
          keywords: ['bitcoin'],
          text: 'Bitcoin is bullish!'
        }
      ]

      const redditData: SentimentData[] = [
        {
          source: 'reddit',
          timestamp: new Date(),
          sentiment: 'neutral',
          score: 0,
          volume: 50,
          keywords: ['market'],
          text: 'Market discussion'
        }
      ]

      const newsData: SentimentData[] = []

      const result = (analyzer as any).combineSentimentData(twitterData, redditData, newsData, 'BTC')
      
      expect(result.overallSentiment).toBe('bullish')
      expect(result.sentimentScore).toBeGreaterThan(0)
      expect(result.confidence).toBeGreaterThan(0)
      expect(result.sources.twitter).toEqual(twitterData)
      expect(result.sources.reddit).toEqual(redditData)
      expect(result.sources.news).toEqual(newsData)
      expect(result.volume.twitter).toBe(1)
      expect(result.volume.reddit).toBe(1)
      expect(result.volume.news).toBe(0)
    })
  })

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      // Mock fetch to simulate API errors
      global.fetch = jest.fn().mockRejectedValue(new Error('API Error'))

      const result = await analyzer.analyzeSentiment('BTC')
      
      expect(result.overallSentiment).toBe('neutral')
      expect(result.sentimentScore).toBe(0)
      expect(result.confidence).toBe(0)
      expect(result.sources.twitter).toEqual([])
      expect(result.sources.reddit).toEqual([])
      expect(result.sources.news).toEqual([])
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty text', () => {
      const result = (analyzer as any).analyzeTextSentiment('')
      expect(result.sentiment).toBe('neutral')
      expect(result.score).toBe(0)
    })

    it('should handle very short text', () => {
      const result = (analyzer as any).analyzeTextSentiment('BTC')
      expect(result.sentiment).toBe('neutral')
      expect(result.score).toBe(0)
    })

    it('should handle text with only stop words', () => {
      const result = (analyzer as any).analyzeTextSentiment('the and or but')
      expect(result.sentiment).toBe('neutral')
      expect(result.score).toBe(0)
    })
  })
}) 