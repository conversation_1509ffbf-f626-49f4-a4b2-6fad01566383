export interface SentimentData {
  source: 'twitter' | 'reddit' | 'news'
  timestamp: Date
  sentiment: 'positive' | 'negative' | 'neutral'
  score: number // -1 to 1
  volume: number
  keywords: string[]
  text: string
}

export interface SentimentAnalysisResult {
  overallSentiment: 'bullish' | 'bearish' | 'neutral'
  sentimentScore: number // -100 to 100
  confidence: number // 0 to 1
  sources: {
    twitter: SentimentData[]
    reddit: SentimentData[]
    news: SentimentData[]
  }
  volume: {
    twitter: number
    reddit: number
    news: number
  }
  trendingTopics: string[]
  influencers: {
    twitter: string[]
    reddit: string[]
  }
}

export class SentimentAnalyzer {
  private twitterApiKey: string
  private twitterApiSecret: string
  private twitterBearerToken: string
  private redditClientId: string
  private redditClientSecret: string
  private redditUsername: string
  private redditPassword: string
  private newsApiKey: string
  private sentimentWeight: number

  constructor() {
    this.twitterApiKey = process.env.TWITTER_API_KEY || ''
    this.twitterApiSecret = process.env.TWITTER_API_SECRET || ''
    this.twitterBearerToken = process.env.TWITTER_BEARER_TOKEN || ''
    this.redditClientId = process.env.REDDIT_CLIENT_ID || ''
    this.redditClientSecret = process.env.REDDIT_CLIENT_SECRET || ''
    this.redditUsername = process.env.REDDIT_USERNAME || ''
    this.redditPassword = process.env.REDDIT_PASSWORD || ''
    this.newsApiKey = process.env.NEWS_API_KEY || ''
    this.sentimentWeight = parseFloat(process.env.SENTIMENT_WEIGHT || '0.3')
  }

  /**
   * Analyze sentiment for a specific cryptocurrency
   */
  async analyzeSentiment(symbol: string): Promise<SentimentAnalysisResult> {
    console.log(`🔍 Analyzing sentiment for ${symbol}...`)

    try {
      // Collect data from all sources
      const [twitterData, redditData, newsData] = await Promise.all([
        this.getTwitterSentiment(symbol),
        this.getRedditSentiment(symbol),
        this.getNewsSentiment(symbol)
      ])

      // Combine and analyze sentiment
      const result = this.combineSentimentData(twitterData, redditData, newsData, symbol)

      console.log(`✅ Sentiment analysis complete for ${symbol}`)
      return result

    } catch (error) {
      console.error(`❌ Sentiment analysis failed for ${symbol}:`, error)
      throw error
    }
  }

  /**
   * Get Twitter sentiment data
   */
  private async getTwitterSentiment(symbol: string): Promise<SentimentData[]> {
    if (!this.twitterBearerToken) {
      console.warn('⚠️ Twitter API not configured')
      return []
    }

    try {
      // Search for tweets about the cryptocurrency
      const query = `${symbol} crypto`
      const url = `https://api.twitter.com/2/tweets/search/recent?query=${encodeURIComponent(query)}&max_results=100&tweet.fields=created_at,public_metrics,author_id&user.fields=username,verified,public_metrics`

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${this.twitterBearerToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Twitter API error: ${response.status}`)
      }

      const data = await response.json()
      const tweets = data.data || []

      // Analyze sentiment for each tweet
      const sentimentData: SentimentData[] = tweets.map((tweet: any) => {
        const sentiment = this.analyzeTextSentiment(tweet.text)
        return {
          source: 'twitter' as const,
          timestamp: new Date(tweet.created_at),
          sentiment: sentiment.sentiment,
          score: sentiment.score,
          volume: tweet.public_metrics?.retweet_count || 0,
          keywords: this.extractKeywords(tweet.text),
          text: tweet.text
        }
      })

      return sentimentData

    } catch (error) {
      console.error('❌ Twitter sentiment analysis failed:', error)
      return []
    }
  }

  /**
   * Get Reddit sentiment data
   */
  private async getRedditSentiment(symbol: string): Promise<SentimentData[]> {
    if (!this.redditClientId || !this.redditClientSecret) {
      console.warn('⚠️ Reddit API not configured')
      return []
    }

    try {
      // Get access token
      const tokenResponse = await fetch('https://www.reddit.com/api/v1/access_token', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${Buffer.from(`${this.redditClientId}:${this.redditClientSecret}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `grant_type=password&username=${this.redditUsername}&password=${this.redditPassword}`
      })

      if (!tokenResponse.ok) {
        throw new Error('Failed to get Reddit access token')
      }

      const tokenData = await tokenResponse.json()
      const accessToken = tokenData.access_token

      // Search Reddit posts
      const subreddits = ['cryptocurrency', 'bitcoin', 'ethereum', 'altcoin']
      const allPosts: SentimentData[] = []

      for (const subreddit of subreddits) {
        const url = `https://oauth.reddit.com/r/${subreddit}/search?q=${symbol}&sort=hot&t=day&limit=25`
        
        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'User-Agent': 'CryptoAgentPro/1.0'
          }
        })

        if (response.ok) {
          const data = await response.json()
          const posts = data.data?.children || []

          posts.forEach((post: any) => {
            const sentiment = this.analyzeTextSentiment(post.data.title + ' ' + post.data.selftext)
            allPosts.push({
              source: 'reddit' as const,
              timestamp: new Date(post.data.created_utc * 1000),
              sentiment: sentiment.sentiment,
              score: sentiment.score,
              volume: post.data.score || 0,
              keywords: this.extractKeywords(post.data.title + ' ' + post.data.selftext),
              text: post.data.title + ' ' + post.data.selftext
            })
          })
        }
      }

      return allPosts

    } catch (error) {
      console.error('❌ Reddit sentiment analysis failed:', error)
      return []
    }
  }

  /**
   * Get news sentiment data
   */
  private async getNewsSentiment(symbol: string): Promise<SentimentData[]> {
    if (!this.newsApiKey) {
      console.warn('⚠️ News API not configured')
      return []
    }

    try {
      const query = `${symbol} cryptocurrency`
      const url = `https://newsapi.org/v2/everything?q=${encodeURIComponent(query)}&sortBy=publishedAt&language=en&pageSize=50&apiKey=${this.newsApiKey}`

      const response = await fetch(url)

      if (!response.ok) {
        throw new Error(`News API error: ${response.status}`)
      }

      const data = await response.json()
      const articles = data.articles || []

      const sentimentData: SentimentData[] = articles.map((article: any) => {
        const sentiment = this.analyzeTextSentiment(article.title + ' ' + article.description)
        return {
          source: 'news' as const,
          timestamp: new Date(article.publishedAt),
          sentiment: sentiment.sentiment,
          score: sentiment.score,
          volume: 1, // News articles don't have volume metrics
          keywords: this.extractKeywords(article.title + ' ' + article.description),
          text: article.title + ' ' + article.description
        }
      })

      return sentimentData

    } catch (error) {
      console.error('❌ News sentiment analysis failed:', error)
      return []
    }
  }

  /**
   * Analyze text sentiment using simple keyword analysis
   * In production, you'd use a more sophisticated NLP model
   */
  private analyzeTextSentiment(text: string): { sentiment: 'positive' | 'negative' | 'neutral', score: number } {
    const positiveWords = [
      'bullish', 'moon', 'pump', 'rally', 'surge', 'gain', 'profit', 'buy', 'hodl',
      'diamond hands', 'to the moon', 'mooning', 'green', 'up', 'rise', 'strong',
      'breakout', 'breakthrough', 'adoption', 'institutional', 'partnership'
    ]

    const negativeWords = [
      'bearish', 'dump', 'crash', 'sell', 'panic', 'fear', 'loss', 'red', 'down',
      'correction', 'bear market', 'dump', 'sell off', 'decline', 'weak', 'breakdown',
      'regulation', 'ban', 'hack', 'scam', 'bubble'
    ]

    const lowerText = text.toLowerCase()
    let positiveCount = 0
    let negativeCount = 0

    positiveWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi')
      const matches = lowerText.match(regex)
      if (matches) positiveCount += matches.length
    })

    negativeWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi')
      const matches = lowerText.match(regex)
      if (matches) negativeCount += matches.length
    })

    const totalWords = text.split(' ').length
    const positiveScore = positiveCount / totalWords
    const negativeScore = negativeCount / totalWords
    const netScore = positiveScore - negativeScore

    if (netScore > 0.01) {
      return { sentiment: 'positive', score: Math.min(netScore * 100, 1) }
    } else if (netScore < -0.01) {
      return { sentiment: 'negative', score: Math.max(netScore * 100, -1) }
    } else {
      return { sentiment: 'neutral', score: 0 }
    }
  }

  /**
   * Extract keywords from text
   */
  private extractKeywords(text: string): string[] {
    const keywords = [
      'bitcoin', 'ethereum', 'crypto', 'blockchain', 'defi', 'nft', 'altcoin',
      'trading', 'investment', 'market', 'price', 'volume', 'liquidity',
      'mining', 'staking', 'yield', 'apy', 'gas', 'fees'
    ]

    const lowerText = text.toLowerCase()
    const foundKeywords: string[] = []

    keywords.forEach(keyword => {
      if (lowerText.includes(keyword)) {
        foundKeywords.push(keyword)
      }
    })

    return foundKeywords
  }

  /**
   * Combine sentiment data from all sources
   */
  private combineSentimentData(
    twitterData: SentimentData[],
    redditData: SentimentData[],
    newsData: SentimentData[],
    symbol: string
  ): SentimentAnalysisResult {
    // Calculate weighted sentiment scores
    const twitterScore = this.calculateWeightedScore(twitterData)
    const redditScore = this.calculateWeightedScore(redditData)
    const newsScore = this.calculateWeightedScore(newsData)

    // Overall sentiment score (-100 to 100)
    const overallScore = (twitterScore * 0.4 + redditScore * 0.4 + newsScore * 0.2)

    // Determine overall sentiment
    let overallSentiment: 'bullish' | 'bearish' | 'neutral'
    if (overallScore > 20) {
      overallSentiment = 'bullish'
    } else if (overallScore < -20) {
      overallSentiment = 'bearish'
    } else {
      overallSentiment = 'neutral'
    }

    // Calculate confidence based on volume and consistency
    const confidence = this.calculateConfidence(twitterData, redditData, newsData)

    // Extract trending topics
    const trendingTopics = this.extractTrendingTopics(twitterData, redditData, newsData)

    // Identify influencers
    const influencers = this.identifyInfluencers(twitterData, redditData)

    return {
      overallSentiment,
      sentimentScore: overallScore,
      confidence,
      sources: {
        twitter: twitterData,
        reddit: redditData,
        news: newsData
      },
      volume: {
        twitter: twitterData.length,
        reddit: redditData.length,
        news: newsData.length
      },
      trendingTopics,
      influencers
    }
  }

  /**
   * Calculate weighted sentiment score
   */
  private calculateWeightedScore(data: SentimentData[]): number {
    if (data.length === 0) return 0

    const totalVolume = data.reduce((sum, item) => sum + item.volume, 0)
    if (totalVolume === 0) return 0

    const weightedScore = data.reduce((sum, item) => {
      const weight = item.volume / totalVolume
      return sum + (item.score * weight)
    }, 0)

    return weightedScore * 100 // Convert to -100 to 100 scale
  }

  /**
   * Calculate confidence based on data quality
   */
  private calculateConfidence(
    twitterData: SentimentData[],
    redditData: SentimentData[],
    newsData: SentimentData[]
  ): number {
    const totalData = twitterData.length + redditData.length + newsData.length
    if (totalData === 0) return 0

    // Base confidence on data volume
    let confidence = Math.min(totalData / 100, 1)

    // Boost confidence if data is recent
    const now = new Date()
    const recentData = [...twitterData, ...redditData, ...newsData].filter(
      item => now.getTime() - item.timestamp.getTime() < 24 * 60 * 60 * 1000 // Last 24 hours
    )

    if (recentData.length > 0) {
      confidence += 0.2
    }

    return Math.min(confidence, 1)
  }

  /**
   * Extract trending topics
   */
  private extractTrendingTopics(
    twitterData: SentimentData[],
    redditData: SentimentData[],
    newsData: SentimentData[]
  ): string[] {
    const allKeywords = [
      ...twitterData.flatMap(item => item.keywords),
      ...redditData.flatMap(item => item.keywords),
      ...newsData.flatMap(item => item.keywords)
    ]

    const keywordCount: Record<string, number> = {}
    allKeywords.forEach(keyword => {
      keywordCount[keyword] = (keywordCount[keyword] || 0) + 1
    })

    return Object.entries(keywordCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([keyword]) => keyword)
  }

  /**
   * Identify influencers
   */
  private identifyInfluencers(
    twitterData: SentimentData[],
    redditData: SentimentData[]
  ): { twitter: string[], reddit: string[] } {
    // This is a simplified implementation
    // In production, you'd analyze user metrics and engagement
    return {
      twitter: [],
      reddit: []
    }
  }

  /**
   * Get sentiment weight for trading decisions
   */
  getSentimentWeight(): number {
    return this.sentimentWeight
  }

  /**
   * Check if sentiment analysis is enabled
   */
  isEnabled(): boolean {
    return process.env.SENTIMENT_ANALYSIS_ENABLED === 'true'
  }
} 