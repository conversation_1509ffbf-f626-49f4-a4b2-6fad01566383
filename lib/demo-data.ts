/**
 * Demo Data for CryptoAgent Pro
 * 
 * This provides mock data when Supabase is not configured
 * Perfect for testing and development
 */

export interface TradingAgent {
  id: string;
  name: string;
  specialization: string;
  status: 'ACTIVE' | 'THINKING' | 'TRADING' | 'ERROR' | 'IDLE' | 'PAUSED';
  performance: number;
  currentTask?: string;
  ai_provider: string;
  ai_model: string;
  last_activity: string;
  created_at: string;
}

export interface TradingPosition {
  id: string;
  agent_id: string;
  exchange: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  size: number;
  entry_price: number;
  current_price: number;
  pnl: number;
  pnl_percentage: number;
  status: 'OPEN' | 'CLOSED' | 'CANCELLED';
  opened_at: string;
  closed_at?: string;
}

export interface TradingSignal {
  id: string;
  agent_id: string;
  symbol: string;
  signal_type: 'BUY' | 'SELL' | 'HOLD';
  strength: number;
  price_target?: number;
  stop_loss?: number;
  reasoning: string;
  created_at: string;
  expires_at?: string;
  executed: boolean;
}

export interface AIAnalysis {
  id: string;
  agent_id: string;
  analysis_type: string;
  symbol: string;
  timeframe: string;
  confidence_score: number;
  recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL';
  ai_model_used: string;
  created_at: string;
  analysis_result: any;
}

// Demo Trading Agents
export const demoAgents: TradingAgent[] = [
  {
    id: '1',
    name: 'Technical Analyst Pro',
    specialization: 'technical_analysis',
    status: 'ACTIVE',
    performance: 87.5,
    currentTask: 'Analyzing BTC/USDT breakout patterns on 4H timeframe',
    ai_provider: 'lm_studio',
    ai_model: 'deepseek-coder-33b-instruct',
    last_activity: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // 2 minutes ago
    created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 1 day ago
  },
  {
    id: '2',
    name: 'Sentiment Monitor',
    specialization: 'sentiment_analysis',
    status: 'THINKING',
    performance: 82.3,
    currentTask: 'Processing social media sentiment for ETH ecosystem tokens',
    ai_provider: 'openai',
    ai_model: 'gpt-4',
    last_activity: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
    created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: '3',
    name: 'Risk Manager',
    specialization: 'risk_management',
    status: 'ACTIVE',
    performance: 94.1,
    currentTask: 'Monitoring portfolio exposure and correlation risks',
    ai_provider: 'anthropic',
    ai_model: 'claude-3-sonnet',
    last_activity: new Date(Date.now() - 1 * 60 * 1000).toISOString(), // 1 minute ago
    created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: '4',
    name: 'Market Scanner',
    specialization: 'market_scanning',
    status: 'TRADING',
    performance: 79.8,
    currentTask: 'Executing AVAX long position based on DeFi momentum',
    ai_provider: 'lm_studio',
    ai_model: 'llama-3.1-70b-instruct',
    last_activity: new Date(Date.now() - 30 * 1000).toISOString(), // 30 seconds ago
    created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: '5',
    name: 'Arbitrage Hunter',
    specialization: 'arbitrage',
    status: 'PAUSED',
    performance: 91.2,
    currentTask: 'Waiting for profitable arbitrage opportunities',
    ai_provider: 'openai',
    ai_model: 'gpt-4-turbo',
    last_activity: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 minutes ago
    created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
  }
];

// Demo Trading Positions
export const demoPositions: TradingPosition[] = [
  {
    id: '1',
    agent_id: '1',
    exchange: 'binance',
    symbol: 'BTC/USDT',
    side: 'BUY',
    size: 0.25,
    entry_price: 67500.00,
    current_price: 68240.50,
    pnl: 185.125,
    pnl_percentage: 1.10,
    status: 'OPEN',
    opened_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString() // 4 hours ago
  },
  {
    id: '2',
    agent_id: '2',
    exchange: 'bybit',
    symbol: 'ETH/USDT',
    side: 'BUY',
    size: 5.8,
    entry_price: 3420.75,
    current_price: 3456.20,
    pnl: 205.61,
    pnl_percentage: 1.04,
    status: 'OPEN',
    opened_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2 hours ago
  },
  {
    id: '3',
    agent_id: '4',
    exchange: 'binance',
    symbol: 'AVAX/USDT',
    side: 'BUY',
    size: 120.5,
    entry_price: 28.45,
    current_price: 29.12,
    pnl: 80.735,
    pnl_percentage: 2.36,
    status: 'OPEN',
    opened_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString() // 1 hour ago
  },
  {
    id: '4',
    agent_id: '1',
    exchange: 'bybit',
    symbol: 'SOL/USDT',
    side: 'SELL',
    size: 15.2,
    entry_price: 142.80,
    current_price: 141.95,
    pnl: 12.92,
    pnl_percentage: 0.60,
    status: 'OPEN',
    opened_at: new Date(Date.now() - 30 * 60 * 1000).toISOString() // 30 minutes ago
  }
];

// Demo Trading Signals
export const demoSignals: TradingSignal[] = [
  {
    id: '1',
    agent_id: '1',
    symbol: 'BTC/USDT',
    signal_type: 'BUY',
    strength: 85.5,
    price_target: 72000,
    stop_loss: 66500,
    reasoning: 'Strong bullish divergence on RSI with volume confirmation. Breaking above key resistance at 68k.',
    created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
    executed: false
  },
  {
    id: '2',
    agent_id: '2',
    symbol: 'ETH/USDT',
    signal_type: 'HOLD',
    strength: 72.3,
    reasoning: 'Mixed sentiment signals. Positive DeFi developments but macro uncertainty. Maintaining current position.',
    created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
    executed: true
  },
  {
    id: '3',
    agent_id: '4',
    symbol: 'LINK/USDT',
    signal_type: 'BUY',
    strength: 91.2,
    price_target: 18.50,
    stop_loss: 15.20,
    reasoning: 'Major partnership announcement imminent. Whale accumulation detected. Technical setup very bullish.',
    created_at: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
    executed: false
  }
];

// Demo AI Analysis
export const demoAnalysis: AIAnalysis[] = [
  {
    id: '1',
    agent_id: '1',
    analysis_type: 'technical_analysis',
    symbol: 'BTC/USDT',
    timeframe: '4h',
    confidence_score: 87.5,
    recommendation: 'BUY',
    ai_model_used: 'deepseek-coder-33b-instruct',
    created_at: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
    analysis_result: {
      indicators: {
        rsi: 58.2,
        macd: 'bullish_crossover',
        bollinger: 'upper_band_test',
        volume: 'above_average'
      },
      patterns: ['ascending_triangle', 'bullish_flag'],
      support_levels: [67000, 66500, 65800],
      resistance_levels: [68500, 69200, 70000]
    }
  },
  {
    id: '2',
    agent_id: '2',
    analysis_type: 'sentiment_analysis',
    symbol: 'ETH/USDT',
    timeframe: '1d',
    confidence_score: 76.8,
    recommendation: 'NEUTRAL',
    ai_model_used: 'gpt-4',
    created_at: new Date(Date.now() - 20 * 60 * 1000).toISOString(),
    analysis_result: {
      social_sentiment: 0.65,
      news_sentiment: 0.72,
      whale_activity: 'moderate_buying',
      fear_greed_index: 58,
      trending_topics: ['ethereum_upgrade', 'defi_tvl', 'institutional_adoption']
    }
  }
];

// Portfolio Summary
export const demoPortfolioSummary = {
  totalValue: 34241.50,
  totalPnL: 1240.75,
  currency: 'USD',
  openPositions: demoPositions.length,
  winRate: 78.5,
  sharpeRatio: 1.85,
  maxDrawdown: -8.2,
  lastUpdated: new Date().toISOString(),
  assets: {
    BTC: { amount: 0.25, value: 17060.125, percentage: 49.8 },
    ETH: { amount: 5.8, value: 20045.96, percentage: 58.5 },
    AVAX: { amount: 120.5, value: 3508.46, percentage: 10.2 },
    SOL: { amount: 15.2, value: 2157.64, percentage: 6.3 },
    USDT: { amount: 1468.35, value: 1468.35, percentage: 4.3 }
  }
};

// Engine Status
export const demoEngineStatus = {
  running: true,
  autoMode: false,
  uptime: '2h 34m',
  activeAgents: 4,
  totalTrades: 127,
  successRate: 78.5,
  lastUpdate: new Date().toISOString()
};
