import * as ccxt from 'ccxt'
import { exchangeManager } from '../exchanges/ExchangeManager'

export interface MarketDataUpdate {
  symbol: string
  exchange: string
  timestamp: string
  price: number
  volume24h: number
  change24h: number
  changePercent24h: number
  high24h: number
  low24h: number
  bid: number
  ask: number
  spread: number
  lastUpdate: string
}

export interface OrderBookUpdate {
  symbol: string
  exchange: string
  timestamp: string
  bids: [number, number][] // [price, amount]
  asks: [number, number][] // [price, amount]
  lastUpdate: string
}

export interface TradeUpdate {
  symbol: string
  exchange: string
  timestamp: string
  price: number
  amount: number
  side: 'buy' | 'sell'
  tradeId: string
}

export class MarketDataWebSocketManager {
  private connections: Map<string, WebSocket> = new Map()
  private subscriptions: Map<string, Set<string>> = new Map() // exchange -> symbols
  private updateInterval: NodeJS.Timeout | null = null
  private listeners: Map<string, Function[]> = new Map()
  private orderBookListeners: Map<string, Function[]> = new Map()
  private tradeListeners: Map<string, Function[]> = new Map()
  private reconnectAttempts: Map<string, number> = new Map()
  private maxReconnectAttempts = 5

  constructor() {
    this.startPeriodicUpdates()
  }

  // Connect to exchange WebSocket feeds
  async connectToExchange(exchangeId: string) {
    try {
      const wsUrl = this.getWebSocketUrl(exchangeId)
      if (!wsUrl) {
        console.log(`⚠️  WebSocket not supported for ${exchangeId}, using REST API fallback`)
        return
      }

      const ws = new WebSocket(wsUrl)
      
      ws.onopen = () => {
        console.log(`✅ Market data WebSocket connected to ${exchangeId}`)
        this.reconnectAttempts.set(exchangeId, 0)
        
        // Subscribe to existing symbols for this exchange
        const symbols = this.subscriptions.get(exchangeId)
        if (symbols) {
          symbols.forEach(symbol => {
            this.sendSubscription(exchangeId, symbol, ws)
          })
        }
      }

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          this.handleExchangeMessage(exchangeId, data)
        } catch (error) {
          console.error(`❌ Error parsing ${exchangeId} WebSocket message:`, error)
        }
      }

      ws.onclose = () => {
        console.log(`🔌 ${exchangeId} WebSocket disconnected`)
        this.connections.delete(exchangeId)
        this.scheduleReconnect(exchangeId)
      }

      ws.onerror = (error) => {
        console.error(`❌ ${exchangeId} WebSocket error:`, error)
      }

      this.connections.set(exchangeId, ws)

    } catch (error) {
      console.error(`❌ Failed to connect to ${exchangeId} WebSocket:`, error)
    }
  }

  // Subscribe to market data for a symbol
  subscribe(symbol: string, exchange: string, callback: (update: MarketDataUpdate) => void) {
    const key = `${exchange}:${symbol}`
    
    if (!this.listeners.has(key)) {
      this.listeners.set(key, [])
    }
    this.listeners.get(key)!.push(callback)

    // Add to subscriptions
    if (!this.subscriptions.has(exchange)) {
      this.subscriptions.set(exchange, new Set())
    }
    this.subscriptions.get(exchange)!.add(symbol)

    // Connect to exchange if not already connected
    if (!this.connections.has(exchange)) {
      this.connectToExchange(exchange)
    } else {
      // Send subscription if already connected
      const ws = this.connections.get(exchange)
      if (ws && ws.readyState === WebSocket.OPEN) {
        this.sendSubscription(exchange, symbol, ws)
      }
    }

    console.log(`📡 Subscribed to market data for ${symbol} on ${exchange}`)
  }

  // Subscribe to order book updates
  subscribeOrderBook(symbol: string, exchange: string, callback: (update: OrderBookUpdate) => void) {
    const key = `${exchange}:${symbol}:orderbook`
    
    if (!this.orderBookListeners.has(key)) {
      this.orderBookListeners.set(key, [])
    }
    this.orderBookListeners.get(key)!.push(callback)

    console.log(`📊 Subscribed to order book for ${symbol} on ${exchange}`)
  }

  // Subscribe to trade updates
  subscribeTrades(symbol: string, exchange: string, callback: (update: TradeUpdate) => void) {
    const key = `${exchange}:${symbol}:trades`
    
    if (!this.tradeListeners.has(key)) {
      this.tradeListeners.set(key, [])
    }
    this.tradeListeners.get(key)!.push(callback)

    console.log(`💱 Subscribed to trades for ${symbol} on ${exchange}`)
  }

  private getWebSocketUrl(exchangeId: string): string | null {
    const urls: Record<string, string> = {
      'binance': 'wss://stream.binance.com:9443/ws/ticker',
      'mexc': 'wss://wbs.mexc.com/ws',
      'kucoin': 'wss://ws-api.kucoin.com/endpoint',
      'bybit': 'wss://stream.bybit.com/v5/public/spot'
    }
    
    return urls[exchangeId] || null
  }

  private sendSubscription(exchangeId: string, symbol: string, ws: WebSocket) {
    let subscriptionMessage: any

    switch (exchangeId) {
      case 'binance':
        subscriptionMessage = {
          method: 'SUBSCRIBE',
          params: [`${symbol.toLowerCase()}@ticker`],
          id: Date.now()
        }
        break
      case 'mexc':
        subscriptionMessage = {
          method: 'SUBSCRIPTION',
          params: [`<EMAIL>@${symbol}`]
        }
        break
      case 'bybit':
        subscriptionMessage = {
          op: 'subscribe',
          args: [`tickers.${symbol}`]
        }
        break
      default:
        console.log(`⚠️  Subscription format not implemented for ${exchangeId}`)
        return
    }

    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(subscriptionMessage))
    }
  }

  private handleExchangeMessage(exchangeId: string, data: any) {
    try {
      const update = this.parseExchangeData(exchangeId, data)
      if (update) {
        this.notifyMarketDataListeners(update)
      }
    } catch (error) {
      console.error(`❌ Error handling ${exchangeId} message:`, error)
    }
  }

  private parseExchangeData(exchangeId: string, data: any): MarketDataUpdate | null {
    try {
      switch (exchangeId) {
        case 'binance':
          if (data.e === '24hrTicker') {
            return {
              symbol: data.s,
              exchange: exchangeId,
              timestamp: new Date().toISOString(),
              price: parseFloat(data.c),
              volume24h: parseFloat(data.v),
              change24h: parseFloat(data.P),
              changePercent24h: parseFloat(data.P),
              high24h: parseFloat(data.h),
              low24h: parseFloat(data.l),
              bid: parseFloat(data.b),
              ask: parseFloat(data.a),
              spread: parseFloat(data.a) - parseFloat(data.b),
              lastUpdate: new Date(data.E).toISOString()
            }
          }
          break
        
        case 'bybit':
          if (data.topic && data.topic.startsWith('tickers.')) {
            const tickerData = data.data
            return {
              symbol: tickerData.symbol,
              exchange: exchangeId,
              timestamp: new Date().toISOString(),
              price: parseFloat(tickerData.lastPrice),
              volume24h: parseFloat(tickerData.volume24h),
              change24h: parseFloat(tickerData.price24hPcnt),
              changePercent24h: parseFloat(tickerData.price24hPcnt) * 100,
              high24h: parseFloat(tickerData.highPrice24h),
              low24h: parseFloat(tickerData.lowPrice24h),
              bid: parseFloat(tickerData.bid1Price),
              ask: parseFloat(tickerData.ask1Price),
              spread: parseFloat(tickerData.ask1Price) - parseFloat(tickerData.bid1Price),
              lastUpdate: new Date(tickerData.ts).toISOString()
            }
          }
          break
      }
    } catch (error) {
      console.error(`❌ Error parsing ${exchangeId} data:`, error)
    }
    
    return null
  }

  private notifyMarketDataListeners(update: MarketDataUpdate) {
    const key = `${update.exchange}:${update.symbol}`
    const callbacks = this.listeners.get(key)
    
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(update)
        } catch (error) {
          console.error('❌ Error in market data callback:', error)
        }
      })
    }
  }

  private scheduleReconnect(exchangeId: string) {
    const attempts = this.reconnectAttempts.get(exchangeId) || 0
    
    if (attempts < this.maxReconnectAttempts) {
      const delay = Math.min(1000 * Math.pow(2, attempts), 30000) // Exponential backoff, max 30s
      
      setTimeout(() => {
        console.log(`🔄 Attempting to reconnect to ${exchangeId} (attempt ${attempts + 1})`)
        this.reconnectAttempts.set(exchangeId, attempts + 1)
        this.connectToExchange(exchangeId)
      }, delay)
    } else {
      console.error(`❌ Max reconnection attempts reached for ${exchangeId}`)
    }
  }

  private startPeriodicUpdates() {
    // Fallback REST API updates every 30 seconds for subscribed symbols
    this.updateInterval = setInterval(async () => {
      await this.fetchRestUpdates()
    }, 30000)
  }

  private async fetchRestUpdates() {
    for (const [exchange, symbols] of this.subscriptions) {
      if (this.connections.has(exchange) && 
          this.connections.get(exchange)?.readyState === WebSocket.OPEN) {
        continue // Skip if WebSocket is active
      }

      // Use REST API as fallback
      for (const symbol of symbols) {
        try {
          const update = await this.fetchRestMarketData(exchange, symbol)
          if (update) {
            this.notifyMarketDataListeners(update)
          }
        } catch (error) {
          console.error(`❌ Failed to fetch REST data for ${symbol} on ${exchange}:`, error)
        }
      }
    }
  }

  private async fetchRestMarketData(exchangeId: string, symbol: string): Promise<MarketDataUpdate | null> {
    try {
      // This would use the exchangeManager to fetch ticker data
      // Implementation depends on your exchange setup
      console.log(`🔄 Fetching REST data for ${symbol} on ${exchangeId}`)
      
      // Placeholder - you would implement actual REST API calls here
      return null
    } catch (error) {
      console.error(`❌ REST API error for ${symbol} on ${exchangeId}:`, error)
      return null
    }
  }

  disconnect() {
    // Close all WebSocket connections
    this.connections.forEach((ws, exchangeId) => {
      ws.close()
      console.log(`🔌 Disconnected from ${exchangeId}`)
    })
    
    this.connections.clear()
    this.subscriptions.clear()
    this.listeners.clear()
    this.orderBookListeners.clear()
    this.tradeListeners.clear()
    
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
    }
    
    console.log('🔌 Market data WebSocket manager disconnected')
  }

  getStatus() {
    const status: Record<string, any> = {}
    
    this.connections.forEach((ws, exchangeId) => {
      status[exchangeId] = {
        connected: ws.readyState === WebSocket.OPEN,
        subscriptions: Array.from(this.subscriptions.get(exchangeId) || []),
        reconnectAttempts: this.reconnectAttempts.get(exchangeId) || 0
      }
    })
    
    return {
      exchanges: status,
      totalSubscriptions: Array.from(this.subscriptions.values())
        .reduce((total, symbols) => total + symbols.size, 0),
      restFallbackActive: this.updateInterval !== null
    }
  }
}

// Singleton instance for global use
export const marketDataWebSocket = new MarketDataWebSocketManager()
