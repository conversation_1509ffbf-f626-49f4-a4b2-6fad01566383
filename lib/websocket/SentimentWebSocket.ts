import { SentimentAnalysisAgentV3 } from '../agents/SentimentAnalysisAgentV3'

export interface SentimentUpdate {
  symbol: string
  timestamp: string
  sentimentScore: number
  recommendation: string
  confidence: number
  socialVolume: string
  marketMood: string
  changeFromPrevious: number
}

export class SentimentWebSocketManager {
  private ws: WebSocket | null = null
  private subscriptions: Set<string> = new Set()
  private updateInterval: NodeJS.Timeout | null = null
  private agent: SentimentAnalysisAgentV3
  private listeners: Map<string, Function[]> = new Map()

  constructor() {
    this.agent = new SentimentAnalysisAgentV3()
  }

  connect(url: string = 'wss://api.cryptoagent.pro/sentiment') {
    try {
      this.ws = new WebSocket(url)
      
      this.ws.onopen = () => {
        console.log('✅ Sentiment WebSocket connected')
        this.startPeriodicUpdates()
      }

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          this.handleMessage(data)
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error)
        }
      }

      this.ws.onclose = () => {
        console.log('🔌 Sentiment WebSocket disconnected')
        this.stopPeriodicUpdates()
        // Auto-reconnect after 5 seconds
        setTimeout(() => this.connect(url), 5000)
      }

      this.ws.onerror = (error) => {
        console.error('❌ Sentiment WebSocket error:', error)
      }

    } catch (error) {
      console.error('❌ Failed to connect to sentiment WebSocket:', error)
      // Fallback to periodic updates without WebSocket
      this.startPeriodicUpdates()
    }
  }

  subscribe(symbol: string, callback: (update: SentimentUpdate) => void) {
    this.subscriptions.add(symbol.toUpperCase())
    
    if (!this.listeners.has(symbol)) {
      this.listeners.set(symbol, [])
    }
    this.listeners.get(symbol)!.push(callback)

    // Send subscription message if WebSocket is connected
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'subscribe',
        symbol: symbol.toUpperCase()
      }))
    }

    console.log(`📡 Subscribed to sentiment updates for ${symbol}`)
  }

  unsubscribe(symbol: string, callback?: Function) {
    if (callback) {
      const callbacks = this.listeners.get(symbol)
      if (callbacks) {
        const index = callbacks.indexOf(callback)
        if (index > -1) {
          callbacks.splice(index, 1)
        }
        if (callbacks.length === 0) {
          this.listeners.delete(symbol)
          this.subscriptions.delete(symbol.toUpperCase())
        }
      }
    } else {
      this.listeners.delete(symbol)
      this.subscriptions.delete(symbol.toUpperCase())
    }

    // Send unsubscribe message if WebSocket is connected
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'unsubscribe',
        symbol: symbol.toUpperCase()
      }))
    }

    console.log(`📡 Unsubscribed from sentiment updates for ${symbol}`)
  }

  private handleMessage(data: any) {
    switch (data.type) {
      case 'sentiment_update':
        this.notifyListeners(data.symbol, data.update)
        break
      case 'bulk_update':
        data.updates.forEach((update: any) => {
          this.notifyListeners(update.symbol, update)
        })
        break
      case 'error':
        console.error('❌ Sentiment WebSocket error:', data.message)
        break
      default:
        console.log('📨 Unknown message type:', data.type)
    }
  }

  private notifyListeners(symbol: string, update: SentimentUpdate) {
    const callbacks = this.listeners.get(symbol)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(update)
        } catch (error) {
          console.error('❌ Error in sentiment update callback:', error)
        }
      })
    }
  }

  private startPeriodicUpdates() {
    // Update sentiment every 2 minutes for subscribed symbols
    this.updateInterval = setInterval(async () => {
      if (this.subscriptions.size === 0) return

      console.log(`🔄 Updating sentiment for ${this.subscriptions.size} symbols...`)
      
      for (const symbol of this.subscriptions) {
        try {
          const analysis = await this.agent.analyze(symbol)
          const update: SentimentUpdate = {
            symbol,
            timestamp: new Date().toISOString(),
            sentimentScore: analysis.data.sentimentScore,
            recommendation: analysis.data.recommendation,
            confidence: analysis.data.confidence,
            socialVolume: analysis.data.socialVolume,
            marketMood: analysis.data.marketMood,
            changeFromPrevious: 0 // Would calculate from previous analysis
          }

          this.notifyListeners(symbol, update)

          // Small delay between symbols to avoid overwhelming APIs
          await new Promise(resolve => setTimeout(resolve, 500))

        } catch (error) {
          console.error(`❌ Failed to update sentiment for ${symbol}:`, error)
        }
      }
    }, 2 * 60 * 1000) // 2 minutes
  }

  private stopPeriodicUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval)
      this.updateInterval = null
    }
  }

  disconnect() {
    this.stopPeriodicUpdates()
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }

    this.subscriptions.clear()
    this.listeners.clear()
    
    console.log('🔌 Sentiment WebSocket manager disconnected')
  }

  getStatus() {
    return {
      connected: this.ws?.readyState === WebSocket.OPEN,
      subscriptions: Array.from(this.subscriptions),
      listenersCount: this.listeners.size,
      periodicUpdatesActive: this.updateInterval !== null
    }
  }
}

// Singleton instance for global use
export const sentimentWebSocket = new SentimentWebSocketManager()