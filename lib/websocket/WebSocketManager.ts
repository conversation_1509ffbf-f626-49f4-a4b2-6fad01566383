import { sentimentWebSocket, SentimentUpdate } from './SentimentWebSocket'
import { marketDataWebSocket, MarketDataUpdate, OrderBookUpdate, TradeUpdate } from './MarketDataWebSocket'

export interface CombinedMarketUpdate {
  symbol: string
  timestamp: string
  marketData?: MarketDataUpdate
  sentiment?: SentimentUpdate
  exchanges: string[]
}

export interface WebSocketStatus {
  sentiment: {
    connected: boolean
    subscriptions: string[]
    listenersCount: number
    periodicUpdatesActive: boolean
  }
  marketData: {
    exchanges: Record<string, {
      connected: boolean
      subscriptions: string[]
      reconnectAttempts: number
    }>
    totalSubscriptions: number
    restFallbackActive: boolean
  }
  combined: {
    activeSymbols: string[]
    totalListeners: number
  }
}

export class UnifiedWebSocketManager {
  private combinedListeners: Map<string, Function[]> = new Map()
  private activeSymbols: Set<string> = new Set()
  private marketDataCache: Map<string, MarketDataUpdate> = new Map()
  private sentimentCache: Map<string, SentimentUpdate> = new Map()

  constructor() {
    this.initializeManagers()
  }

  private initializeManagers() {
    // Connect sentiment WebSocket
    sentimentWebSocket.connect()
    
    console.log('🚀 Unified WebSocket Manager initialized')
  }

  // Subscribe to combined market data and sentiment for a symbol
  subscribeCombined(
    symbol: string, 
    exchanges: string[] = ['binance', 'bybit'], 
    callback: (update: CombinedMarketUpdate) => void
  ) {
    const symbolKey = symbol.toUpperCase()
    this.activeSymbols.add(symbolKey)

    // Add to combined listeners
    if (!this.combinedListeners.has(symbolKey)) {
      this.combinedListeners.set(symbolKey, [])
    }
    this.combinedListeners.get(symbolKey)!.push(callback)

    // Subscribe to sentiment
    sentimentWebSocket.subscribe(symbolKey, (sentimentUpdate) => {
      this.sentimentCache.set(symbolKey, sentimentUpdate)
      this.emitCombinedUpdate(symbolKey)
    })

    // Subscribe to market data from multiple exchanges
    exchanges.forEach(exchange => {
      marketDataWebSocket.subscribe(symbolKey, exchange, (marketUpdate) => {
        const cacheKey = `${exchange}:${symbolKey}`
        this.marketDataCache.set(cacheKey, marketUpdate)
        this.emitCombinedUpdate(symbolKey)
      })
    })

    console.log(`📡 Combined subscription active for ${symbolKey} on exchanges: ${exchanges.join(', ')}`)
  }

  // Subscribe to market data only
  subscribeMarketData(
    symbol: string, 
    exchange: string, 
    callback: (update: MarketDataUpdate) => void
  ) {
    marketDataWebSocket.subscribe(symbol, exchange, callback)
  }

  // Subscribe to sentiment only
  subscribeSentiment(symbol: string, callback: (update: SentimentUpdate) => void) {
    sentimentWebSocket.subscribe(symbol, callback)
  }

  // Subscribe to order book
  subscribeOrderBook(
    symbol: string, 
    exchange: string, 
    callback: (update: OrderBookUpdate) => void
  ) {
    marketDataWebSocket.subscribeOrderBook(symbol, exchange, callback)
  }

  // Subscribe to trades
  subscribeTrades(
    symbol: string, 
    exchange: string, 
    callback: (update: TradeUpdate) => void
  ) {
    marketDataWebSocket.subscribeTrades(symbol, exchange, callback)
  }

  private emitCombinedUpdate(symbol: string) {
    const callbacks = this.combinedListeners.get(symbol)
    if (!callbacks || callbacks.length === 0) return

    // Get latest sentiment
    const sentiment = this.sentimentCache.get(symbol)

    // Get latest market data from all exchanges
    const marketDataUpdates: MarketDataUpdate[] = []
    const exchanges: string[] = []

    this.marketDataCache.forEach((update, key) => {
      if (key.endsWith(`:${symbol}`)) {
        marketDataUpdates.push(update)
        exchanges.push(update.exchange)
      }
    })

    // Use the most recent market data update
    const latestMarketData = marketDataUpdates.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    )[0]

    const combinedUpdate: CombinedMarketUpdate = {
      symbol,
      timestamp: new Date().toISOString(),
      marketData: latestMarketData,
      sentiment,
      exchanges: [...new Set(exchanges)]
    }

    // Notify all listeners
    callbacks.forEach(callback => {
      try {
        callback(combinedUpdate)
      } catch (error) {
        console.error('❌ Error in combined update callback:', error)
      }
    })
  }

  // Get aggregated market data across exchanges
  getAggregatedMarketData(symbol: string): {
    averagePrice: number
    totalVolume: number
    priceSpread: number
    exchanges: string[]
    lastUpdate: string
  } | null {
    const symbolKey = symbol.toUpperCase()
    const marketDataUpdates: MarketDataUpdate[] = []

    this.marketDataCache.forEach((update, key) => {
      if (key.endsWith(`:${symbolKey}`)) {
        marketDataUpdates.push(update)
      }
    })

    if (marketDataUpdates.length === 0) return null

    const prices = marketDataUpdates.map(u => u.price)
    const volumes = marketDataUpdates.map(u => u.volume24h)
    const exchanges = marketDataUpdates.map(u => u.exchange)
    
    const averagePrice = prices.reduce((sum, price) => sum + price, 0) / prices.length
    const totalVolume = volumes.reduce((sum, vol) => sum + vol, 0)
    const priceSpread = Math.max(...prices) - Math.min(...prices)
    const lastUpdate = marketDataUpdates.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    )[0].timestamp

    return {
      averagePrice,
      totalVolume,
      priceSpread,
      exchanges,
      lastUpdate
    }
  }

  // Get current sentiment for a symbol
  getCurrentSentiment(symbol: string): SentimentUpdate | null {
    return this.sentimentCache.get(symbol.toUpperCase()) || null
  }

  // Get latest market data for a symbol from a specific exchange
  getLatestMarketData(symbol: string, exchange: string): MarketDataUpdate | null {
    return this.marketDataCache.get(`${exchange}:${symbol.toUpperCase()}`) || null
  }

  // Unsubscribe from combined updates
  unsubscribeCombined(symbol: string, callback?: Function) {
    const symbolKey = symbol.toUpperCase()
    
    if (callback) {
      const callbacks = this.combinedListeners.get(symbolKey)
      if (callbacks) {
        const index = callbacks.indexOf(callback)
        if (index > -1) {
          callbacks.splice(index, 1)
        }
        if (callbacks.length === 0) {
          this.combinedListeners.delete(symbolKey)
          this.activeSymbols.delete(symbolKey)
        }
      }
    } else {
      this.combinedListeners.delete(symbolKey)
      this.activeSymbols.delete(symbolKey)
    }

    // Clean up caches
    this.sentimentCache.delete(symbolKey)
    this.marketDataCache.forEach((_, key) => {
      if (key.endsWith(`:${symbolKey}`)) {
        this.marketDataCache.delete(key)
      }
    })

    console.log(`📡 Unsubscribed from combined updates for ${symbolKey}`)
  }

  // Get comprehensive status
  getStatus(): WebSocketStatus {
    const sentimentStatus = sentimentWebSocket.getStatus()
    const marketDataStatus = marketDataWebSocket.getStatus()

    return {
      sentiment: sentimentStatus,
      marketData: marketDataStatus,
      combined: {
        activeSymbols: Array.from(this.activeSymbols),
        totalListeners: this.combinedListeners.size
      }
    }
  }

  // Connect to specific exchanges
  async connectToExchanges(exchanges: string[]) {
    for (const exchange of exchanges) {
      await marketDataWebSocket.connectToExchange(exchange)
    }
  }

  // Disconnect all connections
  disconnect() {
    sentimentWebSocket.disconnect()
    marketDataWebSocket.disconnect()
    
    this.combinedListeners.clear()
    this.activeSymbols.clear()
    this.marketDataCache.clear()
    this.sentimentCache.clear()
    
    console.log('🔌 Unified WebSocket Manager disconnected')
  }

  // Get real-time trading signals based on combined data
  getTradingSignals(symbol: string): {
    signal: 'BUY' | 'SELL' | 'HOLD'
    confidence: number
    reasons: string[]
    timestamp: string
  } | null {
    const sentiment = this.getCurrentSentiment(symbol)
    const aggregatedData = this.getAggregatedMarketData(symbol)

    if (!sentiment || !aggregatedData) return null

    const reasons: string[] = []
    let signal: 'BUY' | 'SELL' | 'HOLD' = 'HOLD'
    let confidence = 0

    // Sentiment analysis
    if (sentiment.sentimentScore > 0.7) {
      reasons.push('Strong positive sentiment')
      confidence += 30
    } else if (sentiment.sentimentScore < 0.3) {
      reasons.push('Strong negative sentiment')
      confidence += 30
    }

    // Price spread analysis
    if (aggregatedData.priceSpread > aggregatedData.averagePrice * 0.01) {
      reasons.push('High price spread across exchanges')
      confidence += 20
    }

    // Volume analysis
    if (aggregatedData.totalVolume > 1000000) {
      reasons.push('High trading volume')
      confidence += 25
    }

    // Determine signal
    if (sentiment.sentimentScore > 0.6 && confidence > 50) {
      signal = 'BUY'
    } else if (sentiment.sentimentScore < 0.4 && confidence > 50) {
      signal = 'SELL'
    }

    return {
      signal,
      confidence: Math.min(confidence, 100),
      reasons,
      timestamp: new Date().toISOString()
    }
  }
}

// Singleton instance for global use
export const webSocketManager = new UnifiedWebSocketManager()
