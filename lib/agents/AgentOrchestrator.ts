import { supabase, TradingAgent } from '../supabase'
import { tradingEngine, OrderRequest } from '../trading/TradingEngine'

export interface AgentTask {
  id: string
  agent_id: string
  type: 'ANALYSIS' | 'TRADING' | 'MONITORING'
  symbol: string
  parameters: Record<string, any>
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED'
  result?: any
  created_at: Date
  completed_at?: Date
}

export class AgentOrchestrator {
  private agents: Map<string, TradingAgent> = new Map()
  private isRunning: boolean = false
  private taskQueue: AgentTask[] = []

  constructor() {
    console.log('🎭 Agent Orchestrator initialized')
  }

  /**
   * Start the orchestrator
   */
  async start(): Promise<void> {
    this.isRunning = true
    console.log('🚀 Agent Orchestrator started')
    
    // Load agents from database
    await this.loadAgents()
    
    // Start task processing
    this.startTaskProcessing()
    
    // Start agent monitoring
    this.startAgentMonitoring()
  }

  /**
   * Stop the orchestrator
   */
  async stop(): Promise<void> {
    this.isRunning = false
    console.log('🛑 Agent Orchestrator stopped')
  }

  /**
   * Load agents from database
   */
  private async loadAgents(): Promise<void> {
    try {
      const { data: agents, error } = await supabase
        .from('trading_agents')
        .select('*')
        .eq('status', 'ACTIVE')

      if (error) {
        console.error('❌ Error loading agents:', error)
        return
      }

      this.agents.clear()
      agents?.forEach(agent => {
        this.agents.set(agent.id, agent)
      })

      console.log(`📊 Loaded ${this.agents.size} active agents`)
    } catch (error) {
      console.error('❌ Load agents error:', error)
    }
  }

  /**
   * Add a task to the queue
   */
  async addTask(task: Omit<AgentTask, 'id' | 'created_at'>): Promise<string> {
    const newTask: AgentTask = {
      ...task,
      id: crypto.randomUUID(),
      created_at: new Date()
    }

    this.taskQueue.push(newTask)
    console.log(`📝 Task added to queue: ${newTask.type} for ${newTask.symbol}`)

    return newTask.id
  }

  /**
   * Process tasks in the queue
   */
  private async startTaskProcessing(): Promise<void> {
    setInterval(async () => {
      if (!this.isRunning || this.taskQueue.length === 0) return

      const task = this.taskQueue.shift()
      if (!task) return

      await this.processTask(task)
    }, 5000) // Process tasks every 5 seconds
  }

  /**
   * Process a single task
   */
  private async processTask(task: AgentTask): Promise<void> {
    try {
      console.log(`🔄 Processing task: ${task.type} for ${task.symbol}`)

      // Update agent status to THINKING
      await this.updateAgentStatus(task.agent_id, 'THINKING', `Processing ${task.type} for ${task.symbol}`)

      // Process based on task type
      let result: any
      switch (task.type) {
        case 'ANALYSIS':
          result = await this.performAnalysis(task)
          break
        case 'TRADING':
          result = await this.performTrading(task)
          break
        case 'MONITORING':
          result = await this.performMonitoring(task)
          break
        default:
          throw new Error(`Unknown task type: ${task.type}`)
      }

      // Update task as completed
      task.status = 'COMPLETED'
      task.result = result
      task.completed_at = new Date()

      // Update agent status back to ACTIVE
      await this.updateAgentStatus(task.agent_id, 'ACTIVE')

      console.log(`✅ Task completed: ${task.type} for ${task.symbol}`)

    } catch (error) {
      console.error(`❌ Task failed: ${task.type} for ${task.symbol}`, error)
      
      // Update task as failed
      task.status = 'FAILED'
      task.result = { error: error instanceof Error ? error.message : 'Unknown error' }
      task.completed_at = new Date()

      // Update agent status to ERROR
      await this.updateAgentStatus(task.agent_id, 'ERROR', `Task failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Perform market analysis
   */
  private async performAnalysis(task: AgentTask): Promise<any> {
    const agent = this.agents.get(task.agent_id)
    if (!agent) throw new Error('Agent not found')

    // Simulate AI analysis based on agent specialization
    const analysis = {
      symbol: task.symbol,
      agent: agent.name,
      specialization: agent.specialization,
      confidence: Math.random() * 100,
      recommendation: this.generateRecommendation(agent.specialization),
      indicators: this.generateIndicators(agent.specialization),
      timestamp: new Date().toISOString()
    }

    // Store analysis result
    await supabase
      .from('ai_analysis')
      .insert({
        agent_id: task.agent_id,
        analysis_type: task.type,
        symbol: task.symbol,
        timeframe: '1h',
        analysis_result: analysis,
        confidence_score: analysis.confidence,
        recommendation: analysis.recommendation,
        ai_model_used: agent.ai_model,
        organization_id: task.parameters.organization_id
      })

    return analysis
  }

  /**
   * Perform trading action
   */
  private async performTrading(task: AgentTask): Promise<any> {
    const agent = this.agents.get(task.agent_id)
    if (!agent) throw new Error('Agent not found')

    // Create order request
    const orderRequest: OrderRequest = {
      symbol: task.symbol,
      side: task.parameters.side,
      size: task.parameters.size,
      type: 'MARKET',
      agent_id: task.agent_id,
      organization_id: task.parameters.organization_id
    }

    // Place order through trading engine
    const result = await tradingEngine.placeOrder(orderRequest)

    if (result.success) {
      // Create trading signal
      await supabase
        .from('trading_signals')
        .insert({
          agent_id: task.agent_id,
          symbol: task.symbol,
          signal_type: task.parameters.side,
          strength: task.parameters.confidence || 75,
          reasoning: `AI agent ${agent.name} executed ${task.parameters.side} order`,
          organization_id: task.parameters.organization_id
        })
    }

    return result
  }

  /**
   * Perform monitoring
   */
  private async performMonitoring(task: AgentTask): Promise<any> {
    const agent = this.agents.get(task.agent_id)
    if (!agent) throw new Error('Agent not found')

    // Simulate monitoring logic
    const monitoring = {
      symbol: task.symbol,
      agent: agent.name,
      alerts: this.generateAlerts(task.symbol),
      risk_score: Math.random() * 100,
      timestamp: new Date().toISOString()
    }

    return monitoring
  }

  /**
   * Generate recommendation based on agent specialization
   */
  private generateRecommendation(specialization: string): string {
    const recommendations = ['STRONG_BUY', 'BUY', 'NEUTRAL', 'SELL', 'STRONG_SELL']
    const weights = {
      'technical_analysis': [0.2, 0.3, 0.2, 0.2, 0.1],
      'sentiment_analysis': [0.15, 0.25, 0.3, 0.2, 0.1],
      'risk_management': [0.1, 0.2, 0.4, 0.2, 0.1]
    }

    const weight = weights[specialization as keyof typeof weights] || weights.technical_analysis
    const random = Math.random()
    let cumulative = 0

    for (let i = 0; i < recommendations.length; i++) {
      cumulative += weight[i]
      if (random <= cumulative) {
        return recommendations[i]
      }
    }

    return 'NEUTRAL'
  }

  /**
   * Generate technical indicators
   */
  private generateIndicators(specialization: string): Record<string, any> {
    const indicators: Record<string, any> = {}

    if (specialization === 'technical_analysis') {
      indicators.rsi = 30 + Math.random() * 40
      indicators.macd = {
        macd: Math.random() * 2 - 1,
        signal: Math.random() * 2 - 1,
        histogram: Math.random() * 2 - 1
      }
      indicators.bollinger_bands = {
        upper: 100 + Math.random() * 10,
        middle: 100 + Math.random() * 5,
        lower: 100 - Math.random() * 10
      }
    }

    if (specialization === 'sentiment_analysis') {
      indicators.sentiment_score = Math.random() * 100
      indicators.social_volume = Math.random() * 1000
      indicators.news_sentiment = Math.random() * 2 - 1
    }

    return indicators
  }

  /**
   * Generate monitoring alerts
   */
  private generateAlerts(symbol: string): string[] {
    const alerts = []
    const random = Math.random()

    if (random > 0.7) alerts.push('High volatility detected')
    if (random > 0.8) alerts.push('Volume spike detected')
    if (random > 0.9) alerts.push('Price movement outside normal range')

    return alerts
  }

  /**
   * Update agent status
   */
  private async updateAgentStatus(agentId: string, status: string, currentTask?: string): Promise<void> {
    try {
      await supabase
        .from('trading_agents')
        .update({
          status,
          current_task: currentTask,
          last_activity: new Date().toISOString()
        })
        .eq('id', agentId)
    } catch (error) {
      console.error('❌ Error updating agent status:', error)
    }
  }

  /**
   * Start agent monitoring
   */
  private startAgentMonitoring(): void {
    setInterval(async () => {
      if (!this.isRunning) return

      try {
        // Reload agents from database
        await this.loadAgents()

        // Check agent health
        for (const [agentId, agent] of this.agents) {
          const timeSinceLastActivity = Date.now() - new Date(agent.last_activity).getTime()
          
          // If agent hasn't been active for 5 minutes, mark as IDLE
          if (timeSinceLastActivity > 5 * 60 * 1000 && agent.status === 'ACTIVE') {
            await this.updateAgentStatus(agentId, 'IDLE')
          }
        }
      } catch (error) {
        console.error('❌ Agent monitoring error:', error)
      }
    }, 60000) // Check every minute
  }

  /**
   * Get orchestrator status
   */
  getStatus(): { running: boolean; agents: number; queueLength: number } {
    return {
      running: this.isRunning,
      agents: this.agents.size,
      queueLength: this.taskQueue.length
    }
  }
}

// Export singleton instance
export const agentOrchestrator = new AgentOrchestrator() 