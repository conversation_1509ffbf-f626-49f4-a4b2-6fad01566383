import { BaseAgent, AnalysisResult } from './BaseAgent'

export interface MultiModelSentimentData {
  twitter: {
    mentions: number
    positive: number
    negative: number
    influencerSentiment: string
    volume: 'HIGH' | 'MEDIUM' | 'LOW'
    trendingHashtags: string[]
  }
  reddit: {
    posts: number
    upvoteRatio: number
    commentSentiment: string
    hotPosts: string[]
    subredditActivity: { [key: string]: number }
  }
  news: {
    articles: number
    positive: number
    negative: number
    headlines: string[]
    sources: string[]
    impact: 'HIGH' | 'MEDIUM' | 'LOW'
  }
  social: {
    telegram: { messages: number; sentiment: string }
    discord: { messages: number; sentiment: string }
    youtube: { videos: number; sentiment: string }
  }
}

export interface AIProviderConfig {
  name: string
  endpoint: string
  apiKey: string
  model: string
  specialization: string[]
  temperature: number
  maxTokens: number
}

export interface EnhancedSentimentResult {
  recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL'
  confidence: number
  sentimentScore: number // -100 to +100
  socialVolume: 'HIGH' | 'MEDIUM' | 'LOW'
  influencerSentiment: 'BULLISH' | 'NEUTRAL' | 'BEARISH'
  newsImpact: 'POSITIVE' | 'NEUTRAL' | 'NEGATIVE'
  reasoning: string
  keyFactors: string[]
  timeframe: string
  marketMood: 'FEAR' | 'GREED' | 'NEUTRAL' | 'UNCERTAINTY'
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  aiConsensus: {
    [providerName: string]: {
      score: number
      confidence: number
      reasoning: string
    }
  }
  correlationWithPrice: number
  volumeCorrelation: number
  technicalAlignment: 'BULLISH' | 'BEARISH' | 'NEUTRAL'
  emergingNarratives: string[]
  socialMediaTrends: string[]
}

export class SentimentAnalysisAgentV3 extends BaseAgent {
  private aiProviders: AIProviderConfig[]
  private cache: Map<string, { data: any; timestamp: number }> = new Map()
  private cacheTimeout = 5 * 60 * 1000 // 5 minutes

  constructor() {
    super({
      id: 'sentiment_analysis_v3',
      name: 'Advanced Multi-Model Sentiment Analysis',
      specialization: 'sentiment_analysis',
      aiProvider: 'multi-model',
      aiModel: 'ensemble'
    })
    
    // Configure AI providers with your API keys
    this.aiProviders = [
      {
        name: 'OpenRouter-DeepSeek',
        endpoint: 'https://openrouter.ai/api/v1/chat/completions',
        apiKey: '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',
        model: 'deepseek/deepseek-chat',
        specialization: ['quick_analysis', 'code_review'],
        temperature: 0.3,
        maxTokens: 4096
      },
      {
        name: 'OpenRouter-Claude',
        endpoint: 'https://openrouter.ai/api/v1/chat/completions',
        apiKey: '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',
        model: 'anthropic/claude-3.5-sonnet',
        specialization: ['deep_analysis', 'risk_assessment'],
        temperature: 0.4,
        maxTokens: 4096
      },
      {
        name: 'OpenRouter-Llama',
        endpoint: 'https://openrouter.ai/api/v1/chat/completions',
        apiKey: '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',
        model: 'meta-llama/llama-3.1-405b-instruct',
        specialization: ['market_sentiment', 'social_analysis'],
        temperature: 0.5,
        maxTokens: 4096
      },
      {
        name: 'Gemini',
        endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
        apiKey: 'AIzaSyCTHMGBEqqgHxJWxZRBgBFs-8Z2YVcsBxY',
        model: 'gemini-pro',
        specialization: ['data_analysis', 'prediction'],
        temperature: 0.4,
        maxTokens: 2048
      },
      {
        name: 'GLM-4.5',
        endpoint: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
        apiKey: '108102c71ab14c3aa202f7810429c998.ZxS1JyXLXRB0ytgO',
        model: 'glm-4-plus',
        specialization: ['chinese_markets', 'sentiment', 'multilingual'],
        temperature: 0.6,
        maxTokens: 4096
      }
    ]
  }

  async analyze(symbol: string): Promise<AnalysisResult> {
    this.currentTask = `Multi-model sentiment analysis for ${symbol}`
    
    try {
      // Step 1: Collect comprehensive sentiment data
      console.log(`🔍 Collecting sentiment data for ${symbol}...`)
      const sentimentData = await this.collectSentimentData(symbol)
      
      // Step 2: Get analysis from multiple AI models
      console.log(`🤖 Analyzing with ${this.aiProviders.length} AI models...`)
      const aiAnalyses = await this.getMultiModelAnalysis(symbol, sentimentData)
      
      // Step 3: Combine and weight the results
      console.log(`⚖️ Combining multi-model results...`)
      const finalAnalysis = await this.combineAnalyses(aiAnalyses, sentimentData)
      
      return {
        type: 'sentiment_analysis_v3',
        symbol: symbol,
        recommendation: finalAnalysis.recommendation,
        confidence: finalAnalysis.confidence,
        reasoning: finalAnalysis.reasoning,
        data: finalAnalysis,
        timestamp: new Date()
      }
      
    } catch (error) {
      console.error(`❌ Sentiment analysis failed for ${symbol}:`, error)
      throw error
    }
  }

  private async collectSentimentData(symbol: string): Promise<MultiModelSentimentData> {
    // Check cache first
    const cacheKey = `sentiment_data_${symbol}`
    const cached = this.cache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data
    }

    // Collect data from multiple sources (simplified for demo)
    const data: MultiModelSentimentData = {
      twitter: {
        mentions: Math.floor(Math.random() * 10000) + 1000,
        positive: Math.floor(Math.random() * 40) + 30,
        negative: Math.floor(Math.random() * 30) + 10,
        influencerSentiment: ['BULLISH', 'NEUTRAL', 'BEARISH'][Math.floor(Math.random() * 3)],
        volume: ['HIGH', 'MEDIUM', 'LOW'][Math.floor(Math.random() * 3)] as any,
        trendingHashtags: [`#${symbol}`, '#crypto', '#bullish', '#moon']
      },
      reddit: {
        posts: Math.floor(Math.random() * 500) + 100,
        upvoteRatio: Math.random() * 0.4 + 0.6,
        commentSentiment: ['positive', 'neutral', 'negative'][Math.floor(Math.random() * 3)],
        hotPosts: [
          `${symbol} analysis and predictions`,
          `Why ${symbol} is undervalued`,
          `${symbol} technical analysis`
        ],
        subredditActivity: {
          'cryptocurrency': Math.floor(Math.random() * 100),
          'CryptoMarkets': Math.floor(Math.random() * 50),
          [`${symbol.toLowerCase()}`]: Math.floor(Math.random() * 200)
        }
      },
      news: {
        articles: Math.floor(Math.random() * 50) + 10,
        positive: Math.floor(Math.random() * 60) + 20,
        negative: Math.floor(Math.random() * 30) + 5,
        headlines: [
          `${symbol} shows strong fundamentals`,
          `Major adoption for ${symbol}`,
          `${symbol} partnership announcement`
        ],
        sources: ['CoinDesk', 'CoinTelegraph', 'Decrypt', 'The Block'],
        impact: ['HIGH', 'MEDIUM', 'LOW'][Math.floor(Math.random() * 3)] as any
      },
      social: {
        telegram: {
          messages: Math.floor(Math.random() * 1000),
          sentiment: ['bullish', 'neutral', 'bearish'][Math.floor(Math.random() * 3)]
        },
        discord: {
          messages: Math.floor(Math.random() * 500),
          sentiment: ['bullish', 'neutral', 'bearish'][Math.floor(Math.random() * 3)]
        },
        youtube: {
          videos: Math.floor(Math.random() * 20),
          sentiment: ['bullish', 'neutral', 'bearish'][Math.floor(Math.random() * 3)]
        }
      }
    }

    // Cache the result
    this.cache.set(cacheKey, { data, timestamp: Date.now() })
    
    return data
  }

  private async getMultiModelAnalysis(symbol: string, data: MultiModelSentimentData): Promise<any[]> {
    const analyses = []
    
    for (const provider of this.aiProviders) {
      try {
        console.log(`🔄 Analyzing with ${provider.name}...`)
        const analysis = await this.callAIProvider(provider, symbol, data)
        analyses.push({
          provider: provider.name,
          specialization: provider.specialization,
          analysis
        })
      } catch (error) {
        console.warn(`⚠️ ${provider.name} analysis failed:`, error instanceof Error ? error.message : 'Unknown error')
        // Continue with other providers
      }
    }
    
    return analyses
  }

  private async callAIProvider(provider: AIProviderConfig, symbol: string, data: MultiModelSentimentData): Promise<any> {
    const prompt = this.buildSentimentPrompt(symbol, data, provider.specialization)
    
    if (provider.name.includes('Gemini')) {
      return this.callGemini(provider, prompt)
    } else if (provider.name.includes('GLM')) {
      return this.callGLM(provider, prompt)
    } else {
      return this.callOpenRouter(provider, prompt)
    }
  }

  private async callOpenRouter(provider: AIProviderConfig, prompt: string): Promise<any> {
    const response = await fetch(provider.endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${provider.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://cryptoagent.pro',
        'X-Title': 'CryptoAgent Pro'
      },
      body: JSON.stringify({
        model: provider.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert cryptocurrency sentiment analyst. Provide accurate, data-driven analysis in JSON format.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: provider.temperature,
        max_tokens: provider.maxTokens
      })
    })

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`)
    }

    const result = await response.json()
    return JSON.parse(result.choices[0].message.content)
  }

  private async callGemini(provider: AIProviderConfig, prompt: string): Promise<any> {
    const response = await fetch(`${provider.endpoint}?key=${provider.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: provider.temperature,
          maxOutputTokens: provider.maxTokens
        }
      })
    })

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status}`)
    }

    const result = await response.json()
    return JSON.parse(result.candidates[0].content.parts[0].text)
  }

  private async callGLM(provider: AIProviderConfig, prompt: string): Promise<any> {
    const response = await fetch(provider.endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${provider.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: provider.model,
        messages: [
          {
            role: 'system',
            content: 'You are a professional cryptocurrency market analyst specializing in sentiment analysis.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: provider.temperature,
        max_tokens: provider.maxTokens
      })
    })

    if (!response.ok) {
      throw new Error(`GLM API error: ${response.status}`)
    }

    const result = await response.json()
    return JSON.parse(result.choices[0].message.content)
  }

  private buildSentimentPrompt(symbol: string, data: MultiModelSentimentData, specialization: string[]): string {
    const isMultilingual = specialization.includes('multilingual')
    const language = isMultilingual ? 'Provide analysis in both English and Chinese.' : ''
    
    return `
Analyze cryptocurrency sentiment for ${symbol} using this comprehensive data:

📊 SOCIAL MEDIA DATA:
Twitter:
- Mentions: ${data.twitter.mentions}
- Positive: ${data.twitter.positive}%
- Negative: ${data.twitter.negative}%
- Volume: ${data.twitter.volume}
- Influencer sentiment: ${data.twitter.influencerSentiment}
- Trending: ${data.twitter.trendingHashtags.join(', ')}

Reddit:
- Posts: ${data.reddit.posts}
- Upvote ratio: ${(data.reddit.upvoteRatio * 100).toFixed(1)}%
- Comment sentiment: ${data.reddit.commentSentiment}
- Subreddit activity: ${JSON.stringify(data.reddit.subredditActivity)}

📰 NEWS DATA:
- Articles: ${data.news.articles}
- Positive coverage: ${data.news.positive}%
- Negative coverage: ${data.news.negative}%
- Impact level: ${data.news.impact}
- Key headlines: ${data.news.headlines.join(' | ')}

💬 ADDITIONAL SOCIAL:
- Telegram sentiment: ${data.social.telegram.sentiment} (${data.social.telegram.messages} messages)
- Discord sentiment: ${data.social.discord.sentiment} (${data.social.discord.messages} messages)
- YouTube sentiment: ${data.social.youtube.sentiment} (${data.social.youtube.videos} videos)

${language}

Provide analysis in this EXACT JSON format:
{
  "recommendation": "STRONG_BUY|BUY|NEUTRAL|SELL|STRONG_SELL",
  "confidence": 85,
  "sentimentScore": 72,
  "socialVolume": "HIGH|MEDIUM|LOW",
  "influencerSentiment": "BULLISH|NEUTRAL|BEARISH",
  "newsImpact": "POSITIVE|NEUTRAL|NEGATIVE",
  "marketMood": "FEAR|GREED|NEUTRAL|UNCERTAINTY",
  "riskLevel": "LOW|MEDIUM|HIGH",
  "reasoning": "Detailed multi-factor analysis explanation",
  "keyFactors": ["factor1", "factor2", "factor3"],
  "emergingNarratives": ["narrative1", "narrative2"],
  "socialMediaTrends": ["trend1", "trend2"],
  "correlationWithPrice": 0.75,
  "volumeCorrelation": 0.68,
  "technicalAlignment": "BULLISH|BEARISH|NEUTRAL",
  "timeframe": "24h"
}

Score range: -100 (extremely bearish) to +100 (extremely bullish)
Focus on your specialization: ${specialization.join(', ')}
`
  }

  private async combineAnalyses(analyses: any[], data: MultiModelSentimentData): Promise<EnhancedSentimentResult> {
    if (analyses.length === 0) {
      throw new Error('No AI analyses available')
    }

    // Calculate weighted averages
    let totalScore = 0
    let totalConfidence = 0
    let totalWeight = 0
    const aiConsensus: any = {}
    
    // Weight models based on specialization relevance
    const weights = {
      'sentiment': 1.5,
      'market_sentiment': 1.4,
      'social_analysis': 1.3,
      'deep_analysis': 1.2,
      'data_analysis': 1.1,
      'default': 1.0
    }

    for (const analysis of analyses) {
      const weight = this.calculateWeight(analysis.specialization, weights)
      totalScore += analysis.analysis.sentimentScore * weight
      totalConfidence += analysis.analysis.confidence * weight
      totalWeight += weight
      
      aiConsensus[analysis.provider] = {
        score: analysis.analysis.sentimentScore,
        confidence: analysis.analysis.confidence,
        reasoning: analysis.analysis.reasoning
      }
    }

    const avgScore = totalScore / totalWeight
    const avgConfidence = totalConfidence / totalWeight

    // Determine recommendation based on consensus
    const recommendation = this.getRecommendation(avgScore, avgConfidence)

    // Combine key factors and narratives
    const allFactors = analyses.flatMap(a => a.analysis.keyFactors || [])
    const allNarratives = analyses.flatMap(a => a.analysis.emergingNarratives || [])
    const allTrends = analyses.flatMap(a => a.analysis.socialMediaTrends || [])

    return {
      recommendation,
      confidence: Math.round(avgConfidence),
      sentimentScore: Math.round(avgScore),
      socialVolume: this.determineSocialVolume(data),
      influencerSentiment: this.getConsensusValue(analyses, 'influencerSentiment'),
      newsImpact: this.getConsensusValue(analyses, 'newsImpact'),
      marketMood: this.getConsensusValue(analyses, 'marketMood'),
      riskLevel: this.assessRiskLevel(avgScore, avgConfidence),
      reasoning: this.generateCombinedReasoning(analyses, avgScore, avgConfidence),
      keyFactors: [...new Set(allFactors)].slice(0, 5),
      timeframe: '24h',
      aiConsensus,
      correlationWithPrice: this.calculateCorrelation(analyses, 'correlationWithPrice'),
      volumeCorrelation: this.calculateCorrelation(analyses, 'volumeCorrelation'),
      technicalAlignment: this.getConsensusValue(analyses, 'technicalAlignment'),
      emergingNarratives: [...new Set(allNarratives)].slice(0, 3),
      socialMediaTrends: [...new Set(allTrends)].slice(0, 5)
    }
  }

  private calculateWeight(specializations: string[], weights: any): number {
    for (const spec of specializations) {
      if (weights[spec]) return weights[spec]
    }
    return weights.default
  }

  private getRecommendation(score: number, confidence: number): any {
    if (confidence < 60) return 'NEUTRAL'
    
    if (score >= 60) return 'STRONG_BUY'
    if (score >= 30) return 'BUY'
    if (score <= -60) return 'STRONG_SELL'
    if (score <= -30) return 'SELL'
    return 'NEUTRAL'
  }

  private getConsensusValue(analyses: any[], field: string): any {
    const values = analyses.map(a => a.analysis[field]).filter(Boolean)
    if (values.length === 0) return 'NEUTRAL'
    
    // Return most common value
    const counts = values.reduce((acc, val) => {
      acc[val] = (acc[val] || 0) + 1
      return acc
    }, {})
    
    return Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b)
  }

  private determineSocialVolume(data: MultiModelSentimentData): 'HIGH' | 'MEDIUM' | 'LOW' {
    const totalMentions = data.twitter.mentions + data.reddit.posts + 
                         data.social.telegram.messages + data.social.discord.messages
    
    if (totalMentions > 5000) return 'HIGH'
    if (totalMentions > 1000) return 'MEDIUM'
    return 'LOW'
  }

  private assessRiskLevel(score: number, confidence: number): 'LOW' | 'MEDIUM' | 'HIGH' {
    if (confidence < 50) return 'HIGH'
    if (Math.abs(score) > 70) return 'MEDIUM'
    return 'LOW'
  }

  private calculateCorrelation(analyses: any[], field: string): number {
    const values = analyses.map(a => a.analysis[field]).filter(v => typeof v === 'number')
    if (values.length === 0) return 0.5
    
    return values.reduce((sum, val) => sum + val, 0) / values.length
  }

  private generateCombinedReasoning(analyses: any[], avgScore: number, avgConfidence: number): string {
    const providerCount = analyses.length
    const scoreDirection = avgScore > 0 ? 'bullish' : avgScore < 0 ? 'bearish' : 'neutral'
    
    return `Multi-model consensus from ${providerCount} AI providers shows ${scoreDirection} sentiment (score: ${avgScore.toFixed(1)}) with ${avgConfidence.toFixed(1)}% confidence. Analysis incorporates social media sentiment, news impact, and technical alignment across multiple specialized models including sentiment analysis, market analysis, and social data processing capabilities.`
  }
}