import { BaseAgent, AnalysisResult } from './BaseAgent'

export interface SentimentData {
  twitter: {
    mentions: number
    positive: number
    negative: number
    influencerSentiment: string
  }
  reddit: {
    posts: number
    upvoteRatio: number
    commentSentiment: string
  }
  news: {
    articles: number
    positive: number
    negative: number
    headlines: string[]
  }
}

export class SentimentAnalysisAgent extends BaseAgent {
  async analyze(symbol: string): Promise<AnalysisResult> {
    this.currentTask = `Analyzing ${symbol} market sentiment`

    // Collect sentiment data from multiple sources
    const [twitterData, redditData, newsData] = await Promise.all([
      this.getTwitterSentiment(symbol),
      this.getRedditSentiment(symbol),
      this.getNewsSentiment(symbol)
    ])

    const prompt = `
    As a crypto market sentiment analyst, analyze sentiment for ${symbol}:

    Twitter Data:
    - Mentions: ${twitterData.mentions}
    - Positive tweets: ${twitterData.positive}%
    - Negative tweets: ${twitterData.negative}%
    - Influencer sentiment: ${twitterData.influencerSentiment}
    
    Reddit Data:
    - Posts: ${redditData.posts}
    - Upvote ratio: ${redditData.upvoteRatio}
    - Comment sentiment: ${redditData.commentSentiment}
    
    News Data:
    - Articles: ${newsData.articles}
    - Positive news: ${newsData.positive}%
    - Negative news: ${newsData.negative}%
    - Major headlines: ${JSON.stringify(newsData.headlines)}

    Provide sentiment analysis in this EXACT JSON format:
    {
      "recommendation": "STRONG_BUY|BUY|NEUTRAL|SELL|STRONG_SELL",
      "confidence": 75,
      "sentimentScore": 65,
      "socialVolume": "HIGH|MEDIUM|LOW",
      "influencerSentiment": "BULLISH|NEUTRAL|BEARISH",
      "newsImpact": "POSITIVE|NEUTRAL|NEGATIVE",
      "reasoning": "Detailed sentiment analysis explanation",
      "keyFactors": ["factor1", "factor2", "factor3"],
      "timeframe": "24h"
    }
    
    Sentiment score: -100 (very bearish) to +100 (very bullish)
    `

    const aiResponse = await this.callAI(prompt)
    const analysis = JSON.parse(aiResponse)
    
    return {
      type: 'sentiment_analysis',
      symbol: symbol,
      recommendation: analysis.recommendation,
      confidence: analysis.confidence,
      reasoning: analysis.reasoning,
      data: analysis,
      timestamp: new Date()
    }
  }

  private async getTwitterSentiment(symbol: string): Promise<any> {
    try {
      const response = await fetch('/api/sentiment/twitter', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ symbol })
      })
      return response.json()
    } catch (error) {
      console.error('Twitter sentiment error:', error)
      return {
        mentions: 0,
        positive: 50,
        negative: 50,
        influencerSentiment: 'NEUTRAL'
      }
    }
  }

  private async getRedditSentiment(symbol: string): Promise<any> {
    try {
      const response = await fetch('/api/sentiment/reddit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ symbol })
      })
      return response.json()
    } catch (error) {
      console.error('Reddit sentiment error:', error)
      return {
        posts: 0,
        upvoteRatio: 0.5,
        commentSentiment: 'NEUTRAL'
      }
    }
  }

  private async getNewsSentiment(symbol: string): Promise<any> {
    try {
      const response = await fetch('/api/sentiment/news', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ symbol })
      })
      return response.json()
    } catch (error) {
      console.error('News sentiment error:', error)
      return {
        articles: 0,
        positive: 50,
        negative: 50,
        headlines: []
      }
    }
  }
} 