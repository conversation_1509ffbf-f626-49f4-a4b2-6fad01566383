import { BaseAgent, AgentConfig, AnalysisResult } from './BaseAgent'

export interface MarketData {
  symbol: string
  currentPrice: number
  volume: number
  ohlcv: any[]
  indicators: {
    rsi: number
    macd: any
    bollinger: any
    movingAverages: any
  }
}

export class TechnicalAnalysisAgent extends BaseAgent {
  async analyze(marketData: MarketData): Promise<AnalysisResult> {
    this.currentTask = `Analyzing ${marketData.symbol} technical patterns`
    
    const prompt = `
    As a professional crypto technical analyst, analyze this market data:

    Symbol: ${marketData.symbol}
    Current Price: $${marketData.currentPrice}
    24h Volume: ${marketData.volume}
    
    OHLCV Data (last 24 candles):
    ${JSON.stringify(marketData.ohlcv)}
    
    Technical Indicators:
    RSI: ${marketData.indicators.rsi}
    MACD: ${JSON.stringify(marketData.indicators.macd)}
    Bollinger Bands: ${JSON.stringify(marketData.indicators.bollinger)}
    Moving Averages: ${JSON.stringify(marketData.indicators.movingAverages)}
    
    Provide analysis in this EXACT JSON format:
    {
      "recommendation": "STRONG_BUY|BUY|NEUTRAL|SELL|STRONG_SELL",
      "confidence": 85,
      "entryPrice": 45000,
      "stopLoss": 43000,
      "takeProfit": 48000,
      "reasoning": "Detailed explanation of the analysis",
      "keyLevels": {
        "support": [44000, 42000],
        "resistance": [47000, 49000]
      },
      "timeframe": "4h",
      "riskLevel": "MEDIUM"
    }
    
    Be precise and professional. Base recommendations on solid technical analysis.
    `

    const aiResponse = await this.callAI(prompt)
    
    try {
      const analysis = JSON.parse(aiResponse)
      this.performance = Math.min(100, this.performance + 1) // Increment performance
      
      return {
        type: 'technical_analysis',
        symbol: marketData.symbol,
        recommendation: analysis.recommendation,
        confidence: analysis.confidence,
        reasoning: analysis.reasoning,
        data: analysis,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('❌ Failed to parse AI response:', error)
      throw new Error('Invalid AI response format')
    }
  }

  async calculateRSI(prices: number[], period: number = 14): Promise<number> {
    if (prices.length < period) {
      throw new Error('Not enough data for RSI calculation')
    }

    let gains = 0
    let losses = 0

    for (let i = 1; i < period; i++) {
      const change = prices[i] - prices[i - 1]
      if (change > 0) {
        gains += change
      } else {
        losses += Math.abs(change)
      }
    }

    const avgGain = gains / period
    const avgLoss = losses / period
    const rs = avgGain / avgLoss
    const rsi = 100 - (100 / (1 + rs))

    return rsi
  }

  async calculateMACD(prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9): Promise<any> {
    const ema12 = this.calculateEMA(prices, fastPeriod)
    const ema26 = this.calculateEMA(prices, slowPeriod)
    
    const macdLine = ema12 - ema26
    const signalLine = this.calculateEMA([macdLine], signalPeriod)
    const histogram = macdLine - signalLine

    return {
      macd: macdLine,
      signal: signalLine,
      histogram: histogram
    }
  }

  private calculateEMA(prices: number[], period: number): number {
    const multiplier = 2 / (period + 1)
    let ema = prices[0]

    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier))
    }

    return ema
  }

  async calculateBollingerBands(prices: number[], period: number = 20, stdDev: number = 2): Promise<any> {
    const sma = prices.slice(-period).reduce((sum, price) => sum + price, 0) / period
    
    const variance = prices.slice(-period).reduce((sum, price) => {
      return sum + Math.pow(price - sma, 2)
    }, 0) / period
    
    const standardDeviation = Math.sqrt(variance)
    
    return {
      upper: sma + (standardDeviation * stdDev),
      middle: sma,
      lower: sma - (standardDeviation * stdDev)
    }
  }
} 