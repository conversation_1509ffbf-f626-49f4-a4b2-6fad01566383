import { BaseAgent, AgentConfig, AnalysisResult } from './BaseAgent'

export interface MarketData {
  symbol: string
  currentPrice: number
  volume: number
  ohlcv: any[]
  indicators: {
    rsi: number
    macd: any
    bollinger: any
    movingAverages: any
  }
}

/**
 * TechnicalAnalysisAgent voert technische analyse uit op marktgegevens met behulp van AI.
 * @extends BaseAgent
 */
export class TechnicalAnalysisAgent extends BaseAgent {
  /**
 * Voert een technische analyse uit op de gegeven marktgegevens.
 * @param marketData De marktgegevens om te analyseren.
 * @returns Een belofte met het analyseresultaat.
 */
async analyze(marketData: MarketData): Promise<AnalysisResult> {
    this.currentTask = `AI-powered technical analysis for ${marketData.symbol}`

    try {
      const prompt = this.buildAnalysisPrompt(marketData)
      const systemPrompt = `You are a professional cryptocurrency technical analyst with expertise in chart patterns, technical indicators, and market structure analysis.

Your analysis should be:
1. Data-driven and objective
2. Based on proven technical analysis principles
3. Include specific entry/exit levels
4. Assess risk appropriately
5. Consider multiple timeframes

Respond ONLY with valid JSON in the exact format specified. Do not include any additional text or explanations outside the JSON structure.`

      const aiResponse = await this.callAI(prompt, systemPrompt)
      const analysis = this.parseAIResponse(aiResponse, marketData)

      return {
        type: 'technical_analysis',
        symbol: marketData.symbol,
        recommendation: analysis.recommendation,
        confidence: analysis.confidence,
        reasoning: analysis.reasoning,
        data: analysis,
        timestamp: new Date()
      }

    } catch (error) {
      console.error(`Technical analysis failed for ${marketData.symbol}:`, error)
      return this.getFallbackAnalysis(marketData)
    }
  }

  /**
 * Bouwt de prompt voor de AI-analyse op basis van marktgegevens.
 * @param marketData De marktgegevens.
 * @returns De gegenereerde prompt string.
 */
private buildAnalysisPrompt(marketData: MarketData): string {
    return `Analyze the technical data for ${marketData.symbol}:

CURRENT MARKET STATE:
- Symbol: ${marketData.symbol}
- Current Price: $${marketData.currentPrice.toLocaleString()}
- 24h Volume: ${marketData.volume.toLocaleString()}

PRICE ACTION DATA:
${this.formatOHLCVData(marketData.ohlcv)}

TECHNICAL INDICATORS:
- RSI (14): ${marketData.indicators.rsi.toFixed(2)}
- MACD: ${this.formatMACDData(marketData.indicators.macd)}
- Bollinger Bands: ${this.formatBollingerData(marketData.indicators.bollinger)}
- Moving Averages: ${this.formatMAData(marketData.indicators.movingAverages)}

ANALYSIS REQUIREMENTS:
Provide a comprehensive technical analysis with specific trading recommendations.

Respond in this EXACT JSON format:
{
  "recommendation": "STRONG_BUY|BUY|NEUTRAL|SELL|STRONG_SELL",
  "confidence": 85,
  "entryPrice": 45000,
  "stopLoss": 43000,
  "takeProfit": 48000,
  "reasoning": "Detailed technical analysis explanation",
  "keyLevels": {
    "support": [44000, 42000],
    "resistance": [47000, 49000]
  },
  "timeframe": "4h",
  "riskLevel": "LOW|MEDIUM|HIGH",
  "technicalSignals": {
    "trend": "BULLISH|BEARISH|SIDEWAYS",
    "momentum": "STRONG|WEAK|NEUTRAL",
    "volume": "HIGH|NORMAL|LOW"
  },
  "patternAnalysis": "Description of chart patterns identified",
  "marketStructure": "Analysis of support/resistance and market structure"
}`
  }
  private formatOHLCVData(ohlcv: any[]): string {
    if (!ohlcv || ohlcv.length === 0) return 'No OHLCV data available'

    const recent = ohlcv.slice(-5) // Last 5 candles
    return recent.map((candle, index) => {
      const [timestamp, open, high, low, close, volume] = candle
      const date = new Date(timestamp).toISOString().split('T')[0]
      return `${date}: O:${open} H:${high} L:${low} C:${close} V:${volume}`
    }).join('\n')
  }

  private formatMACDData(macd: any): string {
    if (!macd) return 'No MACD data'
    return `MACD: ${macd.macd?.toFixed(4) || 'N/A'}, Signal: ${macd.signal?.toFixed(4) || 'N/A'}, Histogram: ${macd.histogram?.toFixed(4) || 'N/A'}`
  }

  private formatBollingerData(bollinger: any): string {
    if (!bollinger) return 'No Bollinger Bands data'
    return `Upper: ${bollinger.upper?.toFixed(2) || 'N/A'}, Middle: ${bollinger.middle?.toFixed(2) || 'N/A'}, Lower: ${bollinger.lower?.toFixed(2) || 'N/A'}`
  }

  private formatMAData(movingAverages: any): string {
    if (!movingAverages) return 'No Moving Averages data'
    const mas = []
    if (movingAverages.sma20) mas.push(`SMA20: ${movingAverages.sma20.toFixed(2)}`)
    if (movingAverages.sma50) mas.push(`SMA50: ${movingAverages.sma50.toFixed(2)}`)
    if (movingAverages.ema12) mas.push(`EMA12: ${movingAverages.ema12.toFixed(2)}`)
    if (movingAverages.ema26) mas.push(`EMA26: ${movingAverages.ema26.toFixed(2)}`)
    return mas.join(', ') || 'No MA data available'
  }

  /**
 * Parseert de AI-response en valideert deze.
 * @param aiResponse De response van de AI.
 * @param marketData De marktgegevens.
 * @returns Het geparseerde analyse object.
 */
private parseAIResponse(aiResponse: string, marketData: MarketData): any {
    try {
      const analysis = JSON.parse(aiResponse)

      // Validate required fields
      if (!analysis.recommendation || !analysis.confidence || !analysis.reasoning) {
        throw new Error('Missing required fields in AI response')
      }

      // Ensure confidence is within valid range
      analysis.confidence = Math.max(0, Math.min(100, analysis.confidence))

      // Validate recommendation
      const validRecommendations = ['STRONG_BUY', 'BUY', 'NEUTRAL', 'SELL', 'STRONG_SELL']
      if (!validRecommendations.includes(analysis.recommendation)) {
        analysis.recommendation = 'NEUTRAL'
      }

      // Set default values for missing fields
      analysis.entryPrice = analysis.entryPrice || marketData.currentPrice
      analysis.stopLoss = analysis.stopLoss || marketData.currentPrice * 0.95
      analysis.takeProfit = analysis.takeProfit || marketData.currentPrice * 1.05
      analysis.timeframe = analysis.timeframe || '4h'
      analysis.riskLevel = analysis.riskLevel || 'MEDIUM'

      return analysis
    } catch (error) {
      console.warn('Failed to parse AI response, using fallback analysis:', error)
      return this.createFallbackAnalysis(marketData)
    }
  }

  /**
 * Genereert een fallback analyse als de AI faalt.
 * @param marketData De marktgegevens.
 * @returns Het fallback analyseresultaat.
 */
private getFallbackAnalysis(marketData: MarketData): AnalysisResult {
    const fallbackData = this.createFallbackAnalysis(marketData)!

    return {
      type: 'technical_analysis',
      symbol: marketData.symbol,
      recommendation: fallbackData.recommendation,
      confidence: fallbackData.confidence,
      reasoning: fallbackData.reasoning,
      data: fallbackData,
      timestamp: new Date()
    }
  }

  private createFallbackAnalysis(marketData: MarketData): any {
    // Simple rule-based analysis as fallback
    const rsi = marketData.indicators.rsi
    let recommendation = 'NEUTRAL'
    let confidence = 50

    if (rsi < 30) {
      recommendation = 'BUY'
      confidence = 70
    } else if (rsi > 70) {
      recommendation = 'SELL'
      confidence = 70
    } else if (rsi < 40) {
      recommendation = 'BUY'
      confidence = 60
    } else if (rsi > 60) {
      recommendation = 'SELL'
      confidence = 60
    }

    return {
      recommendation,
      confidence,
      entryPrice: marketData.currentPrice,
      stopLoss: marketData.currentPrice * (recommendation === 'BUY' ? 0.95 : 1.05),
      takeProfit: marketData.currentPrice * (recommendation === 'BUY' ? 1.05 : 0.95),
      reasoning: `Fallback analysis based on RSI (${rsi.toFixed(2)}). AI analysis unavailable.`,
      keyLevels: {
        support: [marketData.currentPrice * 0.95, marketData.currentPrice * 0.90],
        resistance: [marketData.currentPrice * 1.05, marketData.currentPrice * 1.10]
      },
      timeframe: '4h',
      riskLevel: 'HIGH',
      technicalSignals: {
        trend: 'NEUTRAL',
        momentum: 'NEUTRAL',
        volume: 'NORMAL'
      },
      patternAnalysis: 'Fallback analysis - pattern recognition unavailable',
      marketStructure: 'Basic support/resistance levels calculated'
    }
  }

  /**
 * Bereken de RSI indicator.
 * @param prices Array van prijzen.
 * @param period De periode voor berekening (default 14).
 * @returns De berekende RSI waarde.
 */
private calculateRSI(prices: number[], period: number = 14): number {
    if (prices.length < period) {
      throw new Error('Not enough data for RSI calculation')
    }

    let gains = 0
    let losses = 0

    for (let i = 1; i < period; i++) {
      const change = prices[i] - prices[i - 1]
      if (change > 0) {
        gains += change
      } else {
        losses += Math.abs(change)
      }
    }

    const avgGain = gains / period
    const avgLoss = losses / period
    const rs = avgGain / avgLoss
    const rsi = 100 - (100 / (1 + rs))

    return rsi
  }

  /**
 * Bereken de MACD indicator.
 * @param prices Array van prijzen.
 * @param fastPeriod Snelle periode (default 12).
 * @param slowPeriod Langzame periode (default 26).
 * @param signalPeriod Signaal periode (default 9).
 * @returns Het MACD object.
 */
private calculateMACD(prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9): { macd: number; signal: number; histogram: number } {
    const ema12 = this.calculateEMA(prices, fastPeriod)!
    const ema26 = this.calculateEMA(prices, slowPeriod)!
    
    const macdLine = ema12 - ema26
    const signalLine = this.calculateEMA([macdLine], signalPeriod)!
    const histogram = macdLine - signalLine

    return {
      macd: macdLine,
      signal: signalLine,
      histogram: histogram
    }
  }

  private calculateEMA(prices: number[], period: number): number {
    const multiplier = 2 / (period + 1)
    let ema = prices[0]

    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier))
    }

    return ema
  }

  /**
 * Bereken de Bollinger Bands.
 * @param prices Array van prijzen.
 * @param period De periode (default 20).
 * @param stdDev Standaard deviatie multiplier (default 2).
 * @returns Het Bollinger Bands object.
 */
private calculateBollingerBands(prices: number[], period: number = 20, stdDev: number = 2): { upper: number; middle: number; lower: number } {
    const sma = prices.slice(-period).reduce((sum: number, price: number) => sum + price, 0) / period
    
    const variance = prices.slice(-period).reduce((sum: number, price: number) => {
      return sum + Math.pow(price - sma, 2)
    }, 0) / period
    
    const standardDeviation = Math.sqrt(variance)
    
    return {
      upper: sma + (standardDeviation * stdDev),
      middle: sma,
      lower: sma - (standardDeviation * stdDev)
    }
  }
}