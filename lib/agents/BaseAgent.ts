import { aiProviderManager, AIRequest, AIResponse } from '../ai/AIProviderManager'
import { aiConfigManager } from '../ai/AIConfigManager'

export interface AgentConfig {
  id: string
  name: string
  specialization: string
  aiProvider: string
  aiModel: string
}

export interface AnalysisResult {
  type: string
  symbol: string
  recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL'
  confidence: number
  reasoning: string
  data: any
  timestamp: Date
}

export abstract class BaseAgent {
  protected id: string
  protected name: string
  protected specialization: string
  protected aiProvider: string
  protected aiModel: string
  protected status: 'ACTIVE' | 'THINKING' | 'ERROR' | 'IDLE' = 'IDLE'
  protected performance: number = 0
  protected currentTask?: string

  constructor(config: AgentConfig) {
    this.id = config.id
    this.name = config.name
    this.specialization = config.specialization
    this.aiProvider = config.aiProvider
    this.aiModel = config.aiModel
  }

  abstract analyze(data: any): Promise<AnalysisResult>

  protected async callAI(prompt: string, systemPrompt?: string): Promise<string> {
    this.status = 'THINKING'
    this.updateStatus()

    try {
      // Get the best model for this agent
      const modelId = aiConfigManager.getBestModelForAgent(this.id)
      if (!modelId) {
        throw new Error(`No available AI model configured for agent ${this.id}`)
      }

      // Get agent configuration
      const agentConfig = aiConfigManager.getAgentConfig(this.id)
      const defaultConfig = {
        temperature: 0.3,
        maxTokens: 2048,
        systemPrompt: `You are ${this.name}, specialized in ${this.specialization}.`
      }

      const config = agentConfig || defaultConfig

      // Prepare the AI request
      const messages: AIRequest['messages'] = []

      if (systemPrompt || config.systemPrompt) {
        messages.push({
          role: 'system',
          content: systemPrompt || config.systemPrompt
        })
      }

      messages.push({
        role: 'user',
        content: prompt
      })

      const request: AIRequest = {
        model: modelId,
        messages,
        temperature: config.temperature,
        maxTokens: config.maxTokens
      }

      // Make the AI request
      const response: AIResponse = await aiProviderManager.makeRequest(modelId, request)

      // Track usage
      if (response.usage) {
        const model = aiProviderManager.getModel(modelId)
        if (model) {
          const cost = this.calculateCost(response.usage, model)
          aiConfigManager.trackUsage(modelId, response.usage.totalTokens, cost)
        }
      }

      this.status = 'ACTIVE'
      this.updateStatus()

      return response.content
    } catch (error) {
      this.status = 'ERROR'
      this.updateStatus()
      console.error(`AI call failed for agent ${this.id}:`, error)
      throw error
    }
  }

  private calculateCost(usage: { promptTokens: number, completionTokens: number }, model: any): number {
    const inputCost = (usage.promptTokens / 1000000) * model.pricing.input
    const outputCost = (usage.completionTokens / 1000000) * model.pricing.output
    return inputCost + outputCost
  }

  protected async updateStatus() {
    // Update database with current status
    await fetch('/api/agents/update-status', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        agentId: this.id,
        status: this.status,
        currentTask: this.currentTask,
        performance: this.performance
      })
    })
  }

  getStatus(): string {
    return this.status
  }

  getPerformance(): number {
    return this.performance
  }

  getCurrentTask(): string | undefined {
    return this.currentTask
  }

  async setPerformance(score: number) {
    this.performance = Math.max(0, Math.min(100, score))
    await this.updateStatus()
  }

  async setCurrentTask(task: string) {
    this.currentTask = task
    await this.updateStatus()
  }
} 