export interface AgentConfig {
  id: string
  name: string
  specialization: string
  aiProvider: string
  aiModel: string
}

export interface AnalysisResult {
  type: string
  symbol: string
  recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL'
  confidence: number
  reasoning: string
  data: any
  timestamp: Date
}

export abstract class BaseAgent {
  protected id: string
  protected name: string
  protected specialization: string
  protected aiProvider: string
  protected aiModel: string
  protected status: 'ACTIVE' | 'THINKING' | 'ERROR' | 'IDLE' = 'IDLE'
  protected performance: number = 0
  protected currentTask?: string

  constructor(config: AgentConfig) {
    this.id = config.id
    this.name = config.name
    this.specialization = config.specialization
    this.aiProvider = config.aiProvider
    this.aiModel = config.aiModel
  }

  abstract analyze(data: any): Promise<AnalysisResult>

  protected async callAI(prompt: string): Promise<string> {
    this.status = 'THINKING'
    this.updateStatus()

    try {
      const response = await fetch(`/api/ai/${this.aiProvider}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: this.aiModel,
          prompt: prompt,
          temperature: 0.3,
          maxTokens: 2048
        })
      })

      const result = await response.json()
      this.status = 'ACTIVE'
      this.updateStatus()
      
      return result.content
    } catch (error) {
      this.status = 'ERROR'
      this.updateStatus()
      throw error
    }
  }

  protected async updateStatus() {
    // Update database with current status
    await fetch('/api/agents/update-status', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        agentId: this.id,
        status: this.status,
        currentTask: this.currentTask,
        performance: this.performance
      })
    })
  }

  getStatus(): string {
    return this.status
  }

  getPerformance(): number {
    return this.performance
  }

  getCurrentTask(): string | undefined {
    return this.currentTask
  }

  async setPerformance(score: number) {
    this.performance = Math.max(0, Math.min(100, score))
    await this.updateStatus()
  }

  async setCurrentTask(task: string) {
    this.currentTask = task
    await this.updateStatus()
  }
} 