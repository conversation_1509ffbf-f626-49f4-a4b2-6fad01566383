/**
 * Enhanced Logging System for CryptoAgent Pro
 * Provides structured logging with different levels and contexts
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4
}

export interface LogEntry {
  timestamp: string
  level: LogLevel
  context: string
  message: string
  data?: any
  error?: Error
  exchangeId?: string
  symbol?: string
  userId?: string
}

class Logger {
  private logLevel: LogLevel
  private logs: LogEntry[] = []
  private maxLogs: number = 1000

  constructor(level: LogLevel = LogLevel.INFO) {
    this.logLevel = level
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel
  }

  private createLogEntry(
    level: LogLevel,
    context: string,
    message: string,
    data?: any,
    error?: Error
  ): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      context,
      message,
      data,
      error
    }

    // Add to memory store
    this.logs.push(entry)
    if (this.logs.length > this.maxLogs) {
      this.logs.shift() // Remove oldest log
    }

    return entry
  }

  private formatLogMessage(entry: LogEntry): string {
    const levelNames = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'CRITICAL']
    const levelName = levelNames[entry.level]
    
    let message = `[${entry.timestamp}] ${levelName} [${entry.context}] ${entry.message}`
    
    if (entry.data) {
      message += ` | Data: ${JSON.stringify(entry.data)}`
    }
    
    if (entry.error) {
      message += ` | Error: ${entry.error.message}`
      if (entry.error.stack) {
        message += `\nStack: ${entry.error.stack}`
      }
    }
    
    return message
  }

  private outputLog(entry: LogEntry): void {
    const message = this.formatLogMessage(entry)
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message)
        break
      case LogLevel.INFO:
        console.info(message)
        break
      case LogLevel.WARN:
        console.warn(message)
        break
      case LogLevel.ERROR:
      case LogLevel.CRITICAL:
        console.error(message)
        break
    }
  }

  debug(context: string, message: string, data?: any): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return
    const entry = this.createLogEntry(LogLevel.DEBUG, context, message, data)
    this.outputLog(entry)
  }

  info(context: string, message: string, data?: any): void {
    if (!this.shouldLog(LogLevel.INFO)) return
    const entry = this.createLogEntry(LogLevel.INFO, context, message, data)
    this.outputLog(entry)
  }

  warn(context: string, message: string, data?: any): void {
    if (!this.shouldLog(LogLevel.WARN)) return
    const entry = this.createLogEntry(LogLevel.WARN, context, message, data)
    this.outputLog(entry)
  }

  error(context: string, message: string, error?: Error, data?: any): void {
    if (!this.shouldLog(LogLevel.ERROR)) return
    const entry = this.createLogEntry(LogLevel.ERROR, context, message, data, error)
    this.outputLog(entry)
  }

  critical(context: string, message: string, error?: Error, data?: any): void {
    if (!this.shouldLog(LogLevel.CRITICAL)) return
    const entry = this.createLogEntry(LogLevel.CRITICAL, context, message, data, error)
    this.outputLog(entry)
  }

  // Exchange-specific logging
  exchangeLog(
    exchangeId: string,
    level: LogLevel,
    message: string,
    data?: any,
    error?: Error
  ): void {
    if (!this.shouldLog(level)) return
    const entry = this.createLogEntry(level, `Exchange:${exchangeId}`, message, data, error)
    entry.exchangeId = exchangeId
    this.outputLog(entry)
  }

  // Trading-specific logging
  tradingLog(
    exchangeId: string,
    symbol: string,
    level: LogLevel,
    message: string,
    data?: any,
    error?: Error
  ): void {
    if (!this.shouldLog(level)) return
    const entry = this.createLogEntry(level, `Trading:${exchangeId}:${symbol}`, message, data, error)
    entry.exchangeId = exchangeId
    entry.symbol = symbol
    this.outputLog(entry)
  }

  // Get recent logs
  getRecentLogs(count: number = 100): LogEntry[] {
    return this.logs.slice(-count)
  }

  // Get logs by context
  getLogsByContext(context: string): LogEntry[] {
    return this.logs.filter(log => log.context.includes(context))
  }

  // Get logs by level
  getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logs.filter(log => log.level >= level)
  }

  // Clear logs
  clearLogs(): void {
    this.logs = []
  }

  // Set log level
  setLogLevel(level: LogLevel): void {
    this.logLevel = level
  }
}

// Singleton logger instance
export const logger = new Logger(
  process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO
)

// Exchange error handling utilities
export class ExchangeError extends Error {
  constructor(
    public exchangeId: string,
    public originalError: Error,
    public context: string = 'unknown'
  ) {
    super(`${exchangeId}: ${originalError.message}`)
    this.name = 'ExchangeError'
  }
}

export class RateLimitError extends ExchangeError {
  constructor(exchangeId: string, retryAfter?: number) {
    super(exchangeId, new Error('Rate limit exceeded'), 'rate_limit')
    this.name = 'RateLimitError'
    if (retryAfter) {
      this.message += ` (retry after ${retryAfter}ms)`
    }
  }
}

export class NetworkError extends ExchangeError {
  constructor(exchangeId: string, originalError: Error) {
    super(exchangeId, originalError, 'network')
    this.name = 'NetworkError'
  }
}

export class AuthenticationError extends ExchangeError {
  constructor(exchangeId: string, originalError: Error) {
    super(exchangeId, originalError, 'authentication')
    this.name = 'AuthenticationError'
  }
}

// Error classification utility
export function classifyExchangeError(exchangeId: string, error: any): ExchangeError {
  const message = error.message?.toLowerCase() || ''
  
  if (message.includes('rate limit') || message.includes('too many requests')) {
    return new RateLimitError(exchangeId)
  }
  
  if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {
    return new NetworkError(exchangeId, error)
  }
  
  if (message.includes('api key') || message.includes('authentication') || message.includes('unauthorized')) {
    return new AuthenticationError(exchangeId, error)
  }
  
  return new ExchangeError(exchangeId, error)
}

// Retry utility with exponential backoff
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  context: string = 'operation'
): Promise<T> {
  let lastError: Error
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.debug('Retry', `Attempting ${context} (${attempt}/${maxRetries})`)
      return await operation()
    } catch (error) {
      lastError = error as Error
      
      if (attempt === maxRetries) {
        logger.error('Retry', `Final attempt failed for ${context}`, lastError)
        break
      }
      
      const delay = baseDelay * Math.pow(2, attempt - 1)
      logger.warn('Retry', `Attempt ${attempt} failed for ${context}, retrying in ${delay}ms`, { error: lastError.message })
      
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError!
}

export default logger
