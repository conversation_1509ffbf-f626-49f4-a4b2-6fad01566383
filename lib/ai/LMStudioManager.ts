/**
 * Advanced LM Studio Manager for CryptoAgent Pro
 * Intelligent model selection and management
 */

interface ModelConfig {
  name: string
  size: string
  strengths: string[]
  maxTokens: number
  priority: number
}

interface ModelResponse {
  success: boolean
  content?: string
  model?: string
  usage?: any
  error?: string
  fallback?: string
}

interface SystemStatus {
  available: boolean
  loadedModels?: number
  recommendation?: any
  error?: string
}

export class LMStudioManager {
  private baseUrl: string
  private models: Record<string, ModelConfig>
  private currentModel: string | null
  private isLoading: boolean

  constructor() {
    this.baseUrl = process.env.LM_STUDIO_BASE_URL || 'http://localhost:1234'
    this.models = {
      'phi-4-mini': {
        name: 'microsoft/phi-4-mini-reasoning',
        size: '2.18GB',
        strengths: ['general', 'reasoning', 'fast'],
        maxTokens: 4096,
        priority: 1 // Laagste geheugengebruik
      },
      'gpt-oss': {
        name: 'openai/gpt-oss-20b',
        size: '12.11GB', 
        strengths: ['conversation', 'creative', 'analysis'],
        maxTokens: 8192,
        priority: 2
      },
      'deepseek-coder': {
        name: 'deepseek-coder-33b-instruct',
        size: '19.94GB',
        strengths: ['coding', 'programming', 'technical'],
        maxTokens: 16384,
        priority: 3 // Hoogste geheugengebruik
      }
    }
    
    this.currentModel = null
    this.isLoading = false
  }

  // Intelligente model selectie op basis van prompt
  selectModel(prompt: string): string {
    const text = prompt.toLowerCase()
    
    // Crypto trading gerelateerde keywords
    const tradingKeywords = [
      'trading', 'crypto', 'bitcoin', 'ethereum', 'price', 'market', 'analysis',
      'portfolio', 'exchange', 'order', 'buy', 'sell', 'strategy', 'signal'
    ]
    
    // Programmeer gerelateerde keywords
    const codingKeywords = [
      'code', 'programming', 'function', 'debug', 'javascript', 'typescript', 'python', 
      'react', 'api', 'algorithm', 'bug', 'script', 'development',
      'html', 'css', 'database', 'sql', 'git', 'repository', 'component'
    ]
    
    // Creatieve/complexe taken
    const creativeKeywords = [
      'story', 'creative', 'analyze', 'essay', 'complex', 'detailed',
      'research', 'explain in detail', 'comprehensive', 'elaborate'
    ]
    
    // Check voor coding taken (prioriteit voor crypto app development)
    if (codingKeywords.some(keyword => text.includes(keyword))) {
      return 'deepseek-coder'
    }
    
    // Check voor trading analysis (gebruik creatief model)
    if (tradingKeywords.some(keyword => text.includes(keyword))) {
      return 'gpt-oss'
    }
    
    // Check voor creatieve/complexe taken
    if (creativeKeywords.some(keyword => text.includes(keyword)) || prompt.length > 500) {
      return 'gpt-oss'
    }
    
    // Default naar snelste model
    return 'phi-4-mini'
  }

  // API call naar LM Studio
  async callModel(prompt: string, selectedModel: string | null = null): Promise<ModelResponse> {
    try {
      // Auto-select model als niet gespecificeerd
      const modelKey = selectedModel || this.selectModel(prompt)
      const model = this.models[modelKey]
      
      if (!model) {
        throw new Error(`Model ${modelKey} not found`)
      }
      
      console.log(`🤖 Using model: ${model.name} (${model.size})`)
      
      const response = await fetch(`${this.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.LM_STUDIO_API_KEY || 'lm-studio'}`
        },
        body: JSON.stringify({
          model: model.name,
          messages: [
            {
              role: 'system',
              content: this.getSystemPrompt(modelKey)
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: model.maxTokens,
          temperature: 0.7,
          stream: false
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      
      return {
        success: true,
        content: data.choices[0].message.content,
        model: model.name,
        usage: data.usage
      }

    } catch (error) {
      console.error('❌ LM Studio API Error:', error)
      
      const fallbackContent = await this.fallbackToSimpleModel(prompt)
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        fallback: fallbackContent
      }
    }
  }

  // System prompts voor verschillende use cases
  private getSystemPrompt(modelKey: string): string {
    const prompts = {
      'phi-4-mini': 'You are a helpful AI assistant for CryptoAgent Pro. Provide concise, accurate responses.',
      'gpt-oss': 'You are an expert crypto trading analyst and advisor for CryptoAgent Pro. Provide detailed market analysis and trading insights.',
      'deepseek-coder': 'You are an expert software developer specializing in TypeScript, React, and crypto trading applications. Provide clean, well-documented code solutions.'
    }
    
    return prompts[modelKey] || prompts['phi-4-mini']
  }

  // Fallback naar het simpelste model
  async fallbackToSimpleModel(prompt: string): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.LM_STUDIO_API_KEY || 'lm-studio'}`
        },
        body: JSON.stringify({
          model: this.models['phi-4-mini'].name,
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 1000,
          temperature: 0.5
        })
      })

      if (response.ok) {
        const data = await response.json()
        return data.choices[0].message.content
      }
      
      return "Sorry, all models are temporarily unavailable."
    } catch (error) {
      return "Sorry, all models are temporarily unavailable."
    }
  }

  // Batch processing voor meerdere prompts
  async batchProcess(prompts: string[]): Promise<ModelResponse[]> {
    const results: ModelResponse[] = []
    
    for (const prompt of prompts) {
      const result = await this.callModel(prompt)
      results.push(result)
      
      // Kleine delay om overbelasting te voorkomen
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    return results
  }

  // Geheugenstatus check
  async getSystemStatus(): Promise<SystemStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/models`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }
      
      const data = await response.json()
      
      return {
        available: true,
        loadedModels: data.data?.length || 0,
        recommendation: this.getMemoryRecommendation()
      }
    } catch (error) {
      return {
        available: false,
        error: 'LM Studio server not reachable'
      }
    }
  }

  getMemoryRecommendation() {
    // Basis aanbevelingen voor Mac performance
    return {
      optimal: "Gebruik alleen Phi-4-mini voor beste performance (2.18GB)",
      balanced: "Phi-4-mini + GPT-OSS voor gevarieerde taken (14.29GB totaal)",
      maximum: "Alle modellen voor complete functionaliteit (34.23GB totaal)",
      current: this.getCurrentMemoryUsage()
    }
  }

  private getCurrentMemoryUsage() {
    // Simuleer geheugengebruik op basis van geladen modellen
    return {
      estimated: "2.18GB", // Basis schatting
      recommendation: "Monitor system performance and adjust model usage accordingly"
    }
  }

  // Crypto-specific helper methods
  async analyzeCryptoData(data: any, analysisType: string): Promise<ModelResponse> {
    const prompt = this.buildCryptoAnalysisPrompt(data, analysisType)
    return await this.callModel(prompt, 'gpt-oss') // Gebruik analytisch model
  }

  async generateTradingCode(requirements: string): Promise<ModelResponse> {
    const prompt = `Generate TypeScript code for CryptoAgent Pro: ${requirements}`
    return await this.callModel(prompt, 'deepseek-coder') // Gebruik coding model
  }

  private buildCryptoAnalysisPrompt(data: any, analysisType: string): string {
    return `
Analyze the following crypto market data for ${analysisType}:

Data: ${JSON.stringify(data, null, 2)}

Please provide:
1. Key insights
2. Market trends
3. Risk assessment
4. Trading recommendations

Format the response in a structured way suitable for a trading application.
    `.trim()
  }

  // Model management
  getAvailableModels(): Record<string, ModelConfig> {
    return this.models
  }

  getModelInfo(modelKey: string): ModelConfig | null {
    return this.models[modelKey] || null
  }

  setCurrentModel(modelKey: string): boolean {
    if (this.models[modelKey]) {
      this.currentModel = modelKey
      return true
    }
    return false
  }

  getCurrentModel(): string | null {
    return this.currentModel
  }
}

// Singleton instance
export const lmStudioManager = new LMStudioManager()

export default LMStudioManager
