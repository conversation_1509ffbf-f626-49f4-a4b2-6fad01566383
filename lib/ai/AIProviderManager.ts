export interface AIModel {
  id: string
  name: string
  provider: string
  description: string
  contextLength: number
  pricing: {
    input: number  // per 1M tokens
    output: number // per 1M tokens
  }
  capabilities: string[]
  category: 'general' | 'coding' | 'reasoning' | 'creative' | 'analysis'
}

export interface AIProvider {
  id: string
  name: string
  baseUrl: string
  apiKeyRequired: boolean
  models: AIModel[]
  headers?: Record<string, string>
}

export interface AIRequest {
  model: string
  messages: Array<{
    role: 'system' | 'user' | 'assistant'
    content: string
  }>
  temperature?: number
  maxTokens?: number
  stream?: boolean
}

export interface AIResponse {
  content: string
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  model: string
  provider: string
}

export class AIProviderManager {
  private providers: Map<string, AIProvider> = new Map()
  private apiKeys: Map<string, string> = new Map()

  constructor() {
    this.initializeProviders()
  }

  private initializeProviders() {
    // OpenRouter - Access to 100+ models
    this.providers.set('openrouter', {
      id: 'openrouter',
      name: 'OpenRouter',
      baseUrl: 'https://openrouter.ai/api/v1',
      apiKeyRequired: true,
      models: [
        {
          id: 'anthropic/claude-3.5-sonnet',
          name: 'Claude 3.5 Sonnet',
          provider: 'openrouter',
          description: 'Most intelligent model, excellent for complex analysis',
          contextLength: 200000,
          pricing: { input: 3, output: 15 },
          capabilities: ['reasoning', 'analysis', 'coding', 'creative'],
          category: 'reasoning'
        },
        {
          id: 'openai/gpt-4o',
          name: 'GPT-4o',
          provider: 'openrouter',
          description: 'Latest GPT-4 with vision and multimodal capabilities',
          contextLength: 128000,
          pricing: { input: 5, output: 15 },
          capabilities: ['reasoning', 'analysis', 'vision', 'coding'],
          category: 'general'
        },
        {
          id: 'google/gemini-pro-1.5',
          name: 'Gemini Pro 1.5',
          provider: 'openrouter',
          description: 'Google\'s advanced model with large context window',
          contextLength: 1000000,
          pricing: { input: 2.5, output: 7.5 },
          capabilities: ['reasoning', 'analysis', 'coding'],
          category: 'analysis'
        },
        {
          id: 'meta-llama/llama-3.1-405b-instruct',
          name: 'Llama 3.1 405B',
          provider: 'openrouter',
          description: 'Meta\'s largest open-source model',
          contextLength: 32768,
          pricing: { input: 3, output: 3 },
          capabilities: ['reasoning', 'analysis', 'coding'],
          category: 'reasoning'
        },
        {
          id: 'anthropic/claude-3-haiku',
          name: 'Claude 3 Haiku',
          provider: 'openrouter',
          description: 'Fast and cost-effective for simple tasks',
          contextLength: 200000,
          pricing: { input: 0.25, output: 1.25 },
          capabilities: ['analysis', 'creative'],
          category: 'general'
        },
        {
          id: 'openai/gpt-3.5-turbo',
          name: 'GPT-3.5 Turbo',
          provider: 'openrouter',
          description: 'Fast and affordable for most tasks',
          contextLength: 16385,
          pricing: { input: 0.5, output: 1.5 },
          capabilities: ['analysis', 'coding'],
          category: 'general'
        }
      ]
    })

    // LM Studio - Local models
    this.providers.set('lmstudio', {
      id: 'lmstudio',
      name: 'LM Studio',
      baseUrl: 'http://localhost:1234/v1',
      apiKeyRequired: false,
      models: [
        {
          id: 'local-model',
          name: 'Local Model',
          provider: 'lmstudio',
          description: 'Locally running model via LM Studio',
          contextLength: 4096,
          pricing: { input: 0, output: 0 },
          capabilities: ['analysis', 'reasoning'],
          category: 'general'
        }
      ]
    })

    // OpenAI Direct
    this.providers.set('openai', {
      id: 'openai',
      name: 'OpenAI',
      baseUrl: 'https://api.openai.com/v1',
      apiKeyRequired: true,
      models: [
        {
          id: 'gpt-4o',
          name: 'GPT-4o',
          provider: 'openai',
          description: 'Latest GPT-4 model with vision capabilities',
          contextLength: 128000,
          pricing: { input: 5, output: 15 },
          capabilities: ['reasoning', 'analysis', 'vision', 'coding'],
          category: 'general'
        },
        {
          id: 'gpt-4o-mini',
          name: 'GPT-4o Mini',
          provider: 'openai',
          description: 'Smaller, faster version of GPT-4o',
          contextLength: 128000,
          pricing: { input: 0.15, output: 0.6 },
          capabilities: ['analysis', 'coding'],
          category: 'general'
        }
      ]
    })

    // Anthropic Direct
    this.providers.set('anthropic', {
      id: 'anthropic',
      name: 'Anthropic',
      baseUrl: 'https://api.anthropic.com/v1',
      apiKeyRequired: true,
      models: [
        {
          id: 'claude-3-5-sonnet-20241022',
          name: 'Claude 3.5 Sonnet',
          provider: 'anthropic',
          description: 'Most capable Claude model for complex reasoning',
          contextLength: 200000,
          pricing: { input: 3, output: 15 },
          capabilities: ['reasoning', 'analysis', 'coding', 'creative'],
          category: 'reasoning'
        }
      ]
    })
  }

  // Set API key for a provider
  setApiKey(providerId: string, apiKey: string) {
    this.apiKeys.set(providerId, apiKey)
  }

  // Get all available providers
  getProviders(): AIProvider[] {
    return Array.from(this.providers.values())
  }

  // Get models for a specific provider
  getModels(providerId?: string): AIModel[] {
    if (providerId) {
      const provider = this.providers.get(providerId)
      return provider ? provider.models : []
    }
    
    // Return all models from all providers
    const allModels: AIModel[] = []
    this.providers.forEach(provider => {
      allModels.push(...provider.models)
    })
    return allModels
  }

  // Get models by category
  getModelsByCategory(category: AIModel['category']): AIModel[] {
    return this.getModels().filter(model => model.category === category)
  }

  // Get model by ID
  getModel(modelId: string): AIModel | null {
    for (const provider of this.providers.values()) {
      const model = provider.models.find(m => m.id === modelId)
      if (model) return model
    }
    return null
  }

  // Check if provider is available
  async checkProviderAvailability(providerId: string): Promise<boolean> {
    const provider = this.providers.get(providerId)
    if (!provider) return false

    try {
      const response = await fetch(`${provider.baseUrl}/models`, {
        method: 'GET',
        headers: this.getHeaders(providerId),
        signal: AbortSignal.timeout(5000) // 5 second timeout
      })
      return response.ok
    } catch (error) {
      console.warn(`Provider ${providerId} not available:`, error)
      return false
    }
  }

  // Make AI request
  async makeRequest(modelId: string, request: AIRequest): Promise<AIResponse> {
    const model = this.getModel(modelId)
    if (!model) {
      throw new Error(`Model ${modelId} not found`)
    }

    const provider = this.providers.get(model.provider)
    if (!provider) {
      throw new Error(`Provider ${model.provider} not found`)
    }

    // Check if API key is required and available
    if (provider.apiKeyRequired && !this.apiKeys.has(provider.id)) {
      throw new Error(`API key required for provider ${provider.id}`)
    }

    const headers = this.getHeaders(provider.id)
    const body = this.formatRequest(provider.id, request)

    try {
      const response = await fetch(`${provider.baseUrl}/chat/completions`, {
        method: 'POST',
        headers,
        body: JSON.stringify(body)
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`AI request failed: ${response.status} ${errorText}`)
      }

      const data = await response.json()
      return this.formatResponse(provider.id, data, model)

    } catch (error) {
      console.error(`AI request failed for ${modelId}:`, error)
      throw error
    }
  }

  private getHeaders(providerId: string): Record<string, string> {
    const provider = this.providers.get(providerId)
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }

    if (provider?.apiKeyRequired) {
      const apiKey = this.apiKeys.get(providerId)
      if (apiKey) {
        switch (providerId) {
          case 'openrouter':
            headers['Authorization'] = `Bearer ${apiKey}`
            headers['HTTP-Referer'] = 'https://cryptoagent.pro'
            headers['X-Title'] = 'CryptoAgent Pro'
            break
          case 'openai':
            headers['Authorization'] = `Bearer ${apiKey}`
            break
          case 'anthropic':
            headers['x-api-key'] = apiKey
            headers['anthropic-version'] = '2023-06-01'
            break
        }
      }
    }

    return headers
  }

  private formatRequest(providerId: string, request: AIRequest): any {
    const baseRequest = {
      model: request.model,
      messages: request.messages,
      temperature: request.temperature || 0.3,
      max_tokens: request.maxTokens || 2048,
      stream: request.stream || false
    }

    // Provider-specific formatting
    switch (providerId) {
      case 'anthropic':
        // Anthropic uses different parameter names
        return {
          model: request.model,
          messages: request.messages,
          temperature: request.temperature || 0.3,
          max_tokens: request.maxTokens || 2048
        }
      default:
        return baseRequest
    }
  }

  private formatResponse(providerId: string, data: any, model: AIModel): AIResponse {
    let content = ''
    let usage = undefined

    // Handle different response formats
    if (data.choices && data.choices[0]) {
      content = data.choices[0].message?.content || data.choices[0].text || ''
    } else if (data.content) {
      // Anthropic format
      content = Array.isArray(data.content) ? data.content[0]?.text || '' : data.content
    }

    if (data.usage) {
      usage = {
        promptTokens: data.usage.prompt_tokens || 0,
        completionTokens: data.usage.completion_tokens || 0,
        totalTokens: data.usage.total_tokens || 0
      }
    }

    return {
      content,
      usage,
      model: model.id,
      provider: model.provider
    }
  }

  // Get recommended models for trading analysis
  getRecommendedModels(): { 
    fast: AIModel[], 
    balanced: AIModel[], 
    powerful: AIModel[] 
  } {
    const models = this.getModels()
    
    return {
      fast: models.filter(m => 
        m.pricing.input < 1 && m.capabilities.includes('analysis')
      ).slice(0, 3),
      balanced: models.filter(m => 
        m.pricing.input >= 1 && m.pricing.input <= 3 && 
        m.capabilities.includes('reasoning')
      ).slice(0, 3),
      powerful: models.filter(m => 
        m.capabilities.includes('reasoning') && 
        m.contextLength > 100000
      ).slice(0, 3)
    }
  }
}

// Singleton instance
export const aiProviderManager = new AIProviderManager()
