import { aiProviderManager, AIModel } from './AIProviderManager'

export interface AIAgentConfig {
  agentId: string
  agentName: string
  primaryModel: string
  fallbackModel?: string
  temperature: number
  maxTokens: number
  systemPrompt: string
  enabled: boolean
  priority: number
}

export interface AIProviderConfig {
  providerId: string
  apiKey?: string
  baseUrl?: string
  enabled: boolean
  rateLimit?: {
    requestsPerMinute: number
    tokensPerMinute: number
  }
}

export interface AIGlobalConfig {
  defaultProvider: string
  fallbackProvider: string
  maxRetries: number
  retryDelay: number
  timeout: number
  costLimit: {
    dailyLimit: number
    monthlyLimit: number
    alertThreshold: number
  }
  monitoring: {
    logRequests: boolean
    trackUsage: boolean
    trackPerformance: boolean
  }
}

export class AIConfigManager {
  private agentConfigs: Map<string, AIAgentConfig> = new Map()
  private providerConfigs: Map<string, AIProviderConfig> = new Map()
  private globalConfig: AIGlobalConfig
  private usage: Map<string, { tokens: number, cost: number, requests: number }> = new Map()

  constructor() {
    this.globalConfig = this.getDefaultGlobalConfig()
    this.loadConfigurations()
  }

  private getDefaultGlobalConfig(): AIGlobalConfig {
    return {
      defaultProvider: 'openrouter',
      fallbackProvider: 'lmstudio',
      maxRetries: 3,
      retryDelay: 1000,
      timeout: 30000,
      costLimit: {
        dailyLimit: 10.0, // $10 per day
        monthlyLimit: 200.0, // $200 per month
        alertThreshold: 0.8 // Alert at 80% of limit
      },
      monitoring: {
        logRequests: true,
        trackUsage: true,
        trackPerformance: true
      }
    }
  }

  private loadConfigurations() {
    // Load from localStorage or environment variables
    this.loadFromStorage()
    this.loadFromEnvironment()
    this.initializeDefaultAgentConfigs()
  }

  private loadFromStorage() {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem('ai-config')
        if (stored) {
          const config = JSON.parse(stored)
          if (config.global) this.globalConfig = { ...this.globalConfig, ...config.global }
          if (config.providers) {
            Object.entries(config.providers).forEach(([id, config]) => {
              this.providerConfigs.set(id, config as AIProviderConfig)
            })
          }
          if (config.agents) {
            Object.entries(config.agents).forEach(([id, config]) => {
              this.agentConfigs.set(id, config as AIAgentConfig)
            })
          }
        }
      } catch (error) {
        console.warn('Failed to load AI config from storage:', error)
      }
    }
  }

  private loadFromEnvironment() {
    // Load API keys from environment variables
    const envKeys = {
      'openrouter': process.env.OPENROUTER_API_KEY,
      'openai': process.env.OPENAI_API_KEY,
      'anthropic': process.env.ANTHROPIC_API_KEY
    }

    Object.entries(envKeys).forEach(([providerId, apiKey]) => {
      if (apiKey) {
        aiProviderManager.setApiKey(providerId, apiKey)
        this.setProviderConfig(providerId, { 
          providerId, 
          apiKey, 
          enabled: true 
        })
      }
    })
  }

  private initializeDefaultAgentConfigs() {
    const defaultConfigs: AIAgentConfig[] = [
      {
        agentId: 'technical-analysis',
        agentName: 'Technical Analysis Agent',
        primaryModel: 'anthropic/claude-3.5-sonnet',
        fallbackModel: 'openai/gpt-4o-mini',
        temperature: 0.2,
        maxTokens: 2048,
        systemPrompt: `You are a professional cryptocurrency technical analyst. Analyze market data and provide clear, actionable trading insights based on technical indicators, chart patterns, and market trends. Always include confidence levels and risk assessments.`,
        enabled: true,
        priority: 1
      },
      {
        agentId: 'sentiment-analysis',
        agentName: 'Sentiment Analysis Agent',
        primaryModel: 'openai/gpt-4o',
        fallbackModel: 'anthropic/claude-3-haiku',
        temperature: 0.3,
        maxTokens: 1024,
        systemPrompt: `You are a cryptocurrency sentiment analyst. Analyze social media, news, and market sentiment to gauge public opinion and market mood. Provide sentiment scores and explain the reasoning behind your analysis.`,
        enabled: true,
        priority: 2
      },
      {
        agentId: 'risk-management',
        agentName: 'Risk Management Agent',
        primaryModel: 'anthropic/claude-3.5-sonnet',
        fallbackModel: 'google/gemini-pro-1.5',
        temperature: 0.1,
        maxTokens: 1536,
        systemPrompt: `You are a cryptocurrency risk management specialist. Evaluate portfolio risk, position sizing, and provide recommendations for risk mitigation. Focus on preserving capital and managing downside risk.`,
        enabled: true,
        priority: 3
      },
      {
        agentId: 'market-research',
        agentName: 'Market Research Agent',
        primaryModel: 'google/gemini-pro-1.5',
        fallbackModel: 'meta-llama/llama-3.1-405b-instruct',
        temperature: 0.4,
        maxTokens: 3072,
        systemPrompt: `You are a cryptocurrency market researcher. Analyze fundamental factors, project developments, regulatory changes, and macro-economic trends that affect cryptocurrency markets. Provide comprehensive research reports.`,
        enabled: true,
        priority: 4
      }
    ]

    defaultConfigs.forEach(config => {
      if (!this.agentConfigs.has(config.agentId)) {
        this.agentConfigs.set(config.agentId, config)
      }
    })
  }

  // Agent Configuration Methods
  getAgentConfig(agentId: string): AIAgentConfig | null {
    return this.agentConfigs.get(agentId) || null
  }

  setAgentConfig(config: AIAgentConfig) {
    this.agentConfigs.set(config.agentId, config)
    this.saveToStorage()
  }

  getAllAgentConfigs(): AIAgentConfig[] {
    return Array.from(this.agentConfigs.values())
  }

  // Provider Configuration Methods
  getProviderConfig(providerId: string): AIProviderConfig | null {
    return this.providerConfigs.get(providerId) || null
  }

  setProviderConfig(providerId: string, config: Partial<AIProviderConfig>) {
    const existing = this.providerConfigs.get(providerId) || { 
      providerId, 
      enabled: false 
    }
    const updated = { ...existing, ...config }
    this.providerConfigs.set(providerId, updated)
    
    // Update API key in provider manager
    if (updated.apiKey) {
      aiProviderManager.setApiKey(providerId, updated.apiKey)
    }
    
    this.saveToStorage()
  }

  getAllProviderConfigs(): AIProviderConfig[] {
    return Array.from(this.providerConfigs.values())
  }

  // Global Configuration Methods
  getGlobalConfig(): AIGlobalConfig {
    return this.globalConfig
  }

  setGlobalConfig(config: Partial<AIGlobalConfig>) {
    this.globalConfig = { ...this.globalConfig, ...config }
    this.saveToStorage()
  }

  // Model Selection Methods
  getBestModelForAgent(agentId: string): string | null {
    const config = this.getAgentConfig(agentId)
    if (!config || !config.enabled) return null

    // Check if primary model is available
    const primaryModel = aiProviderManager.getModel(config.primaryModel)
    if (primaryModel) {
      const providerConfig = this.getProviderConfig(primaryModel.provider)
      if (providerConfig?.enabled) {
        return config.primaryModel
      }
    }

    // Fall back to fallback model
    if (config.fallbackModel) {
      const fallbackModel = aiProviderManager.getModel(config.fallbackModel)
      if (fallbackModel) {
        const providerConfig = this.getProviderConfig(fallbackModel.provider)
        if (providerConfig?.enabled) {
          return config.fallbackModel
        }
      }
    }

    return null
  }

  // Usage Tracking
  trackUsage(modelId: string, tokens: number, cost: number) {
    const existing = this.usage.get(modelId) || { tokens: 0, cost: 0, requests: 0 }
    this.usage.set(modelId, {
      tokens: existing.tokens + tokens,
      cost: existing.cost + cost,
      requests: existing.requests + 1
    })
  }

  getUsage(modelId?: string): Map<string, { tokens: number, cost: number, requests: number }> {
    if (modelId) {
      const usage = this.usage.get(modelId)
      return usage ? new Map([[modelId, usage]]) : new Map()
    }
    return new Map(this.usage)
  }

  getTotalCost(): number {
    let total = 0
    this.usage.forEach(usage => {
      total += usage.cost
    })
    return total
  }

  // Cost Management
  checkCostLimits(): { 
    withinLimits: boolean, 
    dailyUsage: number, 
    monthlyUsage: number,
    warnings: string[] 
  } {
    const totalCost = this.getTotalCost()
    const warnings: string[] = []
    
    const dailyThreshold = this.globalConfig.costLimit.dailyLimit * this.globalConfig.costLimit.alertThreshold
    const monthlyThreshold = this.globalConfig.costLimit.monthlyLimit * this.globalConfig.costLimit.alertThreshold

    if (totalCost > dailyThreshold) {
      warnings.push(`Daily cost approaching limit: $${totalCost.toFixed(2)} / $${this.globalConfig.costLimit.dailyLimit}`)
    }

    if (totalCost > monthlyThreshold) {
      warnings.push(`Monthly cost approaching limit: $${totalCost.toFixed(2)} / $${this.globalConfig.costLimit.monthlyLimit}`)
    }

    return {
      withinLimits: totalCost < this.globalConfig.costLimit.dailyLimit,
      dailyUsage: totalCost,
      monthlyUsage: totalCost, // Would need proper date tracking
      warnings
    }
  }

  // Configuration Persistence
  private saveToStorage() {
    if (typeof window !== 'undefined') {
      try {
        const config = {
          global: this.globalConfig,
          providers: Object.fromEntries(this.providerConfigs),
          agents: Object.fromEntries(this.agentConfigs)
        }
        localStorage.setItem('ai-config', JSON.stringify(config))
      } catch (error) {
        console.warn('Failed to save AI config to storage:', error)
      }
    }
  }

  // Validation Methods
  validateConfiguration(): { valid: boolean, errors: string[] } {
    const errors: string[] = []

    // Check if at least one provider is configured
    const enabledProviders = Array.from(this.providerConfigs.values())
      .filter(config => config.enabled)
    
    if (enabledProviders.length === 0) {
      errors.push('No AI providers are configured and enabled')
    }

    // Check if enabled agents have valid models
    this.agentConfigs.forEach(config => {
      if (config.enabled) {
        const model = aiProviderManager.getModel(config.primaryModel)
        if (!model) {
          errors.push(`Agent ${config.agentName} has invalid primary model: ${config.primaryModel}`)
        }
      }
    })

    return {
      valid: errors.length === 0,
      errors
    }
  }

  // Reset to defaults
  resetToDefaults() {
    this.agentConfigs.clear()
    this.providerConfigs.clear()
    this.globalConfig = this.getDefaultGlobalConfig()
    this.initializeDefaultAgentConfigs()
    this.saveToStorage()
  }
}

// Singleton instance
export const aiConfigManager = new AIConfigManager()
