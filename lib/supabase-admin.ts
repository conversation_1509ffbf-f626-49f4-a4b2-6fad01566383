/**
 * Server-side Supabase Admin Client
 * ONLY for use in API routes and server-side code
 * Uses SERVICE_ROLE_KEY - NEVER import this in frontend components!
 */
import { createClient } from '@supabase/supabase-js'

// Server-side environment variables (not prefixed with NEXT_PUBLIC_)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error(
    '🚨 Missing Supabase server credentials!\n' +
    'Required: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY\n' +
    'Check your .env.local file'
  )
}

// Validate that we have the service role key (not anon key)
if (supabaseServiceKey.includes('anon') || supabaseServiceKey === process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  throw new Error(
    '🚨 SECURITY ERROR: SERVICE_ROLE_KEY appears to be the ANON key!\n' +
    'Make sure you are using the correct service_role key from Supabase dashboard'
  )
}

// Create admin client with service role key
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Type definitions for database tables
export interface Profile {
  id: string
  email: string
  name?: string
  role: 'USER' | 'ADMIN' | 'PRO'
  settings: any
  created_at: string
  updated_at: string
}

export interface TradingSession {
  id: string
  user_id: string
  session_name: string
  exchange: string
  status: 'ACTIVE' | 'PAUSED' | 'STOPPED' | 'COMPLETED'
  config: any
  created_at: string
  updated_at: string
}

export interface Trade {
  id: string
  user_id: string
  session_id?: string
  exchange: string
  symbol: string
  side: 'buy' | 'sell'
  amount: number
  price: number
  fee: number
  status: string
  order_id?: string
  created_at: string
}

export interface Portfolio {
  id: string
  user_id: string
  name: string
  exchange: string
  total_value: number
  holdings: any
  created_at: string
  updated_at: string
}

export interface Alert {
  id: string
  user_id: string
  type: string
  symbol?: string
  condition_type: string
  condition_value?: number
  message: string
  is_active: boolean
  created_at: string
}

export interface AIAnalysis {
  id: string
  user_id: string
  symbol: string
  analysis_type: string
  agent_name: string
  confidence: number
  recommendation?: string
  reasoning?: string
  data: any
  created_at: string
}

export interface MarketData {
  id: string
  symbol: string
  exchange: string
  price: number
  volume?: number
  change_24h?: number
  data: any
  created_at: string
}

// Admin helper functions (server-side only)
export async function createUserProfile(user: any): Promise<Profile | null> {
  const { data, error } = await supabaseAdmin
    .from('profiles')
    .insert({
      id: user.id,
      email: user.email,
      name: user.user_metadata?.name || user.email
    })
    .select()
    .single()
  
  if (error) {
    console.error('Error creating user profile:', error)
    return null
  }
  
  return data
}

export async function getAllUsers(): Promise<Profile[]> {
  const { data, error } = await supabaseAdmin
    .from('profiles')
    .select('*')
    .order('created_at', { ascending: false })
  
  if (error) {
    console.error('Error fetching all users:', error)
    return []
  }
  
  return data || []
}

export async function updateMarketData(marketData: Omit<MarketData, 'id' | 'created_at'>): Promise<boolean> {
  const { error } = await supabaseAdmin
    .from('market_data')
    .upsert(marketData, { 
      onConflict: 'symbol,exchange',
      ignoreDuplicates: false 
    })
  
  if (error) {
    console.error('Error updating market data:', error)
    return false
  }
  
  return true
}

export async function saveAIAnalysis(analysis: Omit<AIAnalysis, 'id' | 'created_at'>): Promise<AIAnalysis | null> {
  const { data, error } = await supabaseAdmin
    .from('ai_analysis')
    .insert(analysis)
    .select()
    .single()
  
  if (error) {
    console.error('Error saving AI analysis:', error)
    return null
  }
  
  return data
}

export async function getTableStatus(): Promise<Record<string, boolean>> {
  const tables = ['profiles', 'trading_sessions', 'trades', 'portfolios', 'alerts']
  const tableStatus: Record<string, boolean> = {}

  for (const table of tables) {
    try {
      const { error } = await supabaseAdmin
        .from(table)
        .select('count')
        .limit(0)

      tableStatus[table] = !error || error.code !== 'PGRST116' // PGRST116 = table doesn't exist
    } catch (error) {
      tableStatus[table] = false
    }
  }

  return tableStatus
}

// Validation function to ensure this is only used server-side
export function validateServerSideUsage() {
  if (typeof window !== 'undefined') {
    throw new Error(
      '🚨 SECURITY VIOLATION: supabase-admin.ts imported in browser code!\n' +
      'This file contains SERVICE_ROLE_KEY and must ONLY be used in API routes.\n' +
      'Use lib/supabase.ts for frontend components instead.'
    )
  }
}

// Auto-validate on import
validateServerSideUsage()

console.log('✅ Supabase admin client initialized (server-side only)')

export default supabaseAdmin
