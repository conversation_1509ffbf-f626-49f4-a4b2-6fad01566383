# LM Studio Setup voor CryptoAgent Pro

## 🎯 Overzicht

Deze applicatie gebruikt LM Studio modellen voor AI-powered trading en development. Je hebt momenteel 3 modellen geladen:

1. **deepseek-coder-33b-instruct** (19.94 GB) - Voor complexe coding en architectuur
2. **qwen/qwen2.5-coder-14b** (8.33 GB) - Voor API development en backend
3. **codellama-7b-kstack** (4.08 GB) - Voor snelle fixes en frontend

## 🔧 Configuratie

### LM Studio Instellingen
- **URL**: `http://************:1235/v1`
- **Status**: ✅ Running
- **Modellen**: 3 geladen en READY

### Cursor Configuratie
De Cursor settings zijn aangepast om je LM Studio modellen te gebruiken:

```json
{
  "lm_studio": {
    "base_url": "http://************:1235/v1",
    "default_model": "deepseek-coder-33b-instruct",
    "timeout": 30000,
    "retry_attempts": 3
  }
}
```

## 🧪 Testen

### 1. Test de verbinding
```bash
cd cryptoagent-pro
node scripts/test-lm-studio.js
```

### 2. Test via de applicatie
1. Ga naar `/ai-config` in de applicatie
2. Selecteer "LM Studio" provider
3. Klik op "Test Connection"

## 📋 Model Specialisaties

### deepseek-coder-33b-instruct
- **Gebruik**: Complexe coding, architectuur, system design
- **Context**: 16,384 tokens
- **Temperatuur**: 0.1 (precies)
- **Best voor**: Full-stack development, debugging, architectuur

### qwen/qwen2.5-coder-14b
- **Gebruik**: API development, backend, database
- **Context**: 16,384 tokens  
- **Temperatuur**: 0.2 (gebalanceerd)
- **Best voor**: Backend development, API's, database queries

### codellama-7b-kstack
- **Gebruik**: Snelle fixes, frontend, React
- **Context**: 8,192 tokens
- **Temperatuur**: 0.3 (creatief)
- **Best voor**: Frontend, React components, TypeScript

## 🚀 Gebruik in Cursor

### Automatische Model Selectie
Cursor zal automatisch het beste model kiezen op basis van de taak:

- **Complexe architectuur** → deepseek-coder-33b-instruct
- **Backend/API werk** → qwen/qwen2.5-coder-14b  
- **Frontend/React** → codellama-7b-kstack

### Handmatige Model Selectie
Je kunt ook handmatig een model kiezen in Cursor:

1. Open Cursor settings
2. Ga naar AI configuratie
3. Selecteer het gewenste model

## 🔍 Troubleshooting

### Probleem: Verbinding faalt
```bash
# Check of LM Studio draait
curl http://************:1235/v1/models

# Check firewall
sudo ufw status
```

### Probleem: Model niet beschikbaar
1. Controleer of het model geladen is in LM Studio
2. Herstart LM Studio indien nodig
3. Check de model naam spelling

### Probleem: Langzame responses
1. Verlaag de context length
2. Gebruik een kleiner model voor eenvoudige taken
3. Check GPU memory usage

## 📊 Performance Monitoring

### GPU Usage
- **deepseek-coder-33b-instruct**: ~56/62 GPU layers
- **qwen/qwen2.5-coder-14b**: ~40/62 GPU layers  
- **codellama-7b-kstack**: ~20/62 GPU layers

### Memory Usage
- **Total RAM**: 31.90 GB beschikbaar
- **Model Memory**: ~32 GB gebruikt
- **Swap**: Niet aanbevolen voor AI modellen

## 🎯 Best Practices

1. **Gebruik het juiste model voor de taak**
   - Complex werk → deepseek-coder-33b-instruct
   - Backend werk → qwen/qwen2.5-coder-14b
   - Frontend werk → codellama-7b-kstack

2. **Optimaliseer prompts**
   - Wees specifiek over wat je wilt
   - Gebruik duidelijke instructies
   - Test kleine stukken eerst

3. **Monitor performance**
   - Check GPU usage in LM Studio
   - Monitor response times
   - Herstart indien nodig

## 🔄 Updates

### Model Updates
- Controleer regelmatig voor nieuwe model versies
- Backup je huidige configuratie
- Test nieuwe modellen in een aparte omgeving

### Configuratie Updates
- De configuratie wordt automatisch gesynchroniseerd
- Check `/ai-config` voor de laatste status
- Test na elke configuratie wijziging

## 📞 Support

Voor problemen met LM Studio:
1. Check de LM Studio logs
2. Test de verbinding met het test script
3. Controleer de model status in LM Studio
4. Herstart LM Studio indien nodig

Voor problemen met de applicatie:
1. Check de browser console
2. Test de API endpoints
3. Controleer de network tab
4. Herstart de development server 