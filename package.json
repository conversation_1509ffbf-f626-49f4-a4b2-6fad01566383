{"name": "cryptoagent-pro", "version": "0.1.0", "description": "AI-powered crypto trading platform with LM Studio integration", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup": "node scripts/complete-setup.js", "setup:db": "echo 'Run the SQL script in Supabase SQL Editor: database-schema.sql'", "quick-start": "node scripts/quick-start.js", "start:trading": "node scripts/start-trading-engine.js", "start:full": "node scripts/start.js", "test": "echo 'No tests specified' && exit 0", "test:lm-studio": "node scripts/test-lm-studio.js", "test:ai": "curl -X POST http://localhost:3000/api/ai/test-provider -H 'Content-Type: application/json' -d '{\"provider\": \"lm-studio\", \"baseUrl\": \"http://************:1235/v1\"}'", "test:connections": "node scripts/test-connections.js", "prepare": "husky"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.53.0", "@tanstack/react-query": "^5.84.1", "@types/node-cron": "^3.0.11", "@types/ws": "^8.18.1", "ccxt": "^4.4.98", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.536.0", "next": "15.4.5", "node-cron": "^4.2.1", "node-fetch": "^3.3.2", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.7"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "husky": "^9.1.7", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5.9.2", "vitest": "^3.2.4"}, "keywords": ["crypto", "trading", "ai", "lm-studio", "nextjs", "supabase", "typescript"], "author": "CryptoAgent Pro Team", "license": "MIT"}