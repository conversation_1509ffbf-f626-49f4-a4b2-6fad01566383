# ========================================
# CRYPTOAGENT PRO - ENVIRONMENT VARIABLES
# ========================================

# ========================================
# EXCHANGE API KEYS
# ========================================

# Binance API Keys
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
BINANCE_TESTNET=false

# MEXC API Keys
MEXC_API_KEY=your_mexc_api_key_here
MEXC_SECRET_KEY=your_mexc_secret_key_here
MEXC_TESTNET=false

# KuCoin API Keys
KUCOIN_API_KEY=your_kucoin_api_key_here
KUCOIN_SECRET_KEY=your_kucoin_secret_key_here
KUCOIN_PASSPHRASE=your_kucoin_passphrase_here
KUCOIN_TESTNET=false

# Bybit API Keys
BYBIT_API_KEY=your_bybit_api_key_here
BYBIT_SECRET_KEY=your_bybit_secret_key_here
BYBIT_TESTNET=false

# ========================================
# AI CONFIGURATION
# ========================================

# LM Studio Configuration
LM_STUDIO_BASE_URL=http://192.168.0.33:1235/v1
LM_STUDIO_MODEL=deepseek-coder-33b-instruct
LM_STUDIO_TIMEOUT=60000

# OpenAI Configuration (Backup)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Anthropic Configuration (Backup)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# ========================================
# SENTIMENT ANALYSIS CONFIGURATION
# ========================================

# Sentiment Analysis Settings
SENTIMENT_ANALYSIS_ENABLED=true
SENTIMENT_WEIGHT=0.3  # Weight of sentiment in trading decisions (0-1)

# Twitter/X API for sentiment analysis
TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here

# Reddit API for sentiment analysis
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here
REDDIT_USERNAME=your_reddit_username_here
REDDIT_PASSWORD=your_reddit_password_here

# News API for sentiment analysis
NEWS_API_KEY=your_news_api_key_here
NEWS_MAX_LENGTH=150

# ========================================
# BLOCKCHAIN ANALYSIS CONFIGURATION
# ========================================

# Blockchain APIs
ETHERSCAN_API_KEY=your_etherscan_api_key_here

# On-chain analysis settings
ONCHAIN_ANALYSIS_ENABLED=true
WHALE_ALERT_THRESHOLD=1000000  # USD value for whale alerts

# ========================================
# TECHNICAL ANALYSIS CONFIGURATION
# ========================================

# Advanced technical analysis settings
ADVANCED_INDICATORS_ENABLED=true
CUSTOM_INDICATORS=RSI,MACD,Bollinger,Ichimoku,Elliott
INDICATOR_TIMEFRAMES=1h,4h,1d  # Multiple timeframes to analyze

# ========================================
# MACHINE LEARNING CONFIGURATION
# ========================================

# Machine learning model settings
ML_MODELS_ENABLED=true
MODEL_TYPE=LSTM  # Long Short-Term Memory neural network
PREDICTION_HORIZON=24  # Prediction in hours
RETRAINING_INTERVAL=168  # Retraining interval in hours (1 week)

# Multi-model settings
MULTI_MODEL_ENABLED=true
MODELS=GPT4,CLAUDE,GEMINI,LLAMA
CONSENSUS_THRESHOLD=0.75  # Percentage of models that must agree

# ========================================
# CORRELATION ANALYSIS CONFIGURATION
# ========================================

# Correlation analysis settings
CORRELATION_ANALYSIS_ENABLED=true
CORRELATED_ASSETS=BTC,ETH,TOTAL_MARKET_CAP,DXY,SPX
CORRELATION_THRESHOLD=0.7  # Minimum correlation to include in decisions

# ========================================
# ECONOMIC DATA CONFIGURATION
# ========================================

# Economic data APIs
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here
FRED_API_KEY=your_fred_api_key_here  # Federal Reserve Economic Data

# Economic indicator settings
ECONOMIC_INDICATORS_ENABLED=true
MONITORED_INDICATORS=CPI,UNEMPLOYMENT,FED_RATE,GDP

# ========================================
# ANOMALY DETECTION CONFIGURATION
# ========================================

# Anomaly detection settings
ANOMALY_DETECTION_ENABLED=true
ANOMALY_SENSITIVITY=0.8  # Sensitivity (0-1)
ANOMALY_RESPONSE=alert  # 'alert', 'pause', or 'hedge'

# ========================================
# DATABASE CONFIGURATION
# ========================================

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# ========================================
# TRADING CONFIGURATION
# ========================================

# Trading Mode
TRADING_MODE=paper  # paper, live, demo
SANDBOX_MODE=true   # true for testing, false for live trading

# Risk Management
MAX_PORTFOLIO_RISK=20        # Maximum portfolio risk percentage
MAX_POSITION_SIZE=5          # Maximum position size percentage
MAX_DAILY_LOSS=1000         # Maximum daily loss in USD
STOP_LOSS_PERCENT=2         # Default stop loss percentage
TAKE_PROFIT_PERCENT=4       # Default take profit percentage
MAX_OPEN_POSITIONS=10       # Maximum number of open positions

# Portfolio Configuration
TARGET_RETURN=0.15          # Target annual return (15%)
MAX_RISK=0.25               # Maximum portfolio risk (25%)
REBALANCE_THRESHOLD=0.05    # Rebalancing threshold (5%)
MAX_ALLOCATION=0.20         # Maximum allocation per asset (20%)
MIN_ALLOCATION=0.02         # Minimum allocation per asset (2%)

# ========================================
# ALERT CONFIGURATION
# ========================================

# Alert Settings
PRICE_ALERTS_ENABLED=true
RISK_ALERTS_ENABLED=true
PERFORMANCE_ALERTS_ENABLED=true

# Notification Settings
EMAIL_NOTIFICATIONS=true
PUSH_NOTIFICATIONS=true
WEBHOOK_URL=your_webhook_url_here

# Email Configuration (for alerts)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_app_password

# ========================================
# MONITORING CONFIGURATION
# ========================================

# Update Intervals
UPDATE_INTERVAL=30000        # 30 seconds
BALANCE_UPDATE_INTERVAL=60000 # 1 minute
POSITION_UPDATE_INTERVAL=30000 # 30 seconds

# Performance Tracking
PERFORMANCE_TRACKING_ENABLED=true
BACKTEST_ENABLED=true
PORTFOLIO_OPTIMIZATION_ENABLED=true

# Logging Configuration
LOGGING_LEVEL=INFO
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs/trading.log

# ========================================
# SECURITY CONFIGURATION
# ========================================

# Encryption
ENCRYPTION_KEY=your_32_character_encryption_key_here
API_KEY_ENCRYPTION=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
MAX_REQUESTS_PER_MINUTE=60

# IP Whitelist (optional)
IP_WHITELIST_ENABLED=false
ALLOWED_IPS=***********,***********

# ========================================
# DEVELOPMENT CONFIGURATION
# ========================================

# Environment
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Debug Mode
DEBUG_MODE=false
VERBOSE_LOGGING=false

# ========================================
# EXTERNAL SERVICES
# ========================================

# Market Data APIs
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
YAHOO_FINANCE_ENABLED=true
COINGECKO_API_KEY=your_coingecko_key_here

# News APIs
NEWS_API_KEY=your_news_api_key_here
CRYPTOPANIC_API_KEY=your_cryptopanic_key_here

# Social Media APIs
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here

# ========================================
# DEPLOYMENT CONFIGURATION
# ========================================

# Vercel Configuration
VERCEL_PROJECT_ID=your_vercel_project_id
VERCEL_TOKEN=your_vercel_token

# Domain Configuration
CUSTOM_DOMAIN=your-domain.com
SSL_ENABLED=true

# ========================================
# BACKUP CONFIGURATION
# ========================================

# Database Backup
BACKUP_ENABLED=true
BACKUP_INTERVAL=86400  # 24 hours
BACKUP_RETENTION_DAYS=30

# Data Export
EXPORT_ENABLED=true
EXPORT_FORMAT=json
EXPORT_INTERVAL=604800  # 7 days

# ========================================
# COMPLIANCE & REPORTING
# ========================================

# Tax Reporting
TAX_REPORTING_ENABLED=true
TAX_YEAR=2024
TAX_JURISDICTION=US

# Audit Trail
AUDIT_TRAIL_ENABLED=true
AUDIT_LOG_RETENTION_DAYS=365

# ========================================
# MOBILE APP CONFIGURATION
# ========================================

# Push Notifications
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email

# Mobile App Settings
MOBILE_APP_ENABLED=true
PUSH_NOTIFICATIONS_ENABLED=true
BIOMETRIC_AUTH_ENABLED=true

# ========================================
# INTEGRATION CONFIGURATION
# ========================================

# Telegram Bot (for alerts)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Discord Webhook (for alerts)
DISCORD_WEBHOOK_URL=your_discord_webhook_url

# Slack Webhook (for alerts)
SLACK_WEBHOOK_URL=your_slack_webhook_url

# ========================================
# ADVANCED TRADING FEATURES
# ========================================

# Grid Trading
GRID_TRADING_ENABLED=false
GRID_LEVELS=10
GRID_SPACING=0.02

# DCA (Dollar Cost Averaging)
DCA_ENABLED=false
DCA_INTERVAL=86400  # 24 hours
DCA_AMOUNT=100      # USD

# Copy Trading
COPY_TRADING_ENABLED=false
COPY_TRADING_LEADER=leader_id_here
COPY_TRADING_PERCENTAGE=10

# ========================================
# TESTING CONFIGURATION
# ========================================

# Test Mode
TEST_MODE=true
MOCK_TRADING_ENABLED=true
SIMULATED_BALANCE=10000

# Test API Keys
TEST_BINANCE_API_KEY=your_test_binance_api_key
TEST_BINANCE_SECRET_KEY=your_test_binance_secret_key

# ========================================
# MONITORING & ANALYTICS
# ========================================

# Google Analytics
GOOGLE_ANALYTICS_ID=your_ga_id_here

# Sentry (Error Tracking)
SENTRY_DSN=your_sentry_dsn_here

# DataDog (Monitoring)
DATADOG_API_KEY=your_datadog_api_key
DATADOG_APP_KEY=your_datadog_app_key

# ========================================
# LEGAL & COMPLIANCE
# ========================================

# Terms of Service
TOS_VERSION=1.0
TOS_ACCEPTED_DATE=2024-01-01

# Privacy Policy
PRIVACY_POLICY_VERSION=1.0
PRIVACY_POLICY_ACCEPTED_DATE=2024-01-01

# GDPR Compliance
GDPR_COMPLIANT=true
DATA_RETENTION_DAYS=730  # 2 years

# ========================================
# CUSTOM CONFIGURATION
# ========================================

# Custom Trading Pairs
CUSTOM_TRADING_PAIRS=BTC/USDT,ETH/USDT,ADA/USDT

# Custom Indicators
CUSTOM_INDICATORS_ENABLED=true
CUSTOM_RSI_PERIOD=14
CUSTOM_MACD_FAST=12
CUSTOM_MACD_SLOW=26
CUSTOM_MACD_SIGNAL=9

# Custom Risk Rules
CUSTOM_RISK_RULES_ENABLED=true
MAX_SINGLE_TRADE_SIZE=1000
MIN_TRADE_SIZE=10
MAX_DAILY_TRADES=50

# ========================================
# BACKUP & RECOVERY
# ========================================

# Auto Backup
AUTO_BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_LOCATION=./backups

# Disaster Recovery
DISASTER_RECOVERY_ENABLED=true
RECOVERY_POINT_OBJECTIVE=3600  # 1 hour
RECOVERY_TIME_OBJECTIVE=7200   # 2 hours

# ========================================
# END OF CONFIGURATION
# ========================================

# IMPORTANT: Never commit this file with real API keys!
# Copy this file to .env and fill in your actual values
# Keep your .env file secure and never share it 