# Advanced Multi-Model Sentiment Analysis Configuration
# Copy this to .env.local and fill in your API keys

# =================================
# AI PROVIDERS FOR SENTIMENT ANALYSIS
# =================================

# OpenRouter API Keys (for DeepSeek, Claude, Llama)
OPENROUTER_API_KEY_PRIMARY=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
OPENROUTER_API_KEY_SECONDARY=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

# Google Gemini API Key
GOOGLE_GEMINI_API_KEY=AIzaSyCTHMGBEqqgHxJWxZRBgBFs-8Z2YVcsBxY

# GLM-4.5 API Key (Chinese markets specialization)
GLM_API_KEY=108102c71ab14c3aa202f7810429c998.ZxS1JyXLXRB0ytgO

# =================================
# SOCIAL MEDIA DATA SOURCES
# =================================

# Twitter API v2 (for sentiment data)
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here

# Reddit API (for reddit sentiment)
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here

# News API (for news sentiment)
NEWS_API_KEY=your_news_api_key_here

# =================================
# SENTIMENT ANALYSIS SETTINGS
# =================================

# Sentiment analysis preferences
ENABLE_MULTILINGUAL_ANALYSIS=true
ENABLE_CHINESE_MARKETS=true
ENABLE_SOCIAL_MEDIA_TRENDS=true
ENABLE_INFLUENCER_TRACKING=true
ENABLE_NEWS_IMPACT_ANALYSIS=true

# Weight settings for different data sources
TWITTER_WEIGHT=0.3
REDDIT_WEIGHT=0.2
NEWS_WEIGHT=0.4
INFLUENCER_WEIGHT=0.1

# Confidence thresholds
MIN_CONFIDENCE_THRESHOLD=60
HIGH_CONFIDENCE_THRESHOLD=85

# =================================
# AI MODEL CONFIGURATION
# =================================

# Model selection for different tasks
PRIMARY_SENTIMENT_MODEL=deepseek/deepseek-chat
SECONDARY_SENTIMENT_MODEL=anthropic/claude-3.5-sonnet
BACKUP_SENTIMENT_MODEL=meta-llama/llama-3.1-405b-instruct

# Temperature settings for different models
DEEPSEEK_TEMPERATURE=0.3
CLAUDE_TEMPERATURE=0.4
LLAMA_TEMPERATURE=0.5
GEMINI_TEMPERATURE=0.4
GLM_TEMPERATURE=0.6

# Token limits
MAX_TOKENS_SENTIMENT=4096
MAX_TOKENS_GEMINI=2048