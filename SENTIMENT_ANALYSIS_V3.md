# 🧠 Advanced Multi-Model Sentiment Analysis V3

## 🚀 Overview

The CryptoAgent Pro Sentiment Analysis V3 system is a state-of-the-art sentiment analysis platform that leverages multiple AI models to provide comprehensive cryptocurrency sentiment analysis. It integrates with your existing AI providers (OpenRouter, Gemini, GLM-4.5) to deliver accurate, real-time sentiment insights.

## ✨ Key Features

### 🤖 Multi-Model AI Integration
- **OpenRouter DeepSeek**: Fast code analysis and quick sentiment assessment
- **OpenRouter Claude 3.5 Sonnet**: Deep analysis and risk assessment
- **OpenRouter Llama 3.1**: Market sentiment and social analysis
- **Google Gemini Pro**: Data analysis and prediction capabilities
- **GLM-4.5**: Chinese markets and multilingual sentiment analysis

### 📊 Comprehensive Data Sources
- **Twitter**: Real-time mentions, influencer sentiment, trending hashtags
- **Reddit**: Community discussions, upvote ratios, subreddit activity
- **News**: Article sentiment, impact analysis, source credibility
- **Social Media**: Telegram, Discord, YouTube sentiment tracking

### 🎯 Advanced Analytics
- **Sentiment Score**: -100 to +100 range with confidence levels
- **AI Consensus**: Weighted analysis from multiple models
- **Market Mood**: Fear, Greed, Neutral, Uncertainty indicators
- **Risk Assessment**: Low, Medium, High risk levels
- **Technical Alignment**: Correlation with price and volume
- **Emerging Narratives**: Real-time trend detection

## 🛠️ Installation & Setup

### 1. API Key Configuration

Copy the configuration from `sentiment-config.example` to your `.env.local`:

```bash
# Your API keys are already configured:
OPENROUTER_API_KEY_PRIMARY=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
OPENROUTER_API_KEY_SECONDARY=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
GOOGLE_GEMINI_API_KEY=AIzaSyCTHMGBEqqgHxJWxZRBgBFs-8Z2YVcsBxY
GLM_API_KEY=108102c71ab14c3aa202f7810429c998.ZxS1JyXLXRB0ytgO
```

### 2. Install Dependencies

```bash
npm install
# All required dependencies are already in package.json
```

### 3. Start the Development Server

```bash
npm run dev
```

## 🎮 Usage

### 1. Web Dashboard
Navigate to `/sentiment` to access the comprehensive sentiment dashboard:

```typescript
// Access the sentiment dashboard
http://localhost:3000/sentiment
```

### 2. Mobile Control Integration
The mobile control page now includes an "AI Sentiment" button that opens the sentiment dashboard.

### 3. API Endpoints

#### Single Symbol Analysis
```bash
# GET request
curl "http://localhost:3000/api/sentiment/analyze?symbol=BTC"

# POST request
curl -X POST "http://localhost:3000/api/sentiment/analyze" \
  -H "Content-Type: application/json" \
  -d '{"symbol": "BTC"}'
```

#### Bulk Analysis
```bash
curl -X POST "http://localhost:3000/api/sentiment/bulk" \
  -H "Content-Type: application/json" \
  -d '{"symbols": ["BTC", "ETH", "ADA"], "maxConcurrency": 3}'
```

### 4. Programmatic Usage

```typescript
import { SentimentAnalysisAgentV3 } from './lib/agents/SentimentAnalysisAgentV3'

const agent = new SentimentAnalysisAgentV3()
const analysis = await agent.analyze('BTC')

console.log(`Sentiment Score: ${analysis.data.sentimentScore}`)
console.log(`Recommendation: ${analysis.data.recommendation}`)
console.log(`Confidence: ${analysis.data.confidence}%`)
```

## 📡 Real-Time Updates

### WebSocket Integration

```typescript
import { sentimentWebSocket } from './lib/websocket/SentimentWebSocket'

// Subscribe to sentiment updates
sentimentWebSocket.subscribe('BTC', (update) => {
  console.log('New sentiment data:', update)
})

// Connect to WebSocket or start periodic updates
sentimentWebSocket.connect()
```

## 🧪 Testing

### Run Comprehensive Tests

```bash
# Test the sentiment analysis system
npx ts-node scripts/test-sentiment-v3.ts
```

This will test:
- ✅ Single symbol analysis with all AI models
- ✅ Bulk analysis capabilities
- ✅ WebSocket simulation
- ✅ Error handling and fallbacks

## 📊 Response Format

### Sentiment Analysis Response

```json
{
  "success": true,
  "data": {
    "type": "sentiment_analysis_v3",
    "symbol": "BTC",
    "recommendation": "BUY",
    "confidence": 78,
    "data": {
      "sentimentScore": 65,
      "socialVolume": "HIGH",
      "influencerSentiment": "BULLISH",
      "newsImpact": "POSITIVE",
      "marketMood": "GREED",
      "riskLevel": "MEDIUM",
      "reasoning": "Multi-model consensus shows bullish sentiment...",
      "keyFactors": [
        "Strong social media momentum",
        "Positive news coverage",
        "Influencer endorsements"
      ],
      "aiConsensus": {
        "OpenRouter-DeepSeek": {
          "score": 70,
          "confidence": 85,
          "reasoning": "Technical indicators show strength"
        },
        "OpenRouter-Claude": {
          "score": 68,
          "confidence": 82,
          "reasoning": "Fundamental analysis supports bullish outlook"
        },
        "GLM-4.5": {
          "score": 72,
          "confidence": 78,
          "reasoning": "Chinese market sentiment very positive"
        }
      },
      "correlationWithPrice": 0.75,
      "volumeCorrelation": 0.68,
      "technicalAlignment": "BULLISH",
      "emergingNarratives": [
        "Institutional adoption increasing",
        "ETF approval momentum",
        "DeFi integration expanding"
      ],
      "socialMediaTrends": [
        "#Bitcoin trending upward",
        "Positive influencer engagement",
        "Community confidence high"
      ]
    }
  }
}
```

## 🎯 Features by AI Model

### OpenRouter DeepSeek
- **Specialization**: Quick analysis, code review
- **Strength**: Fast processing, technical accuracy
- **Use Case**: Real-time sentiment updates

### OpenRouter Claude 3.5 Sonnet
- **Specialization**: Deep analysis, risk assessment
- **Strength**: Comprehensive reasoning, nuanced analysis
- **Use Case**: Detailed sentiment reports

### OpenRouter Llama 3.1 405B
- **Specialization**: Market sentiment, social analysis
- **Strength**: Large context, social media understanding
- **Use Case**: Social sentiment aggregation

### Google Gemini Pro
- **Specialization**: Data analysis, prediction
- **Strength**: Pattern recognition, trend analysis
- **Use Case**: Predictive sentiment modeling

### GLM-4.5
- **Specialization**: Chinese markets, multilingual
- **Strength**: Global market understanding, cultural context
- **Use Case**: International sentiment analysis

## 🔧 Advanced Configuration

### Model Weights
Adjust model influence in the analysis:

```typescript
const weights = {
  'sentiment': 1.5,           // GLM-4.5
  'market_sentiment': 1.4,    // Llama
  'social_analysis': 1.3,     // Llama
  'deep_analysis': 1.2,       // Claude
  'data_analysis': 1.1,       // Gemini
  'quick_analysis': 1.0       // DeepSeek
}
```

### Rate Limiting
The system includes built-in rate limiting to respect API quotas:

- **OpenRouter**: 100 requests/minute
- **Gemini**: 60 requests/minute  
- **GLM-4.5**: 50 requests/minute

### Caching
Sentiment data is cached for 5 minutes to improve performance and reduce API costs.

## 🚨 Error Handling

The system includes comprehensive error handling:

- **API Failures**: Automatic fallback to available models
- **Rate Limiting**: Intelligent backoff and retry
- **Invalid Responses**: JSON parsing with fallbacks
- **Network Issues**: Graceful degradation

## 📈 Performance Optimization

### Recommendations for Production

1. **API Key Rotation**: Use multiple API keys for higher rate limits
2. **Caching Strategy**: Implement Redis for distributed caching
3. **Database Storage**: Store historical sentiment data
4. **CDN Integration**: Cache static sentiment reports
5. **Rate Limiting**: Implement user-based rate limiting

## 🔐 Security Considerations

- **API Key Encryption**: Keys are masked in logs
- **Rate Limiting**: Prevents API abuse
- **Input Validation**: Symbol format validation
- **Error Sanitization**: No sensitive data in error messages

## 🎉 Integration Examples

### Voice Commands
The mobile control system now supports sentiment analysis voice commands:

- "Analyze Bitcoin sentiment"
- "Check market mood"
- "What's the social volume for Ethereum?"

### Dashboard Widgets
Add sentiment widgets to your dashboard:

```typescript
import SentimentDashboard from './components/sentiment/SentimentDashboard'

<SentimentDashboard />
```

## 🔮 Roadmap

### Upcoming Features

1. **Historical Analysis**: Trend analysis over time
2. **Alert System**: Custom sentiment alerts
3. **Portfolio Integration**: Sentiment-based position sizing
4. **Backtesting**: Historical sentiment performance
5. **Mobile App**: Native mobile sentiment tracking
6. **API Webhooks**: Real-time sentiment notifications

## 🆘 Support

### Common Issues

**Q: Analysis is slow**
A: Enable caching and check your internet connection. Consider using fewer models for faster results.

**Q: API errors**
A: Verify your API keys are correct and check rate limits.

**Q: Inconsistent results** 
A: This is normal - different AI models may have different opinions. The consensus provides the best overall view.

### Debug Mode
Enable debug logging:

```bash
DEBUG_SENTIMENT_ANALYSIS=true npm run dev
```

---

## 🚀 Start Analyzing!

Your advanced multi-model sentiment analysis system is ready! 

1. Visit `/sentiment` to start analyzing
2. Check the mobile control for quick access
3. Use the API for programmatic access
4. Monitor real-time updates via WebSocket

**Happy Trading with AI-Powered Insights!** 🎯📈🤖