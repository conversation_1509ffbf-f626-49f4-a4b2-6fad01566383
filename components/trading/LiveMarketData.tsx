'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { RefreshCw, TrendingUp, TrendingDown, Activity } from 'lucide-react'

interface TickerData {
  symbol: string
  exchange: string
  price: number
  bid: number
  ask: number
  volume: number
  change24h: number
  percentage24h: number
  high24h: number
  low24h: number
  timestamp: number
  datetime: string
}

interface OrderbookData {
  symbol: string
  exchange: string
  bids: [number, number][]
  asks: [number, number][]
  timestamp: number
}

interface MarketDataProps {
  exchange?: string
  symbols?: string[]
  autoRefresh?: boolean
  refreshInterval?: number
}

export default function LiveMarketData({ 
  exchange = 'mexc', 
  symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT'],
  autoRefresh = true,
  refreshInterval = 5000 
}: MarketDataProps) {
  const [tickers, setTickers] = useState<Record<string, TickerData>>({})
  const [orderbooks, setOrderbooks] = useState<Record<string, OrderbookData>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  const fetchTickerData = useCallback(async (symbol: string) => {
    try {
      const response = await fetch(`/api/market-data/live?exchange=${exchange}&symbol=${symbol}&type=ticker`)
      const result = await response.json()
      
      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || 'Failed to fetch ticker')
      }
    } catch (error) {
      console.error(`Failed to fetch ticker for ${symbol}:`, error)
      return null
    }
  }, [exchange])

  const fetchOrderbookData = useCallback(async (symbol: string) => {
    try {
      const response = await fetch(`/api/market-data/live?exchange=${exchange}&symbol=${symbol}&type=orderbook&limit=5`)
      const result = await response.json()
      
      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || 'Failed to fetch orderbook')
      }
    } catch (error) {
      console.error(`Failed to fetch orderbook for ${symbol}:`, error)
      return null
    }
  }, [exchange])

  const fetchAllData = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      // Fetch tickers for all symbols
      const tickerPromises = symbols.map(symbol => fetchTickerData(symbol))
      const tickerResults = await Promise.all(tickerPromises)
      
      const newTickers: Record<string, TickerData> = {}
      tickerResults.forEach((ticker, index) => {
        if (ticker) {
          newTickers[symbols[index]] = ticker
        }
      })
      
      setTickers(newTickers)

      // Fetch orderbook for first symbol only (to avoid rate limits)
      if (symbols.length > 0) {
        const orderbookData = await fetchOrderbookData(symbols[0])
        if (orderbookData) {
          setOrderbooks({ [symbols[0]]: orderbookData })
        }
      }

      setLastUpdate(new Date())
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch market data')
    } finally {
      setLoading(false)
    }
  }, [symbols, fetchTickerData, fetchOrderbookData])

  // Initial load
  useEffect(() => {
    fetchAllData()
  }, [fetchAllData])

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(fetchAllData, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, fetchAllData])

  const formatPrice = (price: number) => {
    if (price >= 1000) {
      return price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
    } else if (price >= 1) {
      return price.toFixed(4)
    } else {
      return price.toFixed(8)
    }
  }

  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(1)}M`
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`
    } else {
      return volume.toFixed(2)
    }
  }

  if (loading && Object.keys(tickers).length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <RefreshCw className="w-6 h-6 animate-spin" />
            <span>Loading market data...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Live Market Data</h2>
          <p className="text-gray-400">
            {exchange.toUpperCase()} • {symbols.length} symbols
            {lastUpdate && (
              <span className="ml-2">
                • Last update: {lastUpdate.toLocaleTimeString()}
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={loading ? "secondary" : "default"}>
            <Activity className="w-3 h-3 mr-1" />
            {loading ? 'Updating...' : 'Live'}
          </Badge>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={fetchAllData}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {error && (
        <Card className="border-red-500">
          <CardContent className="p-4">
            <p className="text-red-400">Error: {error}</p>
          </CardContent>
        </Card>
      )}

      {/* Ticker Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {symbols.map(symbol => {
          const ticker = tickers[symbol]
          if (!ticker) return null

          const isPositive = ticker.change24h >= 0
          
          return (
            <Card key={symbol} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between">
                  <span className="text-lg">{symbol}</span>
                  <Badge variant={isPositive ? "default" : "destructive"}>
                    {isPositive ? <TrendingUp className="w-3 h-3 mr-1" /> : <TrendingDown className="w-3 h-3 mr-1" />}
                    {ticker.percentage24h?.toFixed(2)}%
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <div className="text-2xl font-bold">
                    ${formatPrice(ticker.price)}
                  </div>
                  <div className={`text-sm ${isPositive ? 'text-green-400' : 'text-red-400'}`}>
                    {isPositive ? '+' : ''}${ticker.change24h?.toFixed(2)}
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-gray-400">Bid:</span>
                    <div className="font-mono">${formatPrice(ticker.bid)}</div>
                  </div>
                  <div>
                    <span className="text-gray-400">Ask:</span>
                    <div className="font-mono">${formatPrice(ticker.ask)}</div>
                  </div>
                  <div>
                    <span className="text-gray-400">High:</span>
                    <div className="font-mono">${formatPrice(ticker.high24h)}</div>
                  </div>
                  <div>
                    <span className="text-gray-400">Low:</span>
                    <div className="font-mono">${formatPrice(ticker.low24h)}</div>
                  </div>
                </div>
                
                <div>
                  <span className="text-gray-400 text-sm">24h Volume:</span>
                  <div className="font-mono">{formatVolume(ticker.volume)}</div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Orderbook */}
      {Object.keys(orderbooks).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Order Book - {Object.keys(orderbooks)[0]}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-green-400 mb-2">Bids</h4>
                <div className="space-y-1">
                  {orderbooks[Object.keys(orderbooks)[0]].bids.map((bid, index) => (
                    <div key={index} className="flex justify-between text-sm font-mono">
                      <span className="text-green-400">${formatPrice(bid[0])}</span>
                      <span className="text-gray-400">{bid[1].toFixed(6)}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-red-400 mb-2">Asks</h4>
                <div className="space-y-1">
                  {orderbooks[Object.keys(orderbooks)[0]].asks.map((ask, index) => (
                    <div key={index} className="flex justify-between text-sm font-mono">
                      <span className="text-red-400">${formatPrice(ask[0])}</span>
                      <span className="text-gray-400">{ask[1].toFixed(6)}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
