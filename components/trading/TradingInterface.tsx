'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '../../lib/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Badge } from '../ui/badge'
import { Alert, AlertDescription } from '../ui/alert'
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Activity, 
  Play,
  Pause,
  Square,
  Settings
} from 'lucide-react'

interface TradingSession {
  id: string
  name: string
  status: 'ACTIVE' | 'PAUSED' | 'STOPPED'
  exchange: string
  profit: number
  trades: number
}

export default function TradingInterface() {
  const { user } = useAuth()
  const [sessions, setSessions] = useState<TradingSession[]>([])
  const [loading, setLoading] = useState(false)
  const [newSessionName, setNewSessionName] = useState('')
  const [selectedExchange, setSelectedExchange] = useState('binance')

  // Mock trading sessions
  useEffect(() => {
    setSessions([
      {
        id: '1',
        name: 'BTC Scalping',
        status: 'ACTIVE',
        exchange: 'Binance',
        profit: 245.67,
        trades: 12
      },
      {
        id: '2',
        name: 'ETH DCA',
        status: 'PAUSED',
        exchange: 'Coinbase',
        profit: -23.45,
        trades: 5
      }
    ])
  }, [])

  const createSession = async () => {
    if (!newSessionName.trim()) return

    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const newSession: TradingSession = {
        id: Date.now().toString(),
        name: newSessionName,
        status: 'STOPPED',
        exchange: selectedExchange,
        profit: 0,
        trades: 0
      }

      setSessions(prev => [...prev, newSession])
      setNewSessionName('')
    } catch (error) {
      console.error('Error creating session:', error)
    } finally {
      setLoading(false)
    }
  }

  const toggleSession = (sessionId: string) => {
    setSessions(prev => prev.map(session => {
      if (session.id === sessionId) {
        const newStatus = session.status === 'ACTIVE' ? 'PAUSED' : 'ACTIVE'
        return { ...session, status: newStatus }
      }
      return session
    }))
  }

  const stopSession = (sessionId: string) => {
    setSessions(prev => prev.map(session => {
      if (session.id === sessionId) {
        return { ...session, status: 'STOPPED' }
      }
      return session
    }))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-500'
      case 'PAUSED': return 'bg-yellow-500'
      case 'STOPPED': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'default'
      case 'PAUSED': return 'secondary'
      case 'STOPPED': return 'destructive'
      default: return 'outline'
    }
  }

  return (
    <div className="space-y-6">
      {/* Create New Session */}
      <Card>
        <CardHeader>
          <CardTitle>Create New Trading Session</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <Input
              placeholder="Session name (e.g., BTC Scalping)"
              value={newSessionName}
              onChange={(e) => setNewSessionName(e.target.value)}
              className="flex-1"
            />
            <select
              value={selectedExchange}
              onChange={(e) => setSelectedExchange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="binance">Binance</option>
              <option value="coinbase">Coinbase</option>
              <option value="kraken">Kraken</option>
            </select>
            <Button 
              onClick={createSession} 
              disabled={loading || !newSessionName.trim()}
            >
              {loading ? 'Creating...' : 'Create Session'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Active Sessions */}
      <Card>
        <CardHeader>
          <CardTitle>Trading Sessions</CardTitle>
        </CardHeader>
        <CardContent>
          {sessions.length === 0 ? (
            <div className="text-center py-8">
              <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No trading sessions yet</p>
              <p className="text-sm text-gray-400">Create your first session to start trading</p>
            </div>
          ) : (
            <div className="space-y-4">
              {sessions.map((session) => (
                <div key={session.id} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(session.status)}`}></div>
                      <div>
                        <h3 className="font-medium">{session.name}</h3>
                        <p className="text-sm text-gray-500">{session.exchange}</p>
                      </div>
                    </div>
                    <Badge variant={getStatusBadge(session.status) as any}>
                      {session.status}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500">Profit/Loss</p>
                        <p className={`font-medium ${session.profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          ${session.profit.toFixed(2)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Activity className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-500">Trades</p>
                        <p className="font-medium">{session.trades}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant={session.status === 'ACTIVE' ? 'secondary' : 'default'}
                      onClick={() => toggleSession(session.id)}
                      disabled={session.status === 'STOPPED'}
                    >
                      {session.status === 'ACTIVE' ? (
                        <>
                          <Pause className="h-4 w-4 mr-1" />
                          Pause
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-1" />
                          Start
                        </>
                      )}
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => stopSession(session.id)}
                      disabled={session.status === 'STOPPED'}
                    >
                      <Square className="h-4 w-4 mr-1" />
                      Stop
                    </Button>
                    <Button size="sm" variant="outline">
                      <Settings className="h-4 w-4 mr-1" />
                      Settings
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Trading Tips */}
      <Alert>
        <TrendingUp className="h-4 w-4" />
        <AlertDescription>
          <strong>Pro Tip:</strong> Start with small amounts and monitor your sessions closely. 
          AI trading works best with proper risk management and regular adjustments.
        </AlertDescription>
      </Alert>
    </div>
  )
}
