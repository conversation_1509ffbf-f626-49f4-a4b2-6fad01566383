'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { 
  Play, 
  Pause, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Activity, 
  Clock, 
  Target,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  BarChart3,
  Zap,
  Brain,
  Settings
} from 'lucide-react'

interface TradingSignal {
  symbol: string
  action: 'BUY' | 'SELL' | 'HOLD'
  confidence: number
  price: number
  quantity: number
  reasoning: string
  timestamp: string
  source: 'AI' | 'MANUAL' | 'STRATEGY'
}

interface Order {
  id: string
  exchangeId: string
  symbol: string
  side: 'buy' | 'sell'
  type: 'market' | 'limit' | 'stop' | 'stop_limit'
  amount: number
  price?: number
  status: 'pending' | 'open' | 'closed' | 'canceled' | 'failed'
  filled: number
  remaining: number
  cost: number
  timestamp: string
}

interface TradingStats {
  totalTrades: number
  winningTrades: number
  losingTrades: number
  winRate: number
  totalPnl: number
  totalVolume: number
}

export default function TradingDashboard() {
  const [isEngineRunning, setIsEngineRunning] = useState(false)
  const [signals, setSignals] = useState<TradingSignal[]>([])
  const [orders, setOrders] = useState<Order[]>([])
  const [stats, setStats] = useState<TradingStats>({
    totalTrades: 0,
    winningTrades: 0,
    losingTrades: 0,
    winRate: 0,
    totalPnl: 0,
    totalVolume: 0
  })
  const [portfolio, setPortfolio] = useState<Record<string, number>>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadTradingData()
    const interval = setInterval(loadTradingData, 5000) // Update every 5 seconds
    return () => clearInterval(interval)
  }, [])

  const loadTradingData = async () => {
    try {
      // Mock data - would normally fetch from API
      const mockSignals: TradingSignal[] = [
        {
          symbol: 'BTCUSDT',
          action: 'BUY',
          confidence: 85,
          price: 43250,
          quantity: 0.023,
          reasoning: 'Strong bullish momentum with RSI oversold recovery',
          timestamp: new Date(Date.now() - 300000).toISOString(),
          source: 'AI'
        },
        {
          symbol: 'ETHUSDT',
          action: 'SELL',
          confidence: 72,
          price: 2650,
          quantity: 0.5,
          reasoning: 'Bearish divergence on MACD with resistance rejection',
          timestamp: new Date(Date.now() - 600000).toISOString(),
          source: 'AI'
        }
      ]

      const mockOrders: Order[] = [
        {
          id: 'order-1',
          exchangeId: 'binance',
          symbol: 'BTCUSDT',
          side: 'buy',
          type: 'limit',
          amount: 0.023,
          price: 43250,
          status: 'open',
          filled: 0,
          remaining: 0.023,
          cost: 0,
          timestamp: new Date(Date.now() - 180000).toISOString()
        }
      ]

      const mockStats: TradingStats = {
        totalTrades: 47,
        winningTrades: 32,
        losingTrades: 15,
        winRate: 68.1,
        totalPnl: 1247.83,
        totalVolume: 45230.50
      }

      const mockPortfolio = {
        USDT: 5420.30,
        BTC: 0.125,
        ETH: 2.45,
        ADA: 1250.0
      }

      setSignals(mockSignals)
      setOrders(mockOrders)
      setStats(mockStats)
      setPortfolio(mockPortfolio)
      setLoading(false)
    } catch (error) {
      console.error('Failed to load trading data:', error)
      setLoading(false)
    }
  }

  const toggleEngine = async () => {
    try {
      if (isEngineRunning) {
        // Stop engine
        await fetch('/api/trading/stop', { method: 'POST' })
        setIsEngineRunning(false)
      } else {
        // Start engine
        await fetch('/api/trading/start', { method: 'POST' })
        setIsEngineRunning(true)
      }
    } catch (error) {
      console.error('Failed to toggle trading engine:', error)
    }
  }

  const getSignalIcon = (action: string) => {
    switch (action) {
      case 'BUY':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'SELL':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const getOrderStatusBadge = (status: string) => {
    const variants: Record<string, any> = {
      open: 'default',
      closed: 'secondary',
      canceled: 'destructive',
      failed: 'destructive',
      pending: 'outline'
    }
    return <Badge variant={variants[status] || 'outline'}>{status.toUpperCase()}</Badge>
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64 text-white">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading trading dashboard...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6 text-white">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Live Trading Dashboard</h1>
          <p className="text-gray-600">Monitor and control AI-powered trading operations</p>
        </div>
        
        {/* Engine Control */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${isEngineRunning ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
            <span className="text-sm font-medium">
              {isEngineRunning ? 'Engine Running' : 'Engine Stopped'}
            </span>
          </div>
          
          <Button
            onClick={toggleEngine}
            variant={isEngineRunning ? 'destructive' : 'default'}
            className="flex items-center gap-2"
          >
            {isEngineRunning ? (
              <>
                <Pause className="h-4 w-4" />
                Stop Engine
              </>
            ) : (
              <>
                <Play className="h-4 w-4" />
                Start Engine
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Trades</p>
                <p className="text-xl font-bold">{stats.totalTrades}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Win Rate</p>
                <p className="text-xl font-bold">{stats.winRate.toFixed(1)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Total PnL</p>
                <p className={`text-xl font-bold ${stats.totalPnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  ${stats.totalPnl.toFixed(2)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Volume</p>
                <p className="text-xl font-bold">${stats.totalVolume.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Winning</p>
                <p className="text-xl font-bold text-green-600">{stats.winningTrades}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm text-gray-600">Losing</p>
                <p className="text-xl font-bold text-red-600">{stats.losingTrades}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Signals */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Recent AI Signals
              </CardTitle>
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {signals.map((signal, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getSignalIcon(signal.action)}
                    <div>
                      <div className="font-medium">{signal.symbol}</div>
                      <div className="text-sm text-gray-600">
                        {signal.action} {signal.quantity} @ ${signal.price.toLocaleString()}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{signal.confidence}%</div>
                    <div className="text-xs text-gray-500">
                      {new Date(signal.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Active Orders */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Active Orders
              </CardTitle>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {orders.map((order) => (
                <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">{order.symbol}</div>
                    <div className="text-sm text-gray-600">
                      {order.side.toUpperCase()} {order.amount} @ ${order.price?.toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-500">{order.exchangeId}</div>
                  </div>
                  <div className="text-right">
                    {getOrderStatusBadge(order.status)}
                    <div className="text-xs text-gray-500 mt-1">
                      {new Date(order.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Portfolio Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Portfolio Balance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {Object.entries(portfolio).map(([currency, amount]) => (
              <div key={currency} className="text-center p-3 border rounded-lg">
                <div className="font-medium text-lg">{currency}</div>
                <div className="text-sm text-gray-600">
                  {amount.toLocaleString(undefined, { 
                    minimumFractionDigits: currency === 'USDT' ? 2 : 4,
                    maximumFractionDigits: currency === 'USDT' ? 2 : 4
                  })}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
