'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import {
  useCombinedMarketData,
  useWebSocketStatus,
  useTradingSignals,
  useAggregatedMarketData
} from '../../lib/hooks/useWebSocket'
import { TrendingUp, TrendingDown, Activity, Wifi, WifiOff, Plus, X } from 'lucide-react'

interface SymbolCardProps {
  symbol: string
  exchanges: string[]
  onRemove: () => void
}

function SymbolCard({ symbol, exchanges, onRemove }: SymbolCardProps) {
  const { data: combinedData, isLoading: combinedLoading, error: combinedError } = useCombinedMarketData(symbol, exchanges)
  const { data: aggregatedData } = useAggregatedMarketData(symbol)
  const { signals } = useTradingSignals(symbol)

  if (combinedLoading) {
    return (
      <Card className="animate-pulse">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <div className="h-6 bg-gray-200 rounded w-20"></div>
            <Button variant="ghost" size="sm" onClick={onRemove}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="h-8 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (combinedError) {
    return (
      <Card className="border-red-200">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-sm text-red-600">{symbol}</CardTitle>
            <Button variant="ghost" size="sm" onClick={onRemove}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-red-500">{combinedError}</p>
        </CardContent>
      </Card>
    )
  }

  const marketData = combinedData?.marketData
  const sentiment = combinedData?.sentiment
  const isPositive = marketData?.changePercent24h && marketData.changePercent24h > 0

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg font-bold">{symbol}</CardTitle>
          <Button variant="ghost" size="sm" onClick={onRemove}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex gap-1">
          {combinedData?.exchanges.map(exchange => (
            <Badge key={exchange} variant="secondary" className="text-xs">
              {exchange}
            </Badge>
          ))}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Price Information */}
        {marketData && (
          <div>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl font-bold">
                ${marketData.price.toLocaleString(undefined, { 
                  minimumFractionDigits: 2, 
                  maximumFractionDigits: 6 
                })}
              </span>
              <div className={`flex items-center gap-1 ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                {isPositive ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
                <span className="font-medium">
                  {marketData.changePercent24h > 0 ? '+' : ''}{marketData.changePercent24h.toFixed(2)}%
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
              <div>
                <span className="block">24h High</span>
                <span className="font-medium">${marketData.high24h.toLocaleString()}</span>
              </div>
              <div>
                <span className="block">24h Low</span>
                <span className="font-medium">${marketData.low24h.toLocaleString()}</span>
              </div>
              <div>
                <span className="block">Volume</span>
                <span className="font-medium">{(marketData.volume24h / 1000000).toFixed(2)}M</span>
              </div>
              <div>
                <span className="block">Spread</span>
                <span className="font-medium">${marketData.spread.toFixed(4)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Aggregated Data */}
        {aggregatedData && (
          <div className="border-t pt-3">
            <h4 className="text-sm font-medium mb-2">Cross-Exchange Data</h4>
            <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
              <div>
                <span className="block">Avg Price</span>
                <span className="font-medium">${aggregatedData.averagePrice.toFixed(6)}</span>
              </div>
              <div>
                <span className="block">Price Spread</span>
                <span className="font-medium">${aggregatedData.priceSpread.toFixed(4)}</span>
              </div>
              <div className="col-span-2">
                <span className="block">Total Volume</span>
                <span className="font-medium">{(aggregatedData.totalVolume / 1000000).toFixed(2)}M</span>
              </div>
            </div>
          </div>
        )}

        {/* Sentiment */}
        {sentiment && (
          <div className="border-t pt-3">
            <h4 className="text-sm font-medium mb-2">Sentiment Analysis</h4>
            <div className="flex items-center gap-2 mb-2">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all ${
                    sentiment.sentimentScore > 0.6 ? 'bg-green-500' :
                    sentiment.sentimentScore < 0.4 ? 'bg-red-500' : 'bg-yellow-500'
                  }`}
                  style={{ width: `${sentiment.sentimentScore * 100}%` }}
                />
              </div>
              <span className="text-sm font-medium">
                {(sentiment.sentimentScore * 100).toFixed(0)}%
              </span>
            </div>
            <div className="flex justify-between text-xs text-gray-600">
              <span>Mood: {sentiment.marketMood}</span>
              <span>Confidence: {(sentiment.confidence * 100).toFixed(0)}%</span>
            </div>
            <Badge 
              variant={
                sentiment.recommendation === 'BUY' ? 'default' :
                sentiment.recommendation === 'SELL' ? 'destructive' : 'secondary'
              }
              className="mt-2"
            >
              {sentiment.recommendation}
            </Badge>
          </div>
        )}

        {/* Trading Signals */}
        {signals && (
          <div className="border-t pt-3">
            <h4 className="text-sm font-medium mb-2">Trading Signal</h4>
            <div className="flex items-center gap-2 mb-2">
              <Badge 
                variant={
                  signals.signal === 'BUY' ? 'default' :
                  signals.signal === 'SELL' ? 'destructive' : 'secondary'
                }
              >
                {signals.signal}
              </Badge>
              <span className="text-sm text-gray-600">
                {signals.confidence}% confidence
              </span>
            </div>
            {signals.reasons.length > 0 && (
              <div className="text-xs text-gray-600">
                <span className="block mb-1">Reasons:</span>
                <ul className="list-disc list-inside space-y-1">
                  {signals.reasons.map((reason, index) => (
                    <li key={index}>{reason}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Last Update */}
        <div className="text-xs text-gray-500 border-t pt-2">
          Last update: {combinedData ? new Date(combinedData.timestamp).toLocaleTimeString() : 'N/A'}
        </div>
      </CardContent>
    </Card>
  )
}

export default function RealTimeDashboard() {
  const [symbols, setSymbols] = useState<string[]>(['BTCUSDT', 'ETHUSDT'])
  const [newSymbol, setNewSymbol] = useState('')
  const [selectedExchanges] = useState(['binance', 'bybit'])
  const { status } = useWebSocketStatus()

  const addSymbol = () => {
    if (newSymbol && !symbols.includes(newSymbol.toUpperCase())) {
      setSymbols([...symbols, newSymbol.toUpperCase()])
      setNewSymbol('')
    }
  }

  const removeSymbol = (symbolToRemove: string) => {
    setSymbols(symbols.filter(symbol => symbol !== symbolToRemove))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      addSymbol()
    }
  }

  return (
    <div className="space-y-6 text-white">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Real-Time Trading Dashboard</h1>
          <p className="text-gray-600">Live market data and sentiment analysis</p>
        </div>
        
        {/* Connection Status */}
        <div className="flex items-center gap-2">
          {status?.sentiment.connected ? (
            <div className="flex items-center gap-1 text-green-600">
              <Wifi className="h-4 w-4" />
              <span className="text-sm">Sentiment Connected</span>
            </div>
          ) : (
            <div className="flex items-center gap-1 text-red-600">
              <WifiOff className="h-4 w-4" />
              <span className="text-sm">Sentiment Disconnected</span>
            </div>
          )}
          
          <div className="flex items-center gap-1 text-blue-600">
            <Activity className="h-4 w-4" />
            <span className="text-sm">
              {status?.marketData.totalSubscriptions || 0} Market Feeds
            </span>
          </div>
        </div>
      </div>

      {/* Add Symbol */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Add Symbol</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              placeholder="Enter symbol (e.g., BTCUSDT)"
              value={newSymbol}
              onChange={(e) => setNewSymbol(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
            />
            <Button onClick={addSymbol} disabled={!newSymbol}>
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </div>
          <div className="mt-2 text-sm text-gray-600">
            Monitoring exchanges: {selectedExchanges.join(', ')}
          </div>
        </CardContent>
      </Card>

      {/* Symbol Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {symbols.map(symbol => (
          <SymbolCard
            key={symbol}
            symbol={symbol}
            exchanges={selectedExchanges}
            onRemove={() => removeSymbol(symbol)}
          />
        ))}
      </div>

      {symbols.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No symbols added</h3>
            <p className="text-gray-600">Add a trading symbol to start monitoring real-time data</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
