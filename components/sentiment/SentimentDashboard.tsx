'use client'

import { useState, useEffect } from 'react'
import { 
  TrendingUp, 
  TrendingDown, 
  AlertCircle, 
  Brain, 
  Globe, 
  MessageSquare,
  BarChart3,
  Zap,
  Activity,
  Clock,
  Target,
  ThumbsUp,
  ThumbsDown,
  Minus
} from 'lucide-react'

interface SentimentAnalysis {
  symbol: string
  recommendation: 'STRONG_BUY' | 'BUY' | 'NEUTRAL' | 'SELL' | 'STRONG_SELL'
  confidence: number
  sentimentScore: number
  socialVolume: 'HIGH' | 'MEDIUM' | 'LOW'
  influencerSentiment: 'BULLISH' | 'NEUTRAL' | 'BEARISH'
  newsImpact: 'POSITIVE' | 'NEUTRAL' | 'NEGATIVE'
  reasoning: string
  keyFactors: string[]
  marketMood: 'FEAR' | 'GREED' | 'NEUTRAL' | 'UNCERTAINTY'
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  aiConsensus: {
    [providerName: string]: {
      score: number
      confidence: number
      reasoning: string
    }
  }
  correlationWithPrice: number
  volumeCorrelation: number
  technicalAlignment: 'BULLISH' | 'BEARISH' | 'NEUTRAL'
  emergingNarratives: string[]
  socialMediaTrends: string[]
  timeframe: string
}

export default function SentimentDashboard() {
  const [selectedSymbol, setSelectedSymbol] = useState('BTC')
  const [analysis, setAnalysis] = useState<SentimentAnalysis | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const symbols = ['BTC', 'ETH', 'ADA', 'SOL', 'AVAX', 'DOT', 'LINK', 'UNI']

  useEffect(() => {
    analyzeSentiment(selectedSymbol)
  }, [selectedSymbol])

  const analyzeSentiment = async (symbol: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/sentiment/analyze?symbol=${symbol}`)
      const data = await response.json()
      
      if (data.success) {
        setAnalysis(data.data.data)
      } else {
        setError(data.error || 'Analysis failed')
      }
    } catch (error) {
      setError('Failed to fetch sentiment analysis')
      console.error('Sentiment analysis error:', error)
    } finally {
      setLoading(false)
    }
  }

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'STRONG_BUY': return 'text-green-400 bg-green-400/10 border-green-400'
      case 'BUY': return 'text-green-300 bg-green-300/10 border-green-300'
      case 'NEUTRAL': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400'
      case 'SELL': return 'text-red-300 bg-red-300/10 border-red-300'
      case 'STRONG_SELL': return 'text-red-400 bg-red-400/10 border-red-400'
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400'
    }
  }

  const getRecommendationIcon = (recommendation: string) => {
    switch (recommendation) {
      case 'STRONG_BUY': return <TrendingUp className="w-4 h-4" />
      case 'BUY': return <TrendingUp className="w-4 h-4" />
      case 'NEUTRAL': return <Minus className="w-4 h-4" />
      case 'SELL': return <TrendingDown className="w-4 h-4" />
      case 'STRONG_SELL': return <TrendingDown className="w-4 h-4" />
      default: return <AlertCircle className="w-4 h-4" />
    }
  }

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment.toLowerCase()) {
      case 'bullish': case 'positive': return <ThumbsUp className="w-4 h-4 text-green-400" />
      case 'bearish': case 'negative': return <ThumbsDown className="w-4 h-4 text-red-400" />
      default: return <Minus className="w-4 h-4 text-yellow-400" />
    }
  }

  const getVolumeColor = (volume: string) => {
    switch (volume) {
      case 'HIGH': return 'text-red-400'
      case 'MEDIUM': return 'text-yellow-400'
      case 'LOW': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'HIGH': return 'text-red-400 bg-red-400/10'
      case 'MEDIUM': return 'text-yellow-400 bg-yellow-400/10'
      case 'LOW': return 'text-green-400 bg-green-400/10'
      default: return 'text-gray-400 bg-gray-400/10'
    }
  }

  return (
    <div className="min-h-screen bg-black text-white p-4">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2 flex items-center">
          <Brain className="w-8 h-8 mr-3 text-blue-400" />
          Multi-Model Sentiment Analysis
        </h1>
        <p className="text-gray-400">
          Advanced AI-powered sentiment analysis using multiple specialized models
        </p>
      </div>

      {/* Symbol Selection */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Select Cryptocurrency</h3>
        <div className="flex flex-wrap gap-2">
          {symbols.map((symbol) => (
            <button
              key={symbol}
              onClick={() => setSelectedSymbol(symbol)}
              className={`px-4 py-2 rounded-lg font-medium transition-all ${
                selectedSymbol === symbol
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              {symbol}
            </button>
          ))}
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
            <span>Analyzing sentiment with multiple AI models...</span>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-900/20 border border-red-500 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-400 mr-2" />
            <span className="text-red-400">{error}</span>
          </div>
        </div>
      )}

      {/* Analysis Results */}
      {analysis && !loading && (
        <div className="space-y-6">
          {/* Main Analysis Card */}
          <div className="bg-gray-900 rounded-xl p-6 border border-gray-800">
            <div className="flex justify-between items-start mb-6">
              <div>
                <h2 className="text-2xl font-bold mb-2">{selectedSymbol} Sentiment Analysis</h2>
                <div className="flex items-center space-x-4">
                  <div className={`flex items-center px-3 py-1 rounded-full border ${getRecommendationColor(analysis.recommendation)}`}>
                    {getRecommendationIcon(analysis.recommendation)}
                    <span className="ml-2 font-medium">{analysis.recommendation}</span>
                  </div>
                  <div className="text-gray-400">
                    <Clock className="w-4 h-4 inline mr-1" />
                    {analysis.timeframe}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-blue-400 mb-1">
                  {analysis.sentimentScore}
                </div>
                <div className="text-sm text-gray-400">Sentiment Score</div>
                <div className="text-sm text-gray-500">(-100 to +100)</div>
              </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <Target className="w-5 h-5 text-blue-400" />
                  <span className="text-2xl font-bold">{analysis.confidence}%</span>
                </div>
                <div className="text-sm text-gray-400">Confidence</div>
              </div>

              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <Activity className={`w-5 h-5 ${getVolumeColor(analysis.socialVolume)}`} />
                  <span className={`text-2xl font-bold ${getVolumeColor(analysis.socialVolume)}`}>
                    {analysis.socialVolume}
                  </span>
                </div>
                <div className="text-sm text-gray-400">Social Volume</div>
              </div>

              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <AlertCircle className="w-5 h-5 text-orange-400" />
                  <span className={`px-2 py-1 rounded text-xs font-bold ${getRiskColor(analysis.riskLevel)}`}>
                    {analysis.riskLevel}
                  </span>
                </div>
                <div className="text-sm text-gray-400">Risk Level</div>
              </div>

              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <Globe className="w-5 h-5 text-purple-400" />
                  <span className="text-lg font-bold text-purple-400">
                    {analysis.marketMood}
                  </span>
                </div>
                <div className="text-sm text-gray-400">Market Mood</div>
              </div>
            </div>

            {/* Reasoning */}
            <div className="bg-gray-800 rounded-lg p-4 mb-6">
              <h3 className="font-semibold mb-2 flex items-center">
                <MessageSquare className="w-5 h-5 mr-2 text-green-400" />
                AI Analysis
              </h3>
              <p className="text-gray-300 leading-relaxed">{analysis.reasoning}</p>
            </div>

            {/* Key Factors */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-gray-800 rounded-lg p-4">
                <h3 className="font-semibold mb-3 flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2 text-blue-400" />
                  Key Factors
                </h3>
                <ul className="space-y-2">
                  {analysis.keyFactors.map((factor, index) => (
                    <li key={index} className="flex items-start">
                      <div className="w-2 h-2 rounded-full bg-blue-400 mt-2 mr-3 flex-shrink-0"></div>
                      <span className="text-gray-300">{factor}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="bg-gray-800 rounded-lg p-4">
                <h3 className="font-semibold mb-3 flex items-center">
                  <Zap className="w-5 h-5 mr-2 text-yellow-400" />
                  Social Media Trends
                </h3>
                <ul className="space-y-2">
                  {analysis.socialMediaTrends.map((trend, index) => (
                    <li key={index} className="flex items-start">
                      <div className="w-2 h-2 rounded-full bg-yellow-400 mt-2 mr-3 flex-shrink-0"></div>
                      <span className="text-gray-300">{trend}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* AI Consensus */}
          <div className="bg-gray-900 rounded-xl p-6 border border-gray-800">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <Brain className="w-6 h-6 mr-2 text-purple-400" />
              AI Model Consensus
            </h3>
            <div className="grid gap-4">
              {Object.entries(analysis.aiConsensus).map(([provider, data]) => (
                <div key={provider} className="bg-gray-800 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-blue-400">{provider}</h4>
                    <div className="text-right">
                      <div className="text-lg font-bold">{data.score}</div>
                      <div className="text-sm text-gray-400">{data.confidence}% conf.</div>
                    </div>
                  </div>
                  <p className="text-gray-300 text-sm">{data.reasoning}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Technical Indicators */}
          <div className="bg-gray-900 rounded-xl p-6 border border-gray-800">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <Activity className="w-6 h-6 mr-2 text-green-400" />
              Technical Alignment
            </h3>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="bg-gray-800 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold mb-2">
                  {getSentimentIcon(analysis.influencerSentiment)}
                </div>
                <div className="text-lg font-medium">{analysis.influencerSentiment}</div>
                <div className="text-sm text-gray-400">Influencer Sentiment</div>
              </div>

              <div className="bg-gray-800 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold mb-2">
                  {getSentimentIcon(analysis.newsImpact)}
                </div>
                <div className="text-lg font-medium">{analysis.newsImpact}</div>
                <div className="text-sm text-gray-400">News Impact</div>
              </div>

              <div className="bg-gray-800 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold mb-2">
                  {getSentimentIcon(analysis.technicalAlignment)}
                </div>
                <div className="text-lg font-medium">{analysis.technicalAlignment}</div>
                <div className="text-sm text-gray-400">Technical Alignment</div>
              </div>
            </div>

            <div className="mt-4 grid md:grid-cols-2 gap-4">
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="text-sm text-gray-400 mb-1">Price Correlation</div>
                <div className="text-2xl font-bold">
                  {(analysis.correlationWithPrice * 100).toFixed(1)}%
                </div>
              </div>
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="text-sm text-gray-400 mb-1">Volume Correlation</div>
                <div className="text-2xl font-bold">
                  {(analysis.volumeCorrelation * 100).toFixed(1)}%
                </div>
              </div>
            </div>
          </div>

          {/* Emerging Narratives */}
          {analysis.emergingNarratives.length > 0 && (
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-800">
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <Globe className="w-6 h-6 mr-2 text-indigo-400" />
                Emerging Narratives
              </h3>
              <div className="space-y-3">
                {analysis.emergingNarratives.map((narrative, index) => (
                  <div key={index} className="bg-gray-800 rounded-lg p-3">
                    <div className="flex items-start">
                      <div className="w-2 h-2 rounded-full bg-indigo-400 mt-2 mr-3 flex-shrink-0"></div>
                      <span className="text-gray-300">{narrative}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}