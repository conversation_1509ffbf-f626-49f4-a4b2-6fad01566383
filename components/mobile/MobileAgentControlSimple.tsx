'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, Power, Settings, TrendingUp, AlertTriangle } from 'lucide-react'

export default function MobileAgentControl() {
  const [agents, setAgents] = useState<any[]>([])
  const [positions, setPositions] = useState<any[]>([])
  const [totalPnL, setTotalPnL] = useState(0)
  const [engineStatus, setEngineStatus] = useState({ running: false, autoMode: false })
  const [loading, setLoading] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [command, setCommand] = useState('')
  const [autoMode, setAutoMode] = useState(false)

  useEffect(() => {
    fetchAgents()
    fetchPositions()
    fetchPortfolioData()
    fetchEngineStatus()
  }, [])

  const fetchAgents = async () => {
    try {
      // Mock data for development
      setAgents([
        {
          id: '1',
          name: 'Market Analyzer',
          status: 'ACTIVE',
          specialization: 'technical_analysis',
          performance: 87,
          currentTask: 'Analyzing BTC/USDT patterns'
        },
        {
          id: '2',
          name: 'Risk Manager',
          status: 'THINKING',
          specialization: 'risk_management',
          performance: 92,
          currentTask: 'Calculating portfolio exposure'
        },
        {
          id: '3',
          name: 'News Sentinel',
          status: 'TRADING',
          specialization: 'sentiment_analysis',
          performance: 78,
          currentTask: 'Processing market news'
        }
      ])
    } catch (error) {
      console.error('Error in fetchAgents:', error)
    }
  }

  const fetchPositions = async () => {
    try {
      // Mock data for development
      setPositions([
        {
          id: '1',
          symbol: 'BTC/USDT',
          side: 'LONG',
          size: 0.5,
          entry_price: 43250,
          current_price: 44100,
          pnl: 425,
          pnl_percentage: 1.96
        },
        {
          id: '2',
          symbol: 'ETH/USDT',
          side: 'SHORT',
          size: 2.0,
          entry_price: 2650,
          current_price: 2580,
          pnl: 140,
          pnl_percentage: 2.64
        }
      ])
    } catch (error) {
      console.error('Error in fetchPositions:', error)
    }
  }

  const fetchPortfolioData = async () => {
    try {
      // Mock data
      setTotalPnL(565)
    } catch (error) {
      console.error('Error in fetchPortfolioData:', error)
    }
  }

  const fetchEngineStatus = async () => {
    try {
      // Mock data
      setEngineStatus({ running: true, autoMode: false })
    } catch (error) {
      console.error('Error in fetchEngineStatus:', error)
    }
  }

  const toggleAutoMode = async () => {
    setLoading(true)
    try {
      const newAutoMode = !autoMode
      setAutoMode(newAutoMode)
      setEngineStatus(prev => ({ ...prev, autoMode: newAutoMode }))
    } catch (error) {
      console.error('Error toggling auto mode:', error)
    } finally {
      setLoading(false)
    }
  }

  const triggerAgentAnalysis = async (agentId: string, symbol: string) => {
    console.log(`Triggering analysis for agent ${agentId} on ${symbol}`)
  }

  const startVoiceRecording = () => {
    setIsListening(true)
    setTimeout(() => {
      setIsListening(false)
      setCommand('Analyze BTC market trends')
    }, 3000)
  }

  const executeVoiceCommand = (cmd: string) => {
    console.log('Executing command:', cmd)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'border-orange-500 bg-orange-900/20'
      case 'THINKING':
        return 'border-blue-500 bg-blue-900/20'
      case 'TRADING':
        return 'border-green-500 bg-green-900/20'
      case 'ERROR':
        return 'border-red-500 bg-red-900/20'
      case 'PAUSED':
        return 'border-yellow-500 bg-yellow-900/20'
      default:
        return 'border-gray-500 bg-gray-900/20'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return '🔥'
      case 'THINKING':
        return '🧠'
      case 'TRADING':
        return '⚡'
      case 'ERROR':
        return '❌'
      case 'PAUSED':
        return '⏸️'
      default:
        return '⚪'
    }
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Status Bar */}
      <div className="bg-gray-900 p-4 flex justify-between items-center border-b border-gray-800">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full animate-pulse ${
            engineStatus.running ? 'bg-green-400' : 'bg-red-400'
          }`} />
          <span className={`text-sm font-mono ${
            engineStatus.running ? 'text-green-400' : 'text-red-400'
          }`}>
            {engineStatus.running ? 'ENGINE RUNNING' : 'ENGINE STOPPED'}
          </span>
        </div>
        <h1 className="text-lg font-bold">CRYPTOAGENT PRO</h1>
        <div className="text-xs text-gray-400 font-mono">
          {new Date().toLocaleTimeString()}
        </div>
      </div>

      {/* Portfolio Summary */}
      <div className="p-6 bg-gradient-to-r from-gray-900 to-gray-800 border-b border-gray-700">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Portfolio P&L</h2>
          <div className={`text-4xl font-bold mb-2 ${
            totalPnL >= 0 ? 'text-green-400' : 'text-red-400'
          }`}>
            {totalPnL >= 0 ? '+' : ''}${totalPnL.toFixed(2)}
          </div>
          <p className="text-gray-400 text-sm">
            {positions.length} Active Position{positions.length !== 1 ? 's' : ''}
          </p>
        </div>

        {/* Active Positions */}
        {positions.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <span className="mr-2">📊</span>
              Active Positions
            </h3>
            <div className="space-y-3">
              {positions.map((position) => (
                <div
                  key={position.id}
                  className="bg-gray-800 rounded-lg p-4 border border-gray-700"
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-semibold">{position.symbol}</h4>
                      <p className="text-sm text-gray-400">
                        {position.side} • {position.size} units
                      </p>
                    </div>
                    <div className="text-right">
                      <p className={`font-bold ${
                        position.pnl >= 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {position.pnl >= 0 ? '+' : ''}${position.pnl}
                      </p>
                      <p className={`text-sm ${
                        position.pnl_percentage >= 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {position.pnl_percentage >= 0 ? '+' : ''}{position.pnl_percentage.toFixed(2)}%
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Auto Mode Toggle */}
      <div className="p-6 border-b border-gray-800 bg-gradient-to-r from-gray-900 to-gray-800">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="font-bold text-lg mb-1">Auto Trading Mode</h3>
            <p className="text-sm text-gray-400 mb-2">
              {autoMode ? '🤖 Fully autonomous trading active' : '👤 Manual control enabled'}
            </p>
            {loading && (
              <div className="flex items-center text-xs text-blue-400">
                <div className="animate-spin w-3 h-3 border border-blue-400 border-t-transparent rounded-full mr-2"></div>
                Updating...
              </div>
            )}
          </div>
          <button
            onClick={toggleAutoMode}
            disabled={loading}
            className={`relative w-20 h-10 rounded-full transition-all duration-500 transform hover:scale-105 ${
              autoMode
                ? 'bg-gradient-to-r from-green-500 to-green-600 shadow-lg shadow-green-500/40'
                : 'bg-gradient-to-r from-gray-600 to-gray-700 shadow-lg shadow-gray-600/20'
            } ${loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          >
            <div className={`absolute w-8 h-8 bg-white rounded-full top-1 transition-all duration-500 shadow-lg ${
              autoMode ? 'left-11 bg-gradient-to-r from-white to-green-50' : 'left-1 bg-gradient-to-r from-white to-gray-50'
            }`} />
            <div className="absolute inset-0 flex items-center justify-center text-sm font-bold">
              {autoMode ? '🚀' : '⏸️'}
            </div>
          </button>
        </div>
      </div>

      {/* Agent Status Cards */}
      <div className="p-4 space-y-4">
        <h3 className="text-xl font-bold mb-6 flex items-center">
          <span className="mr-3 text-2xl">🤖</span>
          AI Agents
          <span className="ml-auto text-sm font-normal text-gray-400">
            {agents.filter(a => a.status === 'ACTIVE').length}/{agents.length} Active
          </span>
        </h3>

        {agents.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-4xl mb-4">🤖</div>
            <p className="text-gray-400">Loading agents...</p>
          </div>
        ) : (
          agents.map((agent) => (
            <div
              key={agent.id}
              className={`
                rounded-2xl p-5 border-2 transition-all duration-500 shadow-xl hover:shadow-2xl transform hover:scale-[1.02]
                ${getStatusColor(agent.status)}
                backdrop-blur-sm relative overflow-hidden
              `}
            >
              {/* Background Glow Effect */}
              <div className={`absolute inset-0 opacity-10 ${
                agent.status === 'ACTIVE' ? 'bg-gradient-to-br from-orange-500 to-red-500' :
                agent.status === 'THINKING' ? 'bg-gradient-to-br from-blue-500 to-purple-500' :
                agent.status === 'TRADING' ? 'bg-gradient-to-br from-green-500 to-emerald-500' :
                'bg-gradient-to-br from-gray-500 to-gray-600'
              }`}></div>

              {/* Agent Content */}
              <div className="relative z-10">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{getStatusIcon(agent.status)}</span>
                    <h4 className="font-semibold">{agent.name}</h4>
                  </div>
                  <span className={`
                    px-2 py-1 rounded-full text-xs font-bold uppercase tracking-wide
                    ${agent.status === 'ACTIVE' ? 'bg-orange-500 text-black' :
                      agent.status === 'THINKING' ? 'bg-blue-500 text-white' :
                      agent.status === 'TRADING' ? 'bg-green-500 text-white' :
                      agent.status === 'ERROR' ? 'bg-red-500 text-white' :
                      agent.status === 'PAUSED' ? 'bg-yellow-500 text-black' :
                      'bg-gray-500 text-white'}
                  `}>
                    {agent.status}
                  </span>
                </div>

                {/* Current Task */}
                {agent.currentTask && (
                  <div className="mb-3">
                    <p className="text-yellow-400 text-xs font-medium uppercase tracking-wide">
                      Current Task:
                    </p>
                    <p className="text-gray-300 text-sm">
                      {agent.currentTask}
                    </p>
                  </div>
                )}

                {/* Performance Bar */}
                {agent.performance > 0 && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs">
                      <span className="text-gray-400 uppercase tracking-wide">Performance</span>
                      <span className="font-bold">
                        {agent.performance}%
                      </span>
                    </div>
                    <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                      <div
                        className={`h-full transition-all duration-1000 ${
                          agent.performance >= 80 ? 'bg-green-400' :
                          agent.performance >= 60 ? 'bg-yellow-400' :
                          agent.performance >= 40 ? 'bg-orange-400' :
                          'bg-red-400'
                        }`}
                        style={{ width: `${agent.performance}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* Specialization Tag */}
                <div className="mt-3 pt-2 border-t border-gray-700">
                  <span className="inline-block bg-blue-600/20 text-blue-400 px-2 py-1 rounded text-xs font-medium">
                    {agent.specialization.replace('_', ' ').toUpperCase()}
                  </span>
                </div>

                {/* Quick Actions */}
                <div className="mt-3 flex space-x-2">
                  <button
                    onClick={() => triggerAgentAnalysis(agent.id, 'BTC/USDT')}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-xs font-medium transition-colors"
                  >
                    Analyze BTC
                  </button>
                  <button
                    onClick={() => triggerAgentAnalysis(agent.id, 'ETH/USDT')}
                    className="flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-3 rounded text-xs font-medium transition-colors"
                  >
                    Analyze ETH
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Voice Command Interface */}
      <div className="p-4 border-t border-gray-800">
        <div className="relative">
          <input
            type="text"
            value={command}
            onChange={(e) => setCommand(e.target.value)}
            placeholder="Enter command or use voice..."
            className="
              w-full bg-gray-800 text-white rounded-xl px-4 py-3 pr-16
              border border-gray-600 focus:border-blue-500 focus:outline-none
              placeholder-gray-400
            "
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                executeVoiceCommand(command)
                setCommand('')
              }
            }}
          />
          
          {/* Voice Input Button */}
          <button
            onClick={startVoiceRecording}
            disabled={isListening}
            className={`
              absolute right-2 top-1/2 transform -translate-y-1/2
              w-10 h-10 rounded-full flex items-center justify-center
              transition-all duration-200
              ${isListening 
                ? 'bg-red-500 animate-pulse' 
                : 'bg-blue-600 hover:bg-blue-700'
              }
            `}
          >
            {isListening ? <MicOff size={20} /> : <Mic size={20} />}
          </button>
        </div>
        
        {isListening && (
          <p className="text-center text-sm text-blue-400 mt-2 animate-pulse">
            🎤 Listening... Speak your command
          </p>
        )}
      </div>

      {/* Quick Action Buttons */}
      <div className="p-4 grid grid-cols-2 gap-3">
        <button className="bg-gray-800 hover:bg-gray-700 text-white py-4 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2">
          <TrendingUp size={20} />
          <span>Market Overview</span>
        </button>
        <button className="bg-gray-800 hover:bg-gray-700 text-white py-4 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2">
          <AlertTriangle size={20} />
          <span>Risk Check</span>
        </button>
        <button className="bg-gray-800 hover:bg-gray-700 text-white py-4 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2">
          <Power size={20} />
          <span>Emergency Stop</span>
        </button>
        <button className="bg-gray-800 hover:bg-gray-700 text-white py-4 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2">
          <Settings size={20} />
          <span>Settings</span>
        </button>
      </div>

      {/* Bottom Safe Area */}
      <div className="h-8" />
    </div>
  )
}
