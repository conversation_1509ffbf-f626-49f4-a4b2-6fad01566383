'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@supabase/supabase-js'
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, Power, Settings, TrendingUp, AlertTriangle, Play, Pause, RefreshCw } from 'lucide-react'

// Type definitions for Web Speech API
interface SpeechRecognitionEvent {
  results: {
    [index: number]: {
      [index: number]: {
        transcript: string
        confidence: number
      }
    }
    length: number
  }
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean
  interimResults: boolean
  lang: string
  onresult: (event: SpeechRecognitionEvent) => void
  onerror: (event: Event) => void
  start: () => void
}

interface Window {
  webkitSpeechRecognition: new () => SpeechRecognition
}

interface Agent {
  id: string
  name: string
  status: 'ACTIVE' | 'THINKING' | 'ERROR' | 'IDLE' | 'TRADING' | 'PAUSED'
  currentTask?: string
  performance: number
  specialization: string
}

interface Position {
  id: string
  symbol: string
  side: 'BUY' | 'SELL'
  size: number
  entry_price: number
  current_price?: number
  pnl: number
  pnl_percentage: number
  status: string
}

export default function MobileAgentControl() {
  const [agents, setAgents] = useState<Agent[]>([])
  const [positions, setPositions] = useState<Position[]>([])
  const [autoMode, setAutoMode] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [command, setCommand] = useState('')
  const [portfolioValue, setPortfolioValue] = useState(0)
  const [totalPnL, setTotalPnL] = useState(0)
  const [engineStatus, setEngineStatus] = useState({ running: false, autoMode: false })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isInitialLoading, setIsInitialLoading] = useState(true)
  
  // Create Supabase client
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  useEffect(() => {
    fetchAgents()
    fetchPositions()
    fetchPortfolioData()
    fetchEngineStatus()
    
    // Real-time updates
    const subscription = supabase
      .channel('agent-updates')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'trading_agents'
      }, () => {
        fetchAgents()
      })
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'trading_positions'
      }, () => {
        fetchPositions()
        fetchPortfolioData()
      })
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [supabase])

  const fetchAgents = async () => {
    try {
      const { data, error } = await supabase
        .from('trading_agents')
        .select('*')
        .order('created_at')

      if (error) {
        console.error('Error fetching agents:', error)
        // Set mock data if database fails
        setAgents([
          {
            id: '1',
            name: 'Technical Analysis',
            status: 'ACTIVE',
            currentTask: 'Analyzing BTC/USDT patterns',
            performance: 90,
            specialization: 'technical_analysis'
          },
          {
            id: '2',
            name: 'Sentiment Monitor',
            status: 'THINKING',
            currentTask: 'Processing social media sentiment',
            performance: 84,
            specialization: 'sentiment_analysis'
          },
          {
            id: '3',
            name: 'Risk Manager',
            status: 'ACTIVE',
            currentTask: 'Monitoring portfolio exposure',
            performance: 82,
            specialization: 'risk_management'
          }
        ])
        return
      }

      setAgents(data || [])
    } catch (error) {
      console.error('Failed to fetch agents:', error)
      setAgents([])
    }
  }

  const fetchPositions = async () => {
    try {
      const { data, error } = await supabase
        .from('trading_positions')
        .select('*')
        .eq('status', 'OPEN')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching positions:', error)
        setPositions([])
        return
      }

      setPositions(data || [])
    } catch (error) {
      console.error('Failed to fetch positions:', error)
      setPositions([])
    }
  }

  const fetchPortfolioData = async () => {
    try {
      const response = await fetch('/api/portfolio/summary')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()
      setPortfolioValue(data.totalValue || 34241.5)
      setTotalPnL(data.totalPnL || 1240.75)
    } catch (error) {
      console.error('Failed to fetch portfolio data:', error)
      // Set mock data
      setPortfolioValue(34241.5)
      setTotalPnL(1240.75)
    }
  }

  const fetchEngineStatus = async () => {
    try {
      const response = await fetch('/api/trading/engine-status')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()
      setEngineStatus(data)
    } catch (error) {
      console.error('Failed to fetch engine status:', error)
      setEngineStatus({ running: false, autoMode: false })
    }
  }

  const toggleAutoMode = async () => {
    setLoading(true)
    const newMode = !autoMode
    setAutoMode(newMode)

    try {
      await fetch('/api/trading/auto-mode', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled: newMode })
      })
      
      fetchEngineStatus()
    } catch (error) {
      console.error('Failed to toggle auto mode:', error)
    } finally {
      setLoading(false)
    }
  }

  const startVoiceRecording = () => {
    setIsListening(true)
    
    if ('webkitSpeechRecognition' in window) {
      const recognition = new (window as Window).webkitSpeechRecognition()
      recognition.continuous = false
      recognition.interimResults = false
      recognition.lang = 'en-US'

      recognition.onresult = (event: SpeechRecognitionEvent) => {
        const transcript = event.results[0][0].transcript
        setCommand(transcript)
        executeVoiceCommand(transcript)
        setIsListening(false)
      }

      recognition.onerror = () => {
        setIsListening(false)
      }

      recognition.start()
    }
  }

  const executeVoiceCommand = async (voiceCommand: string) => {
    try {
      const response = await fetch('/api/trading/voice-command', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ command: voiceCommand })
      })
      
      const result = await response.json()
      console.log('Voice command result:', result)
      
      // Refresh data after command execution
      fetchPositions()
      fetchPortfolioData()
    } catch (error) {
      console.error('Failed to execute voice command:', error)
    }
  }

  const triggerAgentAnalysis = async (agentId: string, symbol: string) => {
    try {
      await fetch('/api/agents/tasks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agent_id: agentId,
          type: 'ANALYSIS',
          symbol: symbol,
          parameters: {}
        })
      })
      
      console.log(`Triggered analysis for ${symbol} by agent ${agentId}`)
    } catch (error) {
      console.error('Failed to trigger analysis:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'border-orange-500 bg-orange-500/10 shadow-orange-500/20'
      case 'THINKING': return 'border-blue-500 bg-blue-500/10 shadow-blue-500/20'
      case 'TRADING': return 'border-green-500 bg-green-500/10 shadow-green-500/20'
      case 'ERROR': return 'border-red-500 bg-red-500/10 shadow-red-500/20'
      case 'PAUSED': return 'border-yellow-500 bg-yellow-500/10 shadow-yellow-500/20'
      default: return 'border-gray-600 bg-gray-800/50 shadow-gray-800/20'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE': return '🟡'
      case 'THINKING': return '🔵' 
      case 'TRADING': return '🟢'
      case 'ERROR': return '🔴'
      case 'PAUSED': return '🟡'
      default: return '⚫'
    }
  }

  // Loading Screen
  if (isInitialLoading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <h2 className="text-xl font-bold mb-2">Initializing CryptoAgent Pro</h2>
          <p className="text-gray-400">Loading agents and portfolio data...</p>
        </div>
      </div>
    )
  }

  // Error Screen
  if (error) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center p-4">
        <div className="text-center max-w-md">
          <div className="text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold mb-2 text-red-400">Connection Error</h2>
          <p className="text-gray-400 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Status Bar */}
      <div className="bg-gray-900 p-4 flex justify-between items-center border-b border-gray-800">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full animate-pulse ${
            engineStatus.running ? 'bg-green-400' : 'bg-red-400'
          }`} />
          <span className={`text-sm font-mono ${
            engineStatus.running ? 'text-green-400' : 'text-red-400'
          }`}>
            {engineStatus.running ? 'ENGINE RUNNING' : 'ENGINE STOPPED'}
          </span>
        </div>
        <h1 className="text-lg font-bold">CRYPTOAGENT PRO</h1>
        <div className="text-xs text-gray-400 font-mono">
          {new Date().toLocaleTimeString()}
        </div>
      </div>

      {/* Portfolio Summary */}
      <div className="p-6 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 border-b border-gray-700 relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 right-0 w-32 h-32 bg-blue-500 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-green-500 rounded-full blur-2xl"></div>
        </div>

        <div className="relative z-10">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold mb-2">AGENT ZERO CONTROL</h2>
            <div className="text-4xl font-mono font-bold text-white mb-1">
              ${portfolioValue.toLocaleString()}
            </div>
            <div className={`text-lg font-semibold ${totalPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {totalPnL >= 0 ? '+' : ''}${totalPnL.toFixed(2)} ({((totalPnL / portfolioValue) * 100).toFixed(2)}%)
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-4 gap-3 text-center">
            <div className="bg-black/30 backdrop-blur-sm rounded-lg p-3">
              <div className="text-xs text-gray-400 uppercase">BTC</div>
              <div className="text-sm font-bold text-orange-400">2.1</div>
            </div>
            <div className="bg-black/30 backdrop-blur-sm rounded-lg p-3">
              <div className="text-xs text-gray-400 uppercase">ETH</div>
              <div className="text-sm font-bold text-blue-400">15.8</div>
            </div>
            <div className="bg-black/30 backdrop-blur-sm rounded-lg p-3">
              <div className="text-xs text-gray-400 uppercase">USDC</div>
              <div className="text-sm font-bold text-green-400">5.2K</div>
            </div>
            <div className="bg-black/30 backdrop-blur-sm rounded-lg p-3">
              <div className="text-xs text-gray-400 uppercase">AVAX</div>
              <div className="text-sm font-bold text-red-400">120</div>
            </div>
          </div>
        </div>
      </div>
        
        {/* Open Positions */}
        {positions.length > 0 && (
          <div className="mt-4">
            <p className="text-gray-400 text-xs uppercase tracking-wide mb-2">Open Positions ({positions.length})</p>
            <div className="space-y-2">
              {positions.slice(0, 3).map((position) => (
                <div key={position.id} className="flex justify-between items-center bg-gray-800 rounded-lg p-2">
                  <div>
                    <p className="text-sm font-medium">{position.symbol}</p>
                    <p className="text-xs text-gray-400">{position.side} {position.size}</p>
                  </div>
                  <div className="text-right">
                    <p className={`text-sm font-bold ${position.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {position.pnl >= 0 ? '+' : ''}${position.pnl.toFixed(2)}
                    </p>
                    <p className={`text-xs ${position.pnl_percentage >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {position.pnl_percentage >= 0 ? '+' : ''}{position.pnl_percentage.toFixed(2)}%
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Auto Mode Toggle */}
      <div className="p-6 border-b border-gray-800 bg-gradient-to-r from-gray-900 to-gray-800">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="font-bold text-lg mb-1">Auto Trading Mode</h3>
            <p className="text-sm text-gray-400 mb-2">
              {autoMode ? '🤖 Fully autonomous trading active' : '👤 Manual control enabled'}
            </p>
            {loading && (
              <div className="flex items-center text-xs text-blue-400">
                <div className="animate-spin w-3 h-3 border border-blue-400 border-t-transparent rounded-full mr-2"></div>
                Updating...
              </div>
            )}
          </div>
          <button
            onClick={toggleAutoMode}
            disabled={loading}
            className={`relative w-20 h-10 rounded-full transition-all duration-500 transform hover:scale-105 ${
              autoMode
                ? 'bg-gradient-to-r from-green-500 to-green-600 shadow-lg shadow-green-500/40'
                : 'bg-gradient-to-r from-gray-600 to-gray-700 shadow-lg shadow-gray-600/20'
            } ${loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          >
            <div className={`absolute w-8 h-8 bg-white rounded-full top-1 transition-all duration-500 shadow-lg ${
              autoMode ? 'left-11 bg-gradient-to-r from-white to-green-50' : 'left-1 bg-gradient-to-r from-white to-gray-50'
            }`} />
            <div className="absolute inset-0 flex items-center justify-center text-sm font-bold">
              {autoMode ? '🚀' : '⏸️'}
            </div>
          </button>
        </div>
      </div>

      {/* Agent Status Cards */}
      <div className="p-4 space-y-4">
        <h3 className="text-xl font-bold mb-6 flex items-center">
          <span className="mr-3 text-2xl">🤖</span>
          AI Agents
          <span className="ml-auto text-sm font-normal text-gray-400">
            {agents.filter(a => a.status === 'ACTIVE').length}/{agents.length} Active
          </span>
        </h3>

        {agents.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-4xl mb-4">🤖</div>
            <p className="text-gray-400">Loading agents...</p>
          </div>
        ) : (
          agents.map((agent) => (
            <div
              key={agent.id}
              className={`
                rounded-2xl p-5 border-2 transition-all duration-500 shadow-xl hover:shadow-2xl transform hover:scale-[1.02]
                ${getStatusColor(agent.status)}
                backdrop-blur-sm relative overflow-hidden
              `}
            >
              {/* Background Glow Effect */}
              <div className={`absolute inset-0 opacity-10 ${
                agent.status === 'ACTIVE' ? 'bg-gradient-to-br from-orange-500 to-red-500' :
                agent.status === 'THINKING' ? 'bg-gradient-to-br from-blue-500 to-purple-500' :
                agent.status === 'TRADING' ? 'bg-gradient-to-br from-green-500 to-emerald-500' :
                'bg-gradient-to-br from-gray-500 to-gray-600'
              }`}></div>

              {/* Agent Header */}
              <div className="relative z-10">
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center space-x-2">
                <span className="text-lg">{getStatusIcon(agent.status)}</span>
                <h4 className="font-semibold">{agent.name}</h4>
              </div>
              <span className={`
                px-2 py-1 rounded-full text-xs font-bold uppercase tracking-wide
                ${agent.status === 'ACTIVE' ? 'bg-orange-500 text-black' :
                  agent.status === 'THINKING' ? 'bg-blue-500 text-white' :
                  agent.status === 'TRADING' ? 'bg-green-500 text-white' :
                  agent.status === 'ERROR' ? 'bg-red-500 text-white' :
                  agent.status === 'PAUSED' ? 'bg-yellow-500 text-black' :
                  'bg-gray-500 text-white'}
              `}>
                {agent.status}
              </span>
            </div>

            {/* Current Task */}
            {agent.currentTask && (
              <div className="mb-3">
                <p className="text-yellow-400 text-xs font-medium uppercase tracking-wide">
                  Current Task:
                </p>
                <p className="text-gray-300 text-sm">
                  {agent.currentTask}
                </p>
              </div>
            )}

            {/* Performance Bar */}
            {agent.performance > 0 && (
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-gray-400 uppercase tracking-wide">Performance</span>
                  <span className="font-bold">
                    {agent.performance}%
                  </span>
                </div>
                <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className={`h-full transition-all duration-1000 ${
                      agent.performance >= 80 ? 'bg-green-400' :
                      agent.performance >= 60 ? 'bg-yellow-400' :
                      agent.performance >= 40 ? 'bg-orange-400' :
                      'bg-red-400'
                    }`}
                    style={{ width: `${agent.performance}%` }}
                  />
                </div>
              </div>
            )}

            {/* Specialization Tag */}
            <div className="mt-3 pt-2 border-t border-gray-700">
              <span className="inline-block bg-blue-600/20 text-blue-400 px-2 py-1 rounded text-xs font-medium">
                {agent.specialization.replace('_', ' ').toUpperCase()}
              </span>
            </div>

            {/* Quick Actions */}
            <div className="mt-3 flex space-x-2">
              <button
                onClick={() => triggerAgentAnalysis(agent.id, 'BTC/USDT')}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-xs font-medium transition-colors"
              >
                Analyze BTC
              </button>
              <button
                onClick={() => triggerAgentAnalysis(agent.id, 'ETH/USDT')}
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-3 rounded text-xs font-medium transition-colors"
              >
                Analyze ETH
              </button>
            </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Voice Command Interface */}
      <div className="p-4 border-t border-gray-800">
        <div className="relative">
          <input
            type="text"
            value={command}
            onChange={(e) => setCommand(e.target.value)}
            placeholder="Enter command or use voice..."
            className="
              w-full bg-gray-800 text-white rounded-xl px-4 py-3 pr-16
              border border-gray-600 focus:border-blue-500 focus:outline-none
              placeholder-gray-400
            "
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                executeVoiceCommand(command)
                setCommand('')
              }
            }}
          />
          
          {/* Voice Input Button */}
          <button
            onClick={startVoiceRecording}
            disabled={isListening}
            className={`
              absolute right-2 top-1/2 transform -translate-y-1/2
              w-10 h-10 rounded-full flex items-center justify-center
              transition-all duration-200
              ${isListening 
                ? 'bg-red-500 animate-pulse' 
                : 'bg-blue-600 hover:bg-blue-700'
              }
            `}
          >
            {isListening ? <MicOff size={20} /> : <Mic size={20} />}
          </button>
        </div>
        
        {isListening && (
          <p className="text-center text-sm text-blue-400 mt-2 animate-pulse">
            🎤 Listening... Speak your command
          </p>
        )}
      </div>

      {/* Quick Action Buttons */}
      <div className="p-4 grid grid-cols-2 gap-3">
        <button className="bg-gray-800 hover:bg-gray-700 text-white py-4 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2">
          <TrendingUp size={20} />
          <span>Market Overview</span>
        </button>
        <button className="bg-gray-800 hover:bg-gray-700 text-white py-4 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2">
          <AlertTriangle size={20} />
          <span>Risk Check</span>
        </button>
        <button className="bg-gray-800 hover:bg-gray-700 text-white py-4 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2">
          <Power size={20} />
          <span>Emergency Stop</span>
        </button>
        <button className="bg-gray-800 hover:bg-gray-700 text-white py-4 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2">
          <Settings size={20} />
          <span>Settings</span>
        </button>
      </div>

      {/* Bottom Safe Area */}
      <div className="h-8" />
    </div>
  )
}