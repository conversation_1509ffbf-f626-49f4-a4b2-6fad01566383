'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { 
  Shield, 
  Key, 
  TestTube, 
  CheckCircle, 
  AlertCircle, 
  Settings,
  Eye,
  EyeOff,
  Wifi,
  WifiOff,
  DollarSign,
  TrendingUp,
  Lock,
  Unlock,
  RefreshCw
} from 'lucide-react'

interface ExchangeConfig {
  id: string
  name: string
  displayName: string
  description: string
  website: string
  fees: { maker: number; taker: number }
  limits: { minOrder: number; maxOrder: number; minNotional: number }
  features: { spot: boolean; futures: boolean; margin: boolean; lending: boolean; staking: boolean }
  credentials?: any
  enabled: boolean
  testMode: boolean
  connectionStatus: 'connected' | 'disconnected' | 'error' | 'testing'
  errorMessage?: string
  lastConnected?: string
}

interface TradingSettings {
  exchangeId: string
  defaultO<PERSON>rType: 'market' | 'limit' | 'stop' | 'stop_limit'
  defaultTimeInForce: 'GTC' | 'IOC' | 'FOK'
  maxPositionSize: number
  maxDailyLoss: number
  enableStopLoss: boolean
  defaultStopLoss: number
  enableTakeProfit: boolean
  defaultTakeProfit: number
  slippage: number
  minOrderSize: number
  maxOrderSize: number
  enablePaperTrading: boolean
  confirmOrders: boolean
}

export default function ExchangeConfigDashboard() {
  const [exchanges, setExchanges] = useState<ExchangeConfig[]>([])
  const [selectedExchange, setSelectedExchange] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'overview' | 'credentials' | 'trading' | 'testing'>('overview')
  const [showCredentials, setShowCredentials] = useState<Record<string, boolean>>({})
  const [loading, setLoading] = useState(true)
  const [testing, setTesting] = useState<Record<string, boolean>>({})

  useEffect(() => {
    loadExchanges()
  }, [])

  const loadExchanges = async () => {
    try {
      // Load real exchange status from API
      const response = await fetch('/api/exchanges/status')
      const data = await response.json()

      if (data.exchanges) {
        const exchangeConfigs: ExchangeConfig[] = data.exchanges.map((ex: any) => ({
          id: ex.id,
          name: ex.name,
          displayName: ex.displayName,
          description: ex.description,
          website: `https://www.${ex.id}.com`,
          fees: ex.fees,
          limits: { minOrder: 0.00001, maxOrder: 1000000, minNotional: 10 },
          features: ex.features,
          enabled: ex.enabled,
          testMode: ex.testMode,
          rateLimit: { requestsPerSecond: 20, requestsPerMinute: 1200 },
          supportedPairs: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT'],
          connectionStatus: ex.connectionStatus,
          credentials: ex.credentials.hasApiKey && ex.credentials.hasSecretKey ? {
            apiKey: '***',
            apiSecret: '***',
            passphrase: ex.credentials.hasPassphrase ? '***' : '',
            testnet: ex.credentials.testnet
          } : undefined
        }))

        setExchanges(exchangeConfigs)
        setLoading(false)
        return
      }

      // Fallback to mock data if API fails
      const mockExchanges: ExchangeConfig[] = [
        {
          id: 'binance',
          name: 'binance',
          displayName: 'Binance',
          description: 'World\'s largest cryptocurrency exchange',
          website: 'https://www.binance.com',
          fees: { maker: 0.001, taker: 0.001 },
          limits: { minOrder: 0.00001, maxOrder: 9000000, minNotional: 10 },
          features: { spot: true, futures: true, margin: true, lending: true, staking: true },
          enabled: false,
          testMode: true,
          connectionStatus: 'disconnected'
        },
        {
          id: 'bybit',
          name: 'bybit',
          displayName: 'Bybit',
          description: 'Leading derivatives exchange',
          website: 'https://www.bybit.com',
          fees: { maker: 0.0001, taker: 0.0006 },
          limits: { minOrder: 0.00001, maxOrder: 1000000, minNotional: 1 },
          features: { spot: true, futures: true, margin: true, lending: false, staking: false },
          enabled: false,
          testMode: true,
          connectionStatus: 'disconnected'
        },
        {
          id: 'mexc',
          name: 'mexc',
          displayName: 'MEXC',
          description: 'Global digital asset trading platform',
          website: 'https://www.mexc.com',
          fees: { maker: 0.002, taker: 0.002 },
          limits: { minOrder: 0.00001, maxOrder: 1000000, minNotional: 5 },
          features: { spot: true, futures: true, margin: true, lending: false, staking: true },
          enabled: false,
          testMode: true,
          connectionStatus: 'disconnected'
        },
        {
          id: 'kucoin',
          name: 'kucoin',
          displayName: 'KuCoin',
          description: 'The People\'s Exchange',
          website: 'https://www.kucoin.com',
          fees: { maker: 0.001, taker: 0.001 },
          limits: { minOrder: 0.00001, maxOrder: 1000000, minNotional: 1 },
          features: { spot: true, futures: true, margin: true, lending: true, staking: true },
          enabled: false,
          testMode: true,
          connectionStatus: 'disconnected'
        }
      ]

      setExchanges(mockExchanges)
      setLoading(false)
    } catch (error) {
      console.error('Failed to load exchanges:', error)
      setLoading(false)
    }
  }

  const testConnection = async (exchangeId: string) => {
    setTesting(prev => ({ ...prev, [exchangeId]: true }))

    try {
      const exchange = exchanges.find(ex => ex.id === exchangeId)
      if (!exchange?.credentials) {
        throw new Error('No credentials configured')
      }

      // Real API call to test connection
      const response = await fetch('/api/exchanges/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          exchangeId,
          credentials: exchange.credentials,
          testMode: exchange.testMode
        })
      })

      const result = await response.json()

      if (result.success) {
        setExchanges(prev => prev.map(ex =>
          ex.id === exchangeId
            ? {
                ...ex,
                connectionStatus: 'connected',
                lastConnected: new Date().toISOString(),
                errorMessage: undefined
              }
            : ex
        ))
      } else {
        throw new Error(result.message || 'Connection failed')
      }
    } catch (error) {
      setExchanges(prev => prev.map(ex =>
        ex.id === exchangeId
          ? {
              ...ex,
              connectionStatus: 'error',
              errorMessage: error instanceof Error ? error.message : 'Connection failed'
            }
          : ex
      ))
    } finally {
      setTesting(prev => ({ ...prev, [exchangeId]: false }))
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'testing':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      default:
        return <WifiOff className="h-5 w-5 text-gray-500" />
    }
  }

  const getExchangeIcon = (exchangeId: string) => {
    const icons: Record<string, string> = {
      binance: '🟡',
      bybit: '🟠',
      mexc: '🔵',
      kucoin: '🟢',
      okx: '⚫'
    }
    return icons[exchangeId] || '🔄'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64 text-white">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading exchange configurations...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6 text-white">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Exchange Configuration</h1>
          <p className="text-gray-600">Configure exchange API connections and trading settings</p>
        </div>
        
        {/* Status Summary */}
        <div className="flex gap-4">
          <div className="flex items-center gap-2 text-green-600">
            <Wifi className="h-5 w-5" />
            <span className="text-sm">
              {exchanges.filter(e => e.connectionStatus === 'connected').length} Connected
            </span>
          </div>
          <div className="flex items-center gap-2 text-blue-600">
            <Shield className="h-5 w-5" />
            <span className="text-sm">
              {exchanges.filter(e => e.enabled).length} Enabled
            </span>
          </div>
        </div>
      </div>

      {/* Exchange Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {exchanges.map((exchange) => (
          <Card 
            key={exchange.id}
            className={`cursor-pointer transition-all hover:shadow-lg ${
              selectedExchange === exchange.id 
                ? 'ring-2 ring-blue-500 bg-blue-50' 
                : 'hover:bg-gray-50'
            }`}
            onClick={() => setSelectedExchange(exchange.id)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">{getExchangeIcon(exchange.id)}</span>
                  <CardTitle className="text-lg">{exchange.displayName}</CardTitle>
                </div>
                {getStatusIcon(exchange.connectionStatus)}
              </div>
              <div className="flex gap-2">
                <Badge variant={exchange.enabled ? 'default' : 'secondary'}>
                  {exchange.enabled ? 'Enabled' : 'Disabled'}
                </Badge>
                <Badge variant={exchange.testMode ? 'outline' : 'destructive'}>
                  {exchange.testMode ? 'Testnet' : 'Live'}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              <p className="text-sm text-gray-600 line-clamp-2">
                {exchange.description}
              </p>

              {/* Fees */}
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Maker/Taker:</span>
                <span className="font-medium">
                  {(exchange.fees.maker * 100).toFixed(3)}%/{(exchange.fees.taker * 100).toFixed(3)}%
                </span>
              </div>

              {/* Features */}
              <div className="flex flex-wrap gap-1">
                {Object.entries(exchange.features)
                  .filter(([_, supported]) => supported)
                  .slice(0, 3)
                  .map(([feature]) => (
                    <Badge key={feature} variant="outline" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
              </div>

              {/* Last Connected */}
              {exchange.lastConnected && (
                <div className="text-xs text-gray-500">
                  Last: {new Date(exchange.lastConnected).toLocaleString()}
                </div>
              )}

              {/* Test Button */}
              <Button
                size="sm"
                variant="outline"
                className="w-full"
                onClick={(e) => {
                  e.stopPropagation()
                  testConnection(exchange.id)
                }}
                disabled={testing[exchange.id]}
              >
                {testing[exchange.id] ? (
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <TestTube className="h-4 w-4 mr-2" />
                )}
                {testing[exchange.id] ? 'Testing...' : 'Test'}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Configuration Panel */}
      {selectedExchange && (
        <ExchangeConfigPanel
          exchange={exchanges.find(e => e.id === selectedExchange)!}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          onUpdate={(updates) => {
            setExchanges(prev => prev.map(e => 
              e.id === selectedExchange ? { ...e, ...updates } : e
            ))
          }}
        />
      )}
    </div>
  )
}

interface ExchangeConfigPanelProps {
  exchange: ExchangeConfig
  activeTab: 'overview' | 'credentials' | 'trading' | 'testing'
  onTabChange: (tab: 'overview' | 'credentials' | 'trading' | 'testing') => void
  onUpdate: (updates: Partial<ExchangeConfig>) => void
}

function ExchangeConfigPanel({ exchange, activeTab, onTabChange, onUpdate }: ExchangeConfigPanelProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span className="text-2xl">{getExchangeIcon(exchange.id)}</span>
            <div>
              <CardTitle>{exchange.displayName} Configuration</CardTitle>
              <p className="text-gray-600">{exchange.description}</p>
            </div>
          </div>
          <Button
            variant="outline"
            onClick={() => window.open(exchange.website, '_blank')}
          >
            Visit Website
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {/* Tabs */}
        <div className="border-b mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: Settings },
              { id: 'credentials', label: 'API Keys', icon: Key },
              { id: 'trading', label: 'Trading', icon: TrendingUp },
              { id: 'testing', label: 'Testing', icon: TestTube }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => onTabChange(id as any)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <Icon className="h-4 w-4" />
                {label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <OverviewTab exchange={exchange} onUpdate={onUpdate} />
        )}
        {activeTab === 'credentials' && (
          <CredentialsTab exchange={exchange} onUpdate={onUpdate} />
        )}
        {activeTab === 'trading' && (
          <TradingTab exchange={exchange} onUpdate={onUpdate} />
        )}
        {activeTab === 'testing' && (
          <TestingTab exchange={exchange} />
        )}
      </CardContent>
    </Card>
  )
}

function getExchangeIcon(exchangeId: string) {
  const icons: Record<string, string> = {
    binance: '🟡',
    bybit: '🟠',
    mexc: '🔵',
    kucoin: '🟢',
    okx: '⚫'
  }
  return icons[exchangeId] || '🔄'
}

// Tab components would be implemented here
function OverviewTab({ exchange, onUpdate }: { exchange: ExchangeConfig, onUpdate: (updates: Partial<ExchangeConfig>) => void }) {
  return <div>Overview content for {exchange.displayName}</div>
}

function CredentialsTab({ exchange, onUpdate }: { exchange: ExchangeConfig, onUpdate: (updates: Partial<ExchangeConfig>) => void }) {
  const [credentials, setCredentials] = useState({
    apiKey: exchange.credentials?.apiKey || '',
    apiSecret: exchange.credentials?.apiSecret || '',
    passphrase: exchange.credentials?.passphrase || '',
    testnet: exchange.credentials?.testnet || true
  })
  const [showSecrets, setShowSecrets] = useState(false)
  const [saving, setSaving] = useState(false)

  const handleSave = async () => {
    setSaving(true)
    try {
      // Save credentials
      onUpdate({
        credentials,
        enabled: !!(credentials.apiKey && credentials.apiSecret)
      })

      // In real implementation, this would encrypt and save to backend
      console.log('Saving credentials for', exchange.id)
    } catch (error) {
      console.error('Failed to save credentials:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleClear = () => {
    setCredentials({
      apiKey: '',
      apiSecret: '',
      passphrase: '',
      testnet: true
    })
    onUpdate({
      credentials: undefined,
      enabled: false,
      connectionStatus: 'disconnected'
    })
  }

  return (
    <div className="space-y-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center gap-2 text-yellow-800">
          <Shield className="h-5 w-5" />
          <span className="font-medium">Security Notice</span>
        </div>
        <p className="text-yellow-700 text-sm mt-1">
          Your API keys are encrypted and stored securely. Never share your API keys with anyone.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">API Key</label>
            <div className="relative">
              <Input
                type={showSecrets ? 'text' : 'password'}
                value={credentials.apiKey}
                onChange={(e) => setCredentials(prev => ({ ...prev, apiKey: e.target.value }))}
                placeholder="Enter your API key"
                className="pr-10"
              />
              <button
                type="button"
                onClick={() => setShowSecrets(!showSecrets)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2"
              >
                {showSecrets ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">API Secret</label>
            <Input
              type={showSecrets ? 'text' : 'password'}
              value={credentials.apiSecret}
              onChange={(e) => setCredentials(prev => ({ ...prev, apiSecret: e.target.value }))}
              placeholder="Enter your API secret"
            />
          </div>

          {exchange.id === 'kucoin' && (
            <div>
              <label className="block text-sm font-medium mb-2">Passphrase</label>
              <Input
                type={showSecrets ? 'text' : 'password'}
                value={credentials.passphrase}
                onChange={(e) => setCredentials(prev => ({ ...prev, passphrase: e.target.value }))}
                placeholder="Enter your passphrase"
              />
            </div>
          )}

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="testnet"
              checked={credentials.testnet}
              onChange={(e) => setCredentials(prev => ({ ...prev, testnet: e.target.checked }))}
            />
            <label htmlFor="testnet" className="text-sm font-medium">
              Use Testnet (Recommended for testing)
            </label>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="font-medium">API Permissions Required</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Read account information</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Read trading history</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Place and cancel orders</span>
            </div>
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <span>Withdraw funds (NOT required)</span>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <h4 className="font-medium text-blue-800 mb-2">Setup Instructions</h4>
            <ol className="text-sm text-blue-700 space-y-1">
              <li>1. Log into your {exchange.displayName} account</li>
              <li>2. Go to API Management</li>
              <li>3. Create a new API key</li>
              <li>4. Enable trading permissions</li>
              <li>5. Copy the keys here</li>
            </ol>
          </div>
        </div>
      </div>

      <div className="flex gap-3">
        <Button onClick={handleSave} disabled={saving || !credentials.apiKey || !credentials.apiSecret}>
          {saving ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Lock className="h-4 w-4 mr-2" />
              Save Credentials
            </>
          )}
        </Button>

        <Button variant="outline" onClick={handleClear}>
          <Unlock className="h-4 w-4 mr-2" />
          Clear All
        </Button>
      </div>
    </div>
  )
}

function TradingTab({ exchange, onUpdate }: { exchange: ExchangeConfig, onUpdate: (updates: Partial<ExchangeConfig>) => void }) {
  const [settings, setSettings] = useState<TradingSettings>({
    exchangeId: exchange.id,
    defaultOrderType: 'limit',
    defaultTimeInForce: 'GTC',
    maxPositionSize: 10,
    maxDailyLoss: 5,
    enableStopLoss: true,
    defaultStopLoss: 2,
    enableTakeProfit: true,
    defaultTakeProfit: 5,
    slippage: 0.5,
    minOrderSize: 10,
    maxOrderSize: 1000,
    enablePaperTrading: true,
    confirmOrders: true
  })

  const handleSave = () => {
    // Save trading settings
    console.log('Saving trading settings for', exchange.id, settings)
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Order Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Order Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Default Order Type</label>
              <select
                value={settings.defaultOrderType}
                onChange={(e) => setSettings(prev => ({ ...prev, defaultOrderType: e.target.value as any }))}
                className="w-full p-2 border rounded-md"
              >
                <option value="market">Market</option>
                <option value="limit">Limit</option>
                <option value="stop">Stop</option>
                <option value="stop_limit">Stop Limit</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Time in Force</label>
              <select
                value={settings.defaultTimeInForce}
                onChange={(e) => setSettings(prev => ({ ...prev, defaultTimeInForce: e.target.value as any }))}
                className="w-full p-2 border rounded-md"
              >
                <option value="GTC">Good Till Cancelled</option>
                <option value="IOC">Immediate or Cancel</option>
                <option value="FOK">Fill or Kill</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Slippage Tolerance: {settings.slippage}%
              </label>
              <input
                type="range"
                min="0.1"
                max="5"
                step="0.1"
                value={settings.slippage}
                onChange={(e) => setSettings(prev => ({ ...prev, slippage: parseFloat(e.target.value) }))}
                className="w-full"
              />
            </div>
          </CardContent>
        </Card>

        {/* Risk Management */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Risk Management</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Max Position Size: {settings.maxPositionSize}%
              </label>
              <input
                type="range"
                min="1"
                max="50"
                step="1"
                value={settings.maxPositionSize}
                onChange={(e) => setSettings(prev => ({ ...prev, maxPositionSize: parseInt(e.target.value) }))}
                className="w-full"
              />
              <div className="text-xs text-gray-500">Percentage of total portfolio</div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Max Daily Loss: {settings.maxDailyLoss}%
              </label>
              <input
                type="range"
                min="1"
                max="20"
                step="1"
                value={settings.maxDailyLoss}
                onChange={(e) => setSettings(prev => ({ ...prev, maxDailyLoss: parseInt(e.target.value) }))}
                className="w-full"
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="enableStopLoss"
                  checked={settings.enableStopLoss}
                  onChange={(e) => setSettings(prev => ({ ...prev, enableStopLoss: e.target.checked }))}
                />
                <label htmlFor="enableStopLoss" className="text-sm font-medium">
                  Enable Stop Loss
                </label>
              </div>

              {settings.enableStopLoss && (
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Default Stop Loss: {settings.defaultStopLoss}%
                  </label>
                  <input
                    type="range"
                    min="0.5"
                    max="10"
                    step="0.5"
                    value={settings.defaultStopLoss}
                    onChange={(e) => setSettings(prev => ({ ...prev, defaultStopLoss: parseFloat(e.target.value) }))}
                    className="w-full"
                  />
                </div>
              )}
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="enableTakeProfit"
                  checked={settings.enableTakeProfit}
                  onChange={(e) => setSettings(prev => ({ ...prev, enableTakeProfit: e.target.checked }))}
                />
                <label htmlFor="enableTakeProfit" className="text-sm font-medium">
                  Enable Take Profit
                </label>
              </div>

              {settings.enableTakeProfit && (
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Default Take Profit: {settings.defaultTakeProfit}%
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="20"
                    step="0.5"
                    value={settings.defaultTakeProfit}
                    onChange={(e) => setSettings(prev => ({ ...prev, defaultTakeProfit: parseFloat(e.target.value) }))}
                    className="w-full"
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Order Limits */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Order Limits</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Minimum Order Size (USD)</label>
              <Input
                type="number"
                value={settings.minOrderSize}
                onChange={(e) => setSettings(prev => ({ ...prev, minOrderSize: parseFloat(e.target.value) }))}
                min="1"
                step="1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Maximum Order Size (USD)</label>
              <Input
                type="number"
                value={settings.maxOrderSize}
                onChange={(e) => setSettings(prev => ({ ...prev, maxOrderSize: parseFloat(e.target.value) }))}
                min="10"
                step="10"
              />
            </div>

            <div className="bg-gray-50 p-3 rounded-lg">
              <h4 className="font-medium mb-2">Exchange Limits</h4>
              <div className="text-sm space-y-1">
                <div>Min Order: {exchange.limits.minOrder}</div>
                <div>Max Order: {exchange.limits.maxOrder.toLocaleString()}</div>
                <div>Min Notional: ${exchange.limits.minNotional}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Safety Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Safety Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="enablePaperTrading"
                checked={settings.enablePaperTrading}
                onChange={(e) => setSettings(prev => ({ ...prev, enablePaperTrading: e.target.checked }))}
              />
              <label htmlFor="enablePaperTrading" className="text-sm font-medium">
                Enable Paper Trading
              </label>
            </div>
            <div className="text-xs text-gray-500">
              Simulate trades without real money
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="confirmOrders"
                checked={settings.confirmOrders}
                onChange={(e) => setSettings(prev => ({ ...prev, confirmOrders: e.target.checked }))}
              />
              <label htmlFor="confirmOrders" className="text-sm font-medium">
                Confirm All Orders
              </label>
            </div>
            <div className="text-xs text-gray-500">
              Require confirmation before placing orders
            </div>

            {settings.maxPositionSize > 25 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center gap-2 text-yellow-800">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm font-medium">High Risk Warning</span>
                </div>
                <p className="text-yellow-700 text-xs mt-1">
                  Position size over 25% is considered high risk
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-3">
        <Button onClick={handleSave}>
          <Settings className="h-4 w-4 mr-2" />
          Save Trading Settings
        </Button>

        <Button variant="outline" onClick={() => setSettings({
          exchangeId: exchange.id,
          defaultOrderType: 'limit',
          defaultTimeInForce: 'GTC',
          maxPositionSize: 10,
          maxDailyLoss: 5,
          enableStopLoss: true,
          defaultStopLoss: 2,
          enableTakeProfit: true,
          defaultTakeProfit: 5,
          slippage: 0.5,
          minOrderSize: 10,
          maxOrderSize: 1000,
          enablePaperTrading: true,
          confirmOrders: true
        })}>
          Reset to Defaults
        </Button>
      </div>
    </div>
  )
}

function TestingTab({ exchange }: { exchange: ExchangeConfig }) {
  return <div>Testing tools for {exchange.displayName}</div>
}
