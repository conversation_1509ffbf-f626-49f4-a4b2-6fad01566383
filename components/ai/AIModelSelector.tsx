'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { 
  Brain, 
  Zap, 
  DollarSign, 
  Clock, 
  Shield, 
  CheckCircle, 
  AlertCircle,
  Settings,
  Eye,
  EyeOff
} from 'lucide-react'

interface AIModel {
  id: string
  name: string
  provider: string
  description: string
  contextLength: number
  pricing: {
    input: number
    output: number
  }
  capabilities: string[]
  category: 'general' | 'coding' | 'reasoning' | 'creative' | 'analysis'
}

interface AIProvider {
  id: string
  name: string
  baseUrl: string
  apiKeyRequired: boolean
  models: AIModel[]
}

interface ModelSelectorProps {
  selectedModel?: string
  onModelSelect: (modelId: string) => void
  category?: AIModel['category']
  showPricing?: boolean
}

export function AIModelSelector({ 
  selectedModel, 
  onModelSelect, 
  category,
  showPricing = true 
}: ModelSelectorProps) {
  const [models, setModels] = useState<AIModel[]>([])
  const [providers, setProviders] = useState<AIProvider[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState('')

  useEffect(() => {
    loadModels()
  }, [category])

  const loadModels = async () => {
    try {
      // This would normally fetch from your API
      const mockModels: AIModel[] = [
        {
          id: 'anthropic/claude-3.5-sonnet',
          name: 'Claude 3.5 Sonnet',
          provider: 'openrouter',
          description: 'Most intelligent model, excellent for complex analysis',
          contextLength: 200000,
          pricing: { input: 3, output: 15 },
          capabilities: ['reasoning', 'analysis', 'coding', 'creative'],
          category: 'reasoning'
        },
        {
          id: 'openai/gpt-4o',
          name: 'GPT-4o',
          provider: 'openrouter',
          description: 'Latest GPT-4 with vision and multimodal capabilities',
          contextLength: 128000,
          pricing: { input: 5, output: 15 },
          capabilities: ['reasoning', 'analysis', 'vision', 'coding'],
          category: 'general'
        },
        {
          id: 'google/gemini-pro-1.5',
          name: 'Gemini Pro 1.5',
          provider: 'openrouter',
          description: 'Google\'s advanced model with large context window',
          contextLength: 1000000,
          pricing: { input: 2.5, output: 7.5 },
          capabilities: ['reasoning', 'analysis', 'coding'],
          category: 'analysis'
        },
        {
          id: 'anthropic/claude-3-haiku',
          name: 'Claude 3 Haiku',
          provider: 'openrouter',
          description: 'Fast and cost-effective for simple tasks',
          contextLength: 200000,
          pricing: { input: 0.25, output: 1.25 },
          capabilities: ['analysis', 'creative'],
          category: 'general'
        },
        {
          id: 'local-model',
          name: 'Local Model',
          provider: 'lmstudio',
          description: 'Locally running model via LM Studio',
          contextLength: 4096,
          pricing: { input: 0, output: 0 },
          capabilities: ['analysis', 'reasoning'],
          category: 'general'
        }
      ]

      const filteredModels = category 
        ? mockModels.filter(m => m.category === category)
        : mockModels

      setModels(filteredModels)
      setLoading(false)
    } catch (error) {
      console.error('Failed to load models:', error)
      setLoading(false)
    }
  }

  const filteredModels = models.filter(model =>
    model.name.toLowerCase().includes(filter.toLowerCase()) ||
    model.provider.toLowerCase().includes(filter.toLowerCase()) ||
    model.description.toLowerCase().includes(filter.toLowerCase())
  )

  const getCategoryColor = (category: string) => {
    const colors = {
      general: 'bg-blue-100 text-blue-800',
      reasoning: 'bg-purple-100 text-purple-800',
      analysis: 'bg-green-100 text-green-800',
      coding: 'bg-orange-100 text-orange-800',
      creative: 'bg-pink-100 text-pink-800'
    }
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'openrouter':
        return <Brain className="h-4 w-4" />
      case 'lmstudio':
        return <Shield className="h-4 w-4" />
      case 'openai':
        return <Zap className="h-4 w-4" />
      case 'anthropic':
        return <Brain className="h-4 w-4" />
      default:
        return <Settings className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading AI models...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Search Filter */}
      <div className="flex gap-4">
        <Input
          placeholder="Search models..."
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="flex-1"
        />
      </div>

      {/* Model Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredModels.map((model) => (
          <Card 
            key={model.id}
            className={`cursor-pointer transition-all hover:shadow-lg ${
              selectedModel === model.id 
                ? 'ring-2 ring-blue-500 bg-blue-50' 
                : 'hover:bg-gray-50'
            }`}
            onClick={() => onModelSelect(model.id)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  {getProviderIcon(model.provider)}
                  <CardTitle className="text-lg">{model.name}</CardTitle>
                </div>
                {selectedModel === model.id && (
                  <CheckCircle className="h-5 w-5 text-blue-600" />
                )}
              </div>
              <div className="flex gap-2">
                <Badge variant="secondary" className="text-xs">
                  {model.provider}
                </Badge>
                <Badge className={`text-xs ${getCategoryColor(model.category)}`}>
                  {model.category}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              <p className="text-sm text-gray-600 line-clamp-2">
                {model.description}
              </p>

              {/* Capabilities */}
              <div className="flex flex-wrap gap-1">
                {model.capabilities.slice(0, 3).map((capability) => (
                  <Badge key={capability} variant="outline" className="text-xs">
                    {capability}
                  </Badge>
                ))}
                {model.capabilities.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{model.capabilities.length - 3}
                  </Badge>
                )}
              </div>

              {/* Context Length */}
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Clock className="h-4 w-4" />
                <span>{model.contextLength.toLocaleString()} tokens</span>
              </div>

              {/* Pricing */}
              {showPricing && (
                <div className="flex items-center gap-2 text-sm">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  {model.pricing.input === 0 && model.pricing.output === 0 ? (
                    <span className="text-green-600 font-medium">Free (Local)</span>
                  ) : (
                    <span className="text-gray-600">
                      ${model.pricing.input}/${model.pricing.output} per 1M tokens
                    </span>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredModels.length === 0 && (
        <Card>
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No models found</h3>
            <p className="text-gray-600">
              Try adjusting your search criteria or check your provider configurations.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

interface ProviderConfigProps {
  onConfigUpdate: () => void
}

export function ProviderConfig({ onConfigUpdate }: ProviderConfigProps) {
  const [providers, setProviders] = useState<AIProvider[]>([])
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({})
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({})
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    loadProviders()
  }, [])

  const loadProviders = async () => {
    // Mock provider data
    const mockProviders: AIProvider[] = [
      {
        id: 'openrouter',
        name: 'OpenRouter',
        baseUrl: 'https://openrouter.ai/api/v1',
        apiKeyRequired: true,
        models: []
      },
      {
        id: 'lmstudio',
        name: 'LM Studio',
        baseUrl: 'http://localhost:1234/v1',
        apiKeyRequired: false,
        models: []
      },
      {
        id: 'openai',
        name: 'OpenAI',
        baseUrl: 'https://api.openai.com/v1',
        apiKeyRequired: true,
        models: []
      },
      {
        id: 'anthropic',
        name: 'Anthropic',
        baseUrl: 'https://api.anthropic.com/v1',
        apiKeyRequired: true,
        models: []
      }
    ]

    setProviders(mockProviders)
  }

  const handleApiKeyChange = (providerId: string, apiKey: string) => {
    setApiKeys(prev => ({ ...prev, [providerId]: apiKey }))
  }

  const toggleKeyVisibility = (providerId: string) => {
    setShowKeys(prev => ({ ...prev, [providerId]: !prev[providerId] }))
  }

  const saveConfiguration = async () => {
    setSaving(true)
    try {
      // Save API keys and configuration
      console.log('Saving provider configuration:', apiKeys)
      // This would normally save to your backend
      onConfigUpdate()
    } catch (error) {
      console.error('Failed to save configuration:', error)
    } finally {
      setSaving(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>AI Provider Configuration</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {providers.map((provider) => (
          <div key={provider.id} className="border rounded-lg p-4">
            <div className="flex items-center gap-3 mb-3">
              {getProviderIcon(provider.id)}
              <h3 className="font-medium">{provider.name}</h3>
              <Badge variant={provider.apiKeyRequired ? 'destructive' : 'secondary'}>
                {provider.apiKeyRequired ? 'API Key Required' : 'Local'}
              </Badge>
            </div>

            {provider.apiKeyRequired && (
              <div className="space-y-2">
                <label className="text-sm font-medium">API Key</label>
                <div className="flex gap-2">
                  <Input
                    type={showKeys[provider.id] ? 'text' : 'password'}
                    placeholder={`Enter ${provider.name} API key`}
                    value={apiKeys[provider.id] || ''}
                    onChange={(e) => handleApiKeyChange(provider.id, e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => toggleKeyVisibility(provider.id)}
                  >
                    {showKeys[provider.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            )}

            <div className="text-sm text-gray-600 mt-2">
              Base URL: {provider.baseUrl}
            </div>
          </div>
        ))}

        <Button 
          onClick={saveConfiguration} 
          disabled={saving}
          className="w-full"
        >
          {saving ? 'Saving...' : 'Save Configuration'}
        </Button>
      </CardContent>
    </Card>
  )
}

function getProviderIcon(provider: string) {
  switch (provider) {
    case 'openrouter':
      return <Brain className="h-4 w-4" />
    case 'lmstudio':
      return <Shield className="h-4 w-4" />
    case 'openai':
      return <Zap className="h-4 w-4" />
    case 'anthropic':
      return <Brain className="h-4 w-4" />
    default:
      return <Settings className="h-4 w-4" />
  }
}
