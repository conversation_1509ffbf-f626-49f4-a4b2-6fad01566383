'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { AIModelSelector, ProviderConfig } from './AIModelSelector'
import { 
  Brain, 
  Settings, 
  Activity, 
  DollarSign, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Zap
} from 'lucide-react'

interface AgentConfig {
  agentId: string
  agentName: string
  primaryModel: string
  fallbackModel?: string
  temperature: number
  maxTokens: number
  systemPrompt: string
  enabled: boolean
  priority: number
}

interface UsageStats {
  totalRequests: number
  totalTokens: number
  totalCost: number
  averageResponseTime: number
  successRate: number
}

export default function AIConfigDashboard() {
  const [activeTab, setActiveTab] = useState<'agents' | 'models' | 'providers' | 'usage'>('agents')
  const [agents, setAgents] = useState<AgentConfig[]>([])
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null)
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      // Mock data - would normally fetch from API
      const mockAgents: AgentConfig[] = [
        {
          agentId: 'technical-analysis',
          agentName: 'Technical Analysis Agent',
          primaryModel: 'anthropic/claude-3.5-sonnet',
          fallbackModel: 'openai/gpt-4o-mini',
          temperature: 0.2,
          maxTokens: 2048,
          systemPrompt: 'You are a professional cryptocurrency technical analyst...',
          enabled: true,
          priority: 1
        },
        {
          agentId: 'sentiment-analysis',
          agentName: 'Sentiment Analysis Agent',
          primaryModel: 'openai/gpt-4o',
          fallbackModel: 'anthropic/claude-3-haiku',
          temperature: 0.3,
          maxTokens: 1024,
          systemPrompt: 'You are a cryptocurrency sentiment analyst...',
          enabled: true,
          priority: 2
        },
        {
          agentId: 'risk-management',
          agentName: 'Risk Management Agent',
          primaryModel: 'anthropic/claude-3.5-sonnet',
          fallbackModel: 'google/gemini-pro-1.5',
          temperature: 0.1,
          maxTokens: 1536,
          systemPrompt: 'You are a cryptocurrency risk management specialist...',
          enabled: true,
          priority: 3
        }
      ]

      const mockUsage: UsageStats = {
        totalRequests: 1247,
        totalTokens: 2456789,
        totalCost: 12.34,
        averageResponseTime: 2.3,
        successRate: 98.5
      }

      setAgents(mockAgents)
      setUsageStats(mockUsage)
      setLoading(false)
    } catch (error) {
      console.error('Failed to load AI configuration:', error)
      setLoading(false)
    }
  }

  const handleAgentUpdate = (agentId: string, updates: Partial<AgentConfig>) => {
    setAgents(prev => prev.map(agent => 
      agent.agentId === agentId ? { ...agent, ...updates } : agent
    ))
  }

  const handleModelSelect = (agentId: string, modelId: string, type: 'primary' | 'fallback') => {
    const field = type === 'primary' ? 'primaryModel' : 'fallbackModel'
    handleAgentUpdate(agentId, { [field]: modelId })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading AI configuration...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">AI Configuration</h1>
          <p className="text-gray-600">Configure AI models and agents for trading analysis</p>
        </div>
        
        {/* Status Indicators */}
        <div className="flex gap-4">
          <div className="flex items-center gap-2 text-green-600">
            <CheckCircle className="h-5 w-5" />
            <span className="text-sm">3 Agents Active</span>
          </div>
          <div className="flex items-center gap-2 text-blue-600">
            <Brain className="h-5 w-5" />
            <span className="text-sm">5 Models Available</span>
          </div>
        </div>
      </div>

      {/* Usage Overview */}
      {usageStats && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Requests</p>
                  <p className="text-xl font-bold">{usageStats.totalRequests.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Tokens</p>
                  <p className="text-xl font-bold">{(usageStats.totalTokens / 1000000).toFixed(1)}M</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Cost</p>
                  <p className="text-xl font-bold">${usageStats.totalCost.toFixed(2)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">Avg Response</p>
                  <p className="text-xl font-bold">{usageStats.averageResponseTime}s</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Success Rate</p>
                  <p className="text-xl font-bold">{usageStats.successRate}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="border-b">
        <nav className="flex space-x-8">
          {[
            { id: 'agents', label: 'AI Agents', icon: Brain },
            { id: 'models', label: 'Model Selection', icon: Settings },
            { id: 'providers', label: 'Providers', icon: Zap },
            { id: 'usage', label: 'Usage & Costs', icon: DollarSign }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Icon className="h-4 w-4" />
              {label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'agents' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Agent List */}
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">AI Agents</h2>
                {agents.map((agent) => (
                  <Card 
                    key={agent.agentId}
                    className={`cursor-pointer transition-all ${
                      selectedAgent === agent.agentId ? 'ring-2 ring-blue-500' : ''
                    }`}
                    onClick={() => setSelectedAgent(agent.agentId)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{agent.agentName}</CardTitle>
                        <div className="flex items-center gap-2">
                          <Badge variant={agent.enabled ? 'default' : 'secondary'}>
                            {agent.enabled ? 'Active' : 'Disabled'}
                          </Badge>
                          <Badge variant="outline">Priority {agent.priority}</Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="font-medium">Primary Model:</span> {agent.primaryModel}
                        </div>
                        <div>
                          <span className="font-medium">Temperature:</span> {agent.temperature}
                        </div>
                        <div>
                          <span className="font-medium">Max Tokens:</span> {agent.maxTokens}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Agent Configuration */}
              {selectedAgent && (
                <div className="space-y-4">
                  <h2 className="text-xl font-semibold">Agent Configuration</h2>
                  <AgentConfigForm 
                    agent={agents.find(a => a.agentId === selectedAgent)!}
                    onUpdate={(updates) => handleAgentUpdate(selectedAgent, updates)}
                    onModelSelect={(modelId, type) => handleModelSelect(selectedAgent, modelId, type)}
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'models' && (
          <div>
            <h2 className="text-xl font-semibold mb-4">Available AI Models</h2>
            <AIModelSelector
              onModelSelect={(modelId) => console.log('Selected model:', modelId)}
              showPricing={true}
            />
          </div>
        )}

        {activeTab === 'providers' && (
          <div>
            <h2 className="text-xl font-semibold mb-4">Provider Configuration</h2>
            <ProviderConfig onConfigUpdate={loadData} />
          </div>
        )}

        {activeTab === 'usage' && (
          <div>
            <h2 className="text-xl font-semibold mb-4">Usage Analytics & Cost Management</h2>
            <UsageAnalytics stats={usageStats} />
          </div>
        )}
      </div>
    </div>
  )
}

interface AgentConfigFormProps {
  agent: AgentConfig
  onUpdate: (updates: Partial<AgentConfig>) => void
  onModelSelect: (modelId: string, type: 'primary' | 'fallback') => void
}

function AgentConfigForm({ agent, onUpdate, onModelSelect }: AgentConfigFormProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{agent.agentName} Configuration</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium">Temperature</label>
            <Input
              type="number"
              min="0"
              max="2"
              step="0.1"
              value={agent.temperature}
              onChange={(e) => onUpdate({ temperature: parseFloat(e.target.value) })}
            />
          </div>
          <div>
            <label className="text-sm font-medium">Max Tokens</label>
            <Input
              type="number"
              min="100"
              max="8192"
              value={agent.maxTokens}
              onChange={(e) => onUpdate({ maxTokens: parseInt(e.target.value) })}
            />
          </div>
        </div>

        <div>
          <label className="text-sm font-medium">System Prompt</label>
          <textarea
            className="w-full mt-1 p-2 border rounded-md"
            rows={4}
            value={agent.systemPrompt}
            onChange={(e) => onUpdate({ systemPrompt: e.target.value })}
          />
        </div>

        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={agent.enabled}
            onChange={(e) => onUpdate({ enabled: e.target.checked })}
          />
          <label className="text-sm font-medium">Enable Agent</label>
        </div>
      </CardContent>
    </Card>
  )
}

interface UsageAnalyticsProps {
  stats: UsageStats | null
}

function UsageAnalytics({ stats }: UsageAnalyticsProps) {
  if (!stats) return <div>No usage data available</div>

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Cost Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span>Total Spent</span>
              <span className="font-bold">${stats.totalCost.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Daily Limit</span>
              <span>$10.00</span>
            </div>
            <div className="flex justify-between">
              <span>Monthly Limit</span>
              <span>$200.00</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ width: `${(stats.totalCost / 10) * 100}%` }}
              ></div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span>Success Rate</span>
              <span className="font-bold text-green-600">{stats.successRate}%</span>
            </div>
            <div className="flex justify-between">
              <span>Avg Response Time</span>
              <span className="font-bold">{stats.averageResponseTime}s</span>
            </div>
            <div className="flex justify-between">
              <span>Total Requests</span>
              <span className="font-bold">{stats.totalRequests.toLocaleString()}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
