'use client'

'use client'

import { ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'

function BackButton() {
  const router = useRouter()
  return (
    <button
      onClick={() => router.back()}
      className="flex items-center space-x-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
    >
      <ArrowLeft className="w-5 h-5" />
      <span>Terug</span>
    </button>
  )
}

export default BackButton