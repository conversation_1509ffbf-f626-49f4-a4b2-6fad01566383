'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { 
  Plug, 
  Database, 
  Globe, 
  FolderOpen, 
  Code, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  Settings,
  Play,
  Square,
  TestTube,
  Download,
  Upload,
  Trash2
} from 'lucide-react'

interface MCPServerConfig {
  name: string
  command: string
  args: string[]
  env?: Record<string, string>
  enabled: boolean
  description: string
  category: 'database' | 'web' | 'filesystem' | 'api' | 'other'
  autoConnect: boolean
}

interface MCPTool {
  name: string
  description: string
  inputSchema: any
}

interface MCPResource {
  uri: string
  name: string
  description?: string
  mimeType?: string
}

export default function MCPDashboard() {
  const [servers, setServers] = useState<MCPServerConfig[]>([])
  const [connectionStatus, setConnectionStatus] = useState<Record<string, boolean>>({})
  const [tools, setTools] = useState<MCPTool[]>([])
  const [resources, setResources] = useState<MCPResource[]>([])
  const [selectedServer, setSelectedServer] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'servers' | 'tools' | 'resources' | 'config'>('servers')

  useEffect(() => {
    loadMCPData()
    const interval = setInterval(loadMCPData, 10000) // Update every 10 seconds
    return () => clearInterval(interval)
  }, [])

  const loadMCPData = async () => {
    try {
      // Mock data - would normally fetch from MCP config manager
      const mockServers: MCPServerConfig[] = [
        {
          name: 'supabase',
          command: 'npx',
          args: ['-y', '@supabase/mcp-server-supabase'],
          env: {
            SUPABASE_URL: 'https://zcyqjnbtojltgymtqjnp.supabase.co',
            SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
          },
          enabled: true,
          description: 'Supabase database integration for user management and data storage',
          category: 'database',
          autoConnect: true
        },
        {
          name: 'brave-search',
          command: 'npx',
          args: ['-y', '@modelcontextprotocol/server-brave-search'],
          env: {
            BRAVE_API_KEY: 'BSAfxV16je27QEThZotnM7IWlbWqwYR'
          },
          enabled: true,
          description: 'Brave Search API for web search and market research',
          category: 'web',
          autoConnect: true
        },
        {
          name: 'filesystem',
          command: 'npx',
          args: ['-y', '@modelcontextprotocol/server-filesystem', '/Users/<USER>/Desktop/cryptoagent-pro'],
          enabled: false,
          description: 'Local filesystem access for reading project files',
          category: 'filesystem',
          autoConnect: false
        }
      ]

      const mockConnectionStatus = {
        'supabase': true,
        'brave-search': true,
        'filesystem': false
      }

      const mockTools: MCPTool[] = [
        {
          name: 'supabase:query',
          description: 'Execute SQL queries on Supabase database',
          inputSchema: { type: 'object', properties: { query: { type: 'string' } } }
        },
        {
          name: 'brave-search:search',
          description: 'Search the web using Brave Search API',
          inputSchema: { type: 'object', properties: { query: { type: 'string' }, count: { type: 'number' } } }
        }
      ]

      const mockResources: MCPResource[] = [
        {
          uri: 'supabase://tables',
          name: 'Database Tables',
          description: 'List of all database tables',
          mimeType: 'application/json'
        },
        {
          uri: 'filesystem://project',
          name: 'Project Files',
          description: 'Project source code files',
          mimeType: 'text/plain'
        }
      ]

      setServers(mockServers)
      setConnectionStatus(mockConnectionStatus)
      setTools(mockTools)
      setResources(mockResources)
      setLoading(false)
    } catch (error) {
      console.error('Failed to load MCP data:', error)
      setLoading(false)
    }
  }

  const toggleServerConnection = async (serverName: string) => {
    const isConnected = connectionStatus[serverName]
    
    try {
      if (isConnected) {
        // Disconnect
        setConnectionStatus(prev => ({ ...prev, [serverName]: false }))
      } else {
        // Connect
        setConnectionStatus(prev => ({ ...prev, [serverName]: true }))
      }
    } catch (error) {
      console.error(`Failed to toggle connection for ${serverName}:`, error)
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'database':
        return <Database className="h-5 w-5" />
      case 'web':
        return <Globe className="h-5 w-5" />
      case 'filesystem':
        return <FolderOpen className="h-5 w-5" />
      case 'api':
        return <Code className="h-5 w-5" />
      default:
        return <Plug className="h-5 w-5" />
    }
  }

  const getStatusBadge = (serverName: string) => {
    const isConnected = connectionStatus[serverName]
    return (
      <Badge variant={isConnected ? 'default' : 'secondary'}>
        {isConnected ? 'Connected' : 'Disconnected'}
      </Badge>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64 text-white">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading MCP dashboard...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6 text-white">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">MCP Integration</h1>
          <p className="text-gray-600">Model Context Protocol servers and tools</p>
        </div>
        
        {/* Status Summary */}
        <div className="flex gap-4">
          <div className="flex items-center gap-2 text-green-600">
            <CheckCircle className="h-5 w-5" />
            <span className="text-sm">
              {Object.values(connectionStatus).filter(Boolean).length} Connected
            </span>
          </div>
          <div className="flex items-center gap-2 text-blue-600">
            <Plug className="h-5 w-5" />
            <span className="text-sm">{servers.length} Servers</span>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b">
        <nav className="flex space-x-8">
          {[
            { id: 'servers', label: 'Servers', icon: Plug },
            { id: 'tools', label: 'Tools', icon: Settings },
            { id: 'resources', label: 'Resources', icon: FolderOpen },
            { id: 'config', label: 'Configuration', icon: Code }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Icon className="h-4 w-4" />
              {label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'servers' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {servers.map((server) => (
              <Card 
                key={server.name}
                className={`cursor-pointer transition-all hover:shadow-lg ${
                  selectedServer === server.name 
                    ? 'ring-2 ring-blue-500 bg-blue-50' 
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setSelectedServer(server.name)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(server.category)}
                      <CardTitle className="text-lg">{server.name}</CardTitle>
                    </div>
                    {getStatusBadge(server.name)}
                  </div>
                  <div className="flex gap-2">
                    <Badge variant={server.enabled ? 'default' : 'secondary'}>
                      {server.enabled ? 'Enabled' : 'Disabled'}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {server.category}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent className="space-y-3">
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {server.description}
                  </p>

                  <div className="text-xs text-gray-500">
                    Command: {server.command} {server.args.join(' ')}
                  </div>

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant={connectionStatus[server.name] ? 'destructive' : 'default'}
                      onClick={(e) => {
                        e.stopPropagation()
                        toggleServerConnection(server.name)
                      }}
                      className="flex-1"
                    >
                      {connectionStatus[server.name] ? (
                        <>
                          <Square className="h-3 w-3 mr-1" />
                          Disconnect
                        </>
                      ) : (
                        <>
                          <Play className="h-3 w-3 mr-1" />
                          Connect
                        </>
                      )}
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation()
                        // Test connection
                      }}
                    >
                      <TestTube className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {activeTab === 'tools' && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Available Tools</h2>
              <Button variant="outline" onClick={loadMCPData}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {tools.map((tool) => (
                <Card key={tool.name}>
                  <CardHeader>
                    <CardTitle className="text-lg">{tool.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-3">{tool.description}</p>
                    <div className="text-xs text-gray-500">
                      <strong>Input Schema:</strong>
                      <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                        {JSON.stringify(tool.inputSchema, null, 2)}
                      </pre>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'resources' && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Available Resources</h2>
              <Button variant="outline" onClick={loadMCPData}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {resources.map((resource) => (
                <Card key={resource.uri}>
                  <CardHeader>
                    <CardTitle className="text-lg">{resource.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-2">{resource.description}</p>
                    <div className="text-xs text-gray-500">
                      <div><strong>URI:</strong> {resource.uri}</div>
                      {resource.mimeType && (
                        <div><strong>Type:</strong> {resource.mimeType}</div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'config' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">MCP Configuration</h2>
              <div className="flex gap-2">
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline">
                  <Upload className="h-4 w-4 mr-2" />
                  Import
                </Button>
                <Button variant="destructive">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Reset
                </Button>
              </div>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Global Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <input type="checkbox" id="autoConnect" defaultChecked />
                  <label htmlFor="autoConnect" className="text-sm font-medium">
                    Auto-connect to enabled servers on startup
                  </label>
                </div>
                
                <div className="flex items-center gap-2">
                  <input type="checkbox" id="retryConnection" defaultChecked />
                  <label htmlFor="retryConnection" className="text-sm font-medium">
                    Retry failed connections automatically
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Connection Timeout (seconds)</label>
                  <Input type="number" defaultValue="30" className="w-32" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
