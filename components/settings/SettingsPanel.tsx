'use client'

import { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON><PERSON>, 
  User, 
  Shield, 
  Bell, 
  Palette, 
  Database, 
  Key, 
  Globe,
  ChevronRight,
  LogOut,
  Moon,
  Sun,
  Wifi,
  WifiOff
} from 'lucide-react'
import { useAuth } from '../auth/AuthProvider'

interface SettingsSection {
  id: string
  title: string
  icon: React.ReactNode
  items: SettingsItem[]
}

interface SettingsItem {
  id: string
  title: string
  subtitle?: string
  type: 'toggle' | 'select' | 'input' | 'button' | 'link'
  value?: any
  options?: string[]
  action?: () => void
}

export default function SettingsPanel() {
  const { user, signOut } = useAuth()
  const [darkMode, setDarkMode] = useState(true)
  const [notifications, setNotifications] = useState(true)
  const [autoTrading, setAutoTrading] = useState(false)
  const [riskLevel, setRiskLevel] = useState('medium')
  const [selectedTheme, setSelectedTheme] = useState('dark')

  const settingsSections: SettingsSection[] = [
    {
      id: 'account',
      title: 'Account',
      icon: <User size={20} />,
      items: [
        {
          id: 'profile',
          title: 'Profile',
          subtitle: user?.email || 'Not signed in',
          type: 'link'
        },
        {
          id: 'organization',
          title: 'Organization',
          subtitle: 'CryptoAgent Pro',
          type: 'link'
        },
        {
          id: 'logout',
          title: 'Sign Out',
          type: 'button',
          action: signOut
        }
      ]
    },
    {
      id: 'trading',
      title: 'Trading',
      icon: <Database size={20} />,
      items: [
        {
          id: 'auto-trading',
          title: 'Auto Trading',
          subtitle: 'Enable autonomous trading',
          type: 'toggle',
          value: autoTrading,
          action: () => setAutoTrading(!autoTrading)
        },
        {
          id: 'risk-level',
          title: 'Risk Level',
          subtitle: 'Set trading risk tolerance',
          type: 'select',
          value: riskLevel,
          options: ['low', 'medium', 'high', 'aggressive']
        },
        {
          id: 'stop-loss',
          title: 'Stop Loss',
          subtitle: '5% default',
          type: 'input'
        },
        {
          id: 'position-size',
          title: 'Position Size',
          subtitle: '2% of portfolio',
          type: 'input'
        }
      ]
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: <Bell size={20} />,
      items: [
        {
          id: 'push-notifications',
          title: 'Push Notifications',
          subtitle: 'Get alerts on your device',
          type: 'toggle',
          value: notifications,
          action: () => setNotifications(!notifications)
        },
        {
          id: 'trade-alerts',
          title: 'Trade Alerts',
          subtitle: 'Notify on order execution',
          type: 'toggle',
          value: true
        },
        {
          id: 'price-alerts',
          title: 'Price Alerts',
          subtitle: 'Notify on price movements',
          type: 'toggle',
          value: false
        }
      ]
    },
    {
      id: 'appearance',
      title: 'Appearance',
      icon: <Palette size={20} />,
      items: [
        {
          id: 'dark-mode',
          title: 'Dark Mode',
          subtitle: 'Use dark theme',
          type: 'toggle',
          value: darkMode,
          action: () => setDarkMode(!darkMode)
        },
        {
          id: 'theme',
          title: 'Theme',
          subtitle: 'Choose app theme',
          type: 'select',
          value: selectedTheme,
          options: ['dark', 'light', 'auto']
        }
      ]
    },
    {
      id: 'security',
      title: 'Security',
      icon: <Shield size={20} />,
      items: [
        {
          id: 'two-factor',
          title: 'Two-Factor Authentication',
          subtitle: 'Add extra security',
          type: 'toggle',
          value: false
        },
        {
          id: 'biometric',
          title: 'Biometric Login',
          subtitle: 'Use fingerprint or face ID',
          type: 'toggle',
          value: true
        },
        {
          id: 'session-timeout',
          title: 'Session Timeout',
          subtitle: '30 minutes',
          type: 'select',
          options: ['15 minutes', '30 minutes', '1 hour', 'Never']
        }
      ]
    },
    {
      id: 'api',
      title: 'API Configuration',
      icon: <Key size={20} />,
      items: [
        {
          id: 'supabase-status',
          title: 'Supabase Connection',
          subtitle: 'Not configured',
          type: 'link'
        },
        {
          id: 'exchange-keys',
          title: 'Exchange API Keys',
          subtitle: 'Configure trading exchanges',
          type: 'link'
        },
        {
          id: 'ai-providers',
          title: 'AI Providers',
          subtitle: 'Configure AI models',
          type: 'link'
        }
      ]
    },
    {
      id: 'system',
      title: 'System',
      icon: <Settings size={20} />,
      items: [
        {
          id: 'connection-status',
          title: 'Connection Status',
          subtitle: 'Online',
          type: 'link'
        },
        {
          id: 'cache-clear',
          title: 'Clear Cache',
          subtitle: 'Free up storage',
          type: 'button'
        },
        {
          id: 'reset-settings',
          title: 'Reset Settings',
          subtitle: 'Restore defaults',
          type: 'button'
        }
      ]
    }
  ]

  const renderSettingItem = (item: SettingsItem) => {
    switch (item.type) {
      case 'toggle':
        return (
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-white font-medium">{item.title}</p>
              {item.subtitle && (
                <p className="text-gray-400 text-sm">{item.subtitle}</p>
              )}
            </div>
            <button
              onClick={item.action}
              className={`relative w-12 h-6 rounded-full transition-all duration-300 ${
                item.value ? 'bg-blue-500' : 'bg-gray-600'
              }`}
            >
              <div className={`absolute w-4 h-4 bg-white rounded-full top-1 transition-all duration-300 ${
                item.value ? 'left-7' : 'left-1'
              }`} />
            </button>
          </div>
        )

      case 'select':
        return (
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-white font-medium">{item.title}</p>
              {item.subtitle && (
                <p className="text-gray-400 text-sm">{item.subtitle}</p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-blue-400 text-sm capitalize">{item.value}</span>
              <ChevronRight size={16} className="text-gray-400" />
            </div>
          </div>
        )

      case 'input':
        return (
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-white font-medium">{item.title}</p>
              {item.subtitle && (
                <p className="text-gray-400 text-sm">{item.subtitle}</p>
              )}
            </div>
            <ChevronRight size={16} className="text-gray-400" />
          </div>
        )

      case 'button':
        return (
          <button
            onClick={item.action}
            className="w-full text-left"
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-white font-medium">{item.title}</p>
                {item.subtitle && (
                  <p className="text-gray-400 text-sm">{item.subtitle}</p>
                )}
              </div>
              <ChevronRight size={16} className="text-gray-400" />
            </div>
          </button>
        )

      case 'link':
      default:
        return (
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-white font-medium">{item.title}</p>
              {item.subtitle && (
                <p className="text-gray-400 text-sm">{item.subtitle}</p>
              )}
            </div>
            <ChevronRight size={16} className="text-gray-400" />
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <div className="bg-gray-900 p-4 flex items-center justify-between border-b border-gray-800">
        <div className="flex items-center space-x-3">
          <Settings size={24} />
          <h1 className="text-xl font-bold">Settings</h1>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
          <span className="text-green-400 text-xs font-mono">ONLINE</span>
        </div>
      </div>

      {/* Settings Content */}
      <div className="p-4 space-y-6">
        {settingsSections.map((section) => (
          <div key={section.id} className="space-y-3">
            {/* Section Header */}
            <div className="flex items-center space-x-2">
              <span className="text-gray-400">{section.icon}</span>
              <h2 className="text-lg font-semibold text-gray-300">{section.title}</h2>
            </div>

            {/* Section Items */}
            <div className="bg-gray-900 rounded-xl border border-gray-800 overflow-hidden">
              {section.items.map((item, index) => (
                <div
                  key={item.id}
                  className={`
                    p-4 border-b border-gray-800 last:border-b-0
                    ${item.type === 'button' && item.id === 'logout' ? 'text-red-400' : ''}
                  `}
                >
                  {renderSettingItem(item)}
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* App Version */}
        <div className="text-center pt-8">
          <p className="text-gray-500 text-sm">CryptoAgent Pro v0.1.0</p>
          <p className="text-gray-600 text-xs mt-1">Built with Next.js & Supabase</p>
        </div>
      </div>
    </div>
  )
} 