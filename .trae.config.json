{"version": "1.0", "name": "CryptoAgent Pro - Trae AI Assistant", "description": "Nederlandse AI coding assistent voor crypto trading app ontwikkeling", "ai": {"provider": "openai-compatible", "baseUrl": "http://localhost:1234/v1", "apiKey": "lm-studio", "models": {"default": "deepseek-coder-33b-instruct", "creative": "qwen/qwen2.5-coder-14b", "fast": "wizardlm-2-7b", "analysis": "microsoft/phi-4-mini-reasoning"}, "temperature": 0.3, "maxTokens": 4096, "timeout": 60000}, "language": "nl", "systemPrompt": "Je bent een Nederlandse AI coding assistent die helpt bij het ontwikkelen van een crypto trading applicatie. Je hebt expertise in TypeScript, React, Next.js, Supabase, en crypto trading strategieën. Antwoord altijd in het Nederlands en focus op praktische, werkende code.", "capabilities": {"codeGeneration": true, "codeReview": true, "debugging": true, "documentation": true, "testing": true, "refactoring": true, "architecture": true, "cryptoTrading": true, "apiIntegration": true}, "project": {"name": "cryptoagent-pro", "type": "next-js", "language": "typescript", "frameworks": ["next.js", "react", "supabase", "tailwind"], "apis": ["binance", "mexc", "kucoin", "bybit"], "specialization": "crypto-trading"}, "context": {"maxFiles": 50, "maxLines": 10000, "includePatterns": ["src/**/*.{ts,tsx,js,jsx}", "*.{md,json,yaml,yml}", "scripts/**/*.js", "components/**/*.{ts,tsx}", "lib/**/*.ts", "types/**/*.ts"], "excludePatterns": ["node_modules/**", ".next/**", "dist/**", "*.log", ".env*", "*.key"]}, "commands": {"generate": {"model": "default", "temperature": 0.3, "prompt": "Genereer code voor: {input}"}, "review": {"model": "analysis", "temperature": 0.2, "prompt": "Review deze code en geef feedback: {input}"}, "debug": {"model": "default", "temperature": 0.1, "prompt": "Debug dit probleem: {input}"}, "crypto": {"model": "creative", "temperature": 0.5, "prompt": "Help met crypto trading logica: {input}"}, "api": {"model": "default", "temperature": 0.2, "prompt": "Maak API integratie voor: {input}"}}, "shortcuts": {"Cmd+T": "generate", "Cmd+R": "review", "Cmd+D": "debug", "Cmd+C": "crypto", "Cmd+A": "api"}, "features": {"autoComplete": true, "inlineChat": true, "contextAware": true, "multiModel": true, "streaming": true, "caching": true}, "optimization": {"cacheResponses": true, "parallelRequests": false, "retryOnFailure": true, "maxRetries": 3, "fallbackModel": "fast"}, "privacy": {"localOnly": true, "noTelemetry": true, "encryptCache": false, "dataRetention": 7}}