#!/usr/bin/env node

/**
 * Market Data API Test Suite
 * Tests real-time market data functionality
 */

const ccxt = require('ccxt')
const dotenv = require('dotenv')
const path = require('path')

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') })

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

class MarketDataTest {
  constructor() {
    this.exchange = null
  }

  log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`)
  }

  async initializeExchange() {
    this.log('\n🔧 Initializing MEXC Exchange for Market Data...', colors.blue + colors.bright)
    
    try {
      this.exchange = new ccxt.mexc({
        apiKey: process.env.MEXC_API_KEY,
        secret: process.env.MEXC_SECRET_KEY,
        enableRateLimit: true,
        timeout: 30000
      })

      await this.exchange.loadMarkets()
      this.log('✅ MEXC Exchange initialized successfully', colors.green)
      this.log(`📊 Markets loaded: ${Object.keys(this.exchange.markets).length} pairs`, colors.green)
      
      return true
    } catch (error) {
      this.log(`❌ Failed to initialize exchange: ${error.message}`, colors.red)
      return false
    }
  }

  async testTickerData() {
    this.log('\n📈 Testing Ticker Data...', colors.cyan + colors.bright)
    
    const symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']
    
    for (const symbol of symbols) {
      try {
        if (!this.exchange.markets[symbol]) {
          this.log(`  ⚠️  ${symbol} not available`, colors.yellow)
          continue
        }

        const ticker = await this.exchange.fetchTicker(symbol)
        
        this.log(`  📊 ${symbol}:`, colors.green)
        this.log(`    Price: $${ticker.last?.toFixed(2)}`, colors.green)
        this.log(`    24h Change: ${ticker.percentage?.toFixed(2)}%`, 
          ticker.percentage >= 0 ? colors.green : colors.red)
        this.log(`    Volume: ${ticker.baseVolume?.toFixed(2)}`, colors.green)
        this.log(`    Bid/Ask: $${ticker.bid?.toFixed(2)} / $${ticker.ask?.toFixed(2)}`, colors.green)
        
        await new Promise(resolve => setTimeout(resolve, 500)) // Rate limiting
      } catch (error) {
        this.log(`  ❌ ${symbol} ticker failed: ${error.message}`, colors.red)
      }
    }
  }

  async testOrderbookData() {
    this.log('\n📖 Testing Orderbook Data...', colors.cyan + colors.bright)
    
    try {
      const symbol = 'BTC/USDT'
      const orderbook = await this.exchange.fetchOrderBook(symbol, 10)
      
      this.log(`  📊 ${symbol} Orderbook:`, colors.green)
      this.log(`    Timestamp: ${new Date(orderbook.timestamp).toISOString()}`, colors.green)
      
      this.log(`    Top 5 Bids:`, colors.green)
      orderbook.bids.slice(0, 5).forEach((bid, index) => {
        this.log(`      ${index + 1}. $${bid[0].toFixed(2)} × ${bid[1].toFixed(6)}`, colors.green)
      })
      
      this.log(`    Top 5 Asks:`, colors.green)
      orderbook.asks.slice(0, 5).forEach((ask, index) => {
        this.log(`      ${index + 1}. $${ask[0].toFixed(2)} × ${ask[1].toFixed(6)}`, colors.green)
      })
      
      const spread = orderbook.asks[0][0] - orderbook.bids[0][0]
      const spreadPercent = (spread / orderbook.bids[0][0]) * 100
      this.log(`    Spread: $${spread.toFixed(2)} (${spreadPercent.toFixed(4)}%)`, colors.yellow)
      
    } catch (error) {
      this.log(`  ❌ Orderbook test failed: ${error.message}`, colors.red)
    }
  }

  async testTradeData() {
    this.log('\n💱 Testing Recent Trades...', colors.cyan + colors.bright)
    
    try {
      const symbol = 'BTC/USDT'
      const trades = await this.exchange.fetchTrades(symbol, undefined, 10)
      
      this.log(`  📊 ${symbol} Recent Trades (${trades.length}):`, colors.green)
      
      trades.slice(0, 5).forEach((trade, index) => {
        const time = new Date(trade.timestamp).toLocaleTimeString()
        const side = trade.side === 'buy' ? '🟢' : '🔴'
        this.log(`    ${side} ${time}: $${trade.price?.toFixed(2)} × ${trade.amount?.toFixed(6)}`, colors.green)
      })
      
    } catch (error) {
      this.log(`  ❌ Trades test failed: ${error.message}`, colors.red)
    }
  }

  async testOHLCVData() {
    this.log('\n📊 Testing OHLCV Candle Data...', colors.cyan + colors.bright)
    
    try {
      const symbol = 'BTC/USDT'
      const timeframes = ['1m', '5m', '1h']
      
      for (const timeframe of timeframes) {
        const ohlcv = await this.exchange.fetchOHLCV(symbol, timeframe, undefined, 5)
        
        this.log(`  📈 ${symbol} ${timeframe} candles (${ohlcv.length}):`, colors.green)
        
        const latest = ohlcv[ohlcv.length - 1]
        if (latest) {
          const [timestamp, open, high, low, close, volume] = latest
          const time = new Date(timestamp).toLocaleTimeString()
          this.log(`    Latest: ${time} O:${open} H:${high} L:${low} C:${close} V:${volume?.toFixed(2)}`, colors.green)
        }
        
        await new Promise(resolve => setTimeout(resolve, 500)) // Rate limiting
      }
      
    } catch (error) {
      this.log(`  ❌ OHLCV test failed: ${error.message}`, colors.red)
    }
  }

  async testMarketList() {
    this.log('\n📋 Testing Market List...', colors.cyan + colors.bright)
    
    try {
      const markets = Object.values(this.exchange.markets)
      const activeMarkets = markets.filter(market => market.active)
      const spotMarkets = activeMarkets.filter(market => market.spot)
      const usdtMarkets = spotMarkets.filter(market => market.quote === 'USDT')
      
      this.log(`  📊 Market Statistics:`, colors.green)
      this.log(`    Total markets: ${markets.length}`, colors.green)
      this.log(`    Active markets: ${activeMarkets.length}`, colors.green)
      this.log(`    Spot markets: ${spotMarkets.length}`, colors.green)
      this.log(`    USDT pairs: ${usdtMarkets.length}`, colors.green)
      
      this.log(`  🔝 Top 10 USDT pairs:`, colors.green)
      usdtMarkets.slice(0, 10).forEach((market, index) => {
        this.log(`    ${index + 1}. ${market.symbol}`, colors.green)
      })
      
    } catch (error) {
      this.log(`  ❌ Market list test failed: ${error.message}`, colors.red)
    }
  }

  async runAllTests() {
    this.log('🚀 Starting Market Data API Tests', colors.magenta + colors.bright)
    this.log('=' * 50, colors.magenta)

    const initialized = await this.initializeExchange()
    if (!initialized) {
      this.log('\n❌ Cannot proceed without exchange connection', colors.red + colors.bright)
      return
    }

    await this.testTickerData()
    await this.testOrderbookData()
    await this.testTradeData()
    await this.testOHLCVData()
    await this.testMarketList()

    this.log('\n📊 MARKET DATA TEST SUMMARY', colors.magenta + colors.bright)
    this.log('=' * 50, colors.magenta)
    this.log('✅ All market data feeds tested successfully!', colors.green + colors.bright)
    this.log('🚀 Real-time data infrastructure is operational', colors.green)
    this.log('📈 Ready for live trading dashboard integration', colors.green)
  }
}

// Run the tests
if (require.main === module) {
  const tester = new MarketDataTest()
  tester.runAllTests().catch(error => {
    console.error('❌ Market data test failed:', error)
    process.exit(1)
  })
}

module.exports = MarketDataTest
