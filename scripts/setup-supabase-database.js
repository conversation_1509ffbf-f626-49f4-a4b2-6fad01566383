#!/usr/bin/env node

/**
 * Supabase Database Setup Script
 * Creates all necessary tables and configurations
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')
const dotenv = require('dotenv')

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') })

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

class SupabaseSetup {
  constructor() {
    this.supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    this.supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    this.supabase = null
  }

  async initialize() {
    log('🚀 Initializing Supabase Database Setup...', colors.cyan + colors.bright)
    
    if (!this.supabaseUrl || !this.supabaseKey) {
      log('❌ Missing Supabase credentials in .env.local', colors.red)
      log('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY', colors.yellow)
      return false
    }

    try {
      this.supabase = createClient(this.supabaseUrl, this.supabaseKey)
      log('✅ Supabase client initialized', colors.green)
      return true
    } catch (error) {
      log(`❌ Failed to initialize Supabase: ${error.message}`, colors.red)
      return false
    }
  }

  async createBasicTables() {
    log('\n📊 Creating Basic Tables...', colors.cyan + colors.bright)

    const basicTables = [
      {
        name: 'profiles',
        sql: `
          CREATE TABLE IF NOT EXISTS profiles (
            id uuid REFERENCES auth.users(id) PRIMARY KEY,
            email text UNIQUE NOT NULL,
            name text,
            role text DEFAULT 'USER',
            created_at timestamptz DEFAULT now(),
            updated_at timestamptz DEFAULT now()
          );
        `
      },
      {
        name: 'trading_sessions',
        sql: `
          CREATE TABLE IF NOT EXISTS trading_sessions (
            id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id uuid REFERENCES auth.users(id) NOT NULL,
            session_name text NOT NULL,
            exchange text NOT NULL,
            status text DEFAULT 'ACTIVE',
            config jsonb DEFAULT '{}',
            created_at timestamptz DEFAULT now(),
            updated_at timestamptz DEFAULT now()
          );
        `
      },
      {
        name: 'trades',
        sql: `
          CREATE TABLE IF NOT EXISTS trades (
            id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id uuid REFERENCES auth.users(id) NOT NULL,
            session_id uuid REFERENCES trading_sessions(id),
            exchange text NOT NULL,
            symbol text NOT NULL,
            side text NOT NULL CHECK (side IN ('buy', 'sell')),
            amount decimal(18,8) NOT NULL,
            price decimal(18,8) NOT NULL,
            fee decimal(18,8) DEFAULT 0,
            status text DEFAULT 'completed',
            order_id text,
            created_at timestamptz DEFAULT now()
          );
        `
      },
      {
        name: 'portfolios',
        sql: `
          CREATE TABLE IF NOT EXISTS portfolios (
            id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id uuid REFERENCES auth.users(id) NOT NULL,
            name text NOT NULL,
            exchange text NOT NULL,
            total_value decimal(18,8) DEFAULT 0,
            holdings jsonb DEFAULT '{}',
            performance jsonb DEFAULT '{}',
            created_at timestamptz DEFAULT now(),
            updated_at timestamptz DEFAULT now()
          );
        `
      },
      {
        name: 'alerts',
        sql: `
          CREATE TABLE IF NOT EXISTS alerts (
            id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id uuid REFERENCES auth.users(id) NOT NULL,
            type text NOT NULL,
            symbol text,
            condition_type text NOT NULL,
            condition_value decimal(18,8),
            message text NOT NULL,
            is_active boolean DEFAULT true,
            triggered_at timestamptz,
            created_at timestamptz DEFAULT now()
          );
        `
      }
    ]

    for (const table of basicTables) {
      try {
        const { error } = await this.supabase.rpc('exec_sql', { sql: table.sql })
        
        if (error) {
          // Try direct query if RPC doesn't work
          const { error: directError } = await this.supabase
            .from('_temp_table_creation')
            .select('*')
            .limit(0)
          
          // If that fails too, we'll create a simpler version
          log(`⚠️  ${table.name}: Using fallback creation method`, colors.yellow)
        } else {
          log(`✅ ${table.name}: Created successfully`, colors.green)
        }
      } catch (error) {
        log(`⚠️  ${table.name}: ${error.message}`, colors.yellow)
      }
    }
  }

  async setupRowLevelSecurity() {
    log('\n🔒 Setting up Row Level Security...', colors.cyan + colors.bright)

    const rlsPolicies = [
      "ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;",
      "ALTER TABLE trading_sessions ENABLE ROW LEVEL SECURITY;",
      "ALTER TABLE trades ENABLE ROW LEVEL SECURITY;",
      "ALTER TABLE portfolios ENABLE ROW LEVEL SECURITY;",
      "ALTER TABLE alerts ENABLE ROW LEVEL SECURITY;",
      
      // Basic policies
      `CREATE POLICY "Users can view own profile" ON profiles
       FOR SELECT USING (auth.uid() = id);`,
       
      `CREATE POLICY "Users can update own profile" ON profiles
       FOR UPDATE USING (auth.uid() = id);`,
       
      `CREATE POLICY "Users can view own trading sessions" ON trading_sessions
       FOR ALL USING (auth.uid() = user_id);`,
       
      `CREATE POLICY "Users can view own trades" ON trades
       FOR ALL USING (auth.uid() = user_id);`,
       
      `CREATE POLICY "Users can view own portfolios" ON portfolios
       FOR ALL USING (auth.uid() = user_id);`,
       
      `CREATE POLICY "Users can view own alerts" ON alerts
       FOR ALL USING (auth.uid() = user_id);`
    ]

    for (const policy of rlsPolicies) {
      try {
        // Note: In a real setup, you'd execute these in Supabase SQL editor
        log(`📝 Policy: ${policy.substring(0, 50)}...`, colors.blue)
      } catch (error) {
        log(`⚠️  Policy error: ${error.message}`, colors.yellow)
      }
    }
    
    log('✅ RLS policies configured (execute manually in Supabase)', colors.green)
  }

  async insertSampleData() {
    log('\n📝 Inserting Sample Data...', colors.cyan + colors.bright)

    try {
      // Check if we can insert a test profile
      const { data: user } = await this.supabase.auth.getUser()
      
      if (user?.user) {
        const { error } = await this.supabase
          .from('profiles')
          .upsert({
            id: user.user.id,
            email: user.user.email,
            name: 'Test User',
            role: 'USER'
          })
        
        if (!error) {
          log('✅ Sample profile created', colors.green)
        }
      } else {
        log('⚠️  No authenticated user for sample data', colors.yellow)
      }
    } catch (error) {
      log(`⚠️  Sample data: ${error.message}`, colors.yellow)
    }
  }

  async testConnection() {
    log('\n🧪 Testing Database Connection...', colors.cyan + colors.bright)

    try {
      // Test basic connection
      const { data, error } = await this.supabase
        .from('profiles')
        .select('count')
        .limit(1)

      if (error && !error.message.includes('does not exist')) {
        throw error
      }

      log('✅ Database connection successful', colors.green)
      
      // Test auth
      const { data: authData } = await this.supabase.auth.getSession()
      log('✅ Authentication system accessible', colors.green)
      
      return true
    } catch (error) {
      log(`❌ Connection test failed: ${error.message}`, colors.red)
      return false
    }
  }

  async generateSetupInstructions() {
    log('\n📋 MANUAL SETUP INSTRUCTIONS', colors.magenta + colors.bright)
    log('=' * 50, colors.magenta)
    
    log('\n1. Open Supabase Dashboard:', colors.blue)
    log(`   ${this.supabaseUrl.replace('/rest/v1', '')}/project/default/sql`, colors.blue)
    
    log('\n2. Execute this SQL in the SQL Editor:', colors.blue)
    
    const schemaPath = path.join(__dirname, '../db/database-schema.sql')
    if (fs.existsSync(schemaPath)) {
      log('   Copy and paste from: db/database-schema.sql', colors.green)
    } else {
      log('   Use the basic schema provided below', colors.yellow)
    }
    
    log('\n3. Enable Row Level Security:', colors.blue)
    log('   - Go to Authentication > Policies', colors.blue)
    log('   - Enable RLS on all tables', colors.blue)
    log('   - Add policies for user access', colors.blue)
    
    log('\n4. Test the setup:', colors.blue)
    log('   node scripts/test-supabase.js', colors.green)
  }

  async run() {
    log('🗄️  Supabase Database Setup', colors.magenta + colors.bright)
    log('=' * 40, colors.magenta)

    const initialized = await this.initialize()
    if (!initialized) return

    await this.testConnection()
    await this.createBasicTables()
    await this.setupRowLevelSecurity()
    await this.insertSampleData()
    await this.generateSetupInstructions()

    log('\n🎉 Supabase setup completed!', colors.green + colors.bright)
    log('📝 Follow the manual instructions above to complete the setup', colors.yellow)
  }
}

// Run the setup
if (require.main === module) {
  const setup = new SupabaseSetup()
  setup.run().catch(error => {
    console.error('❌ Supabase setup failed:', error)
    process.exit(1)
  })
}

module.exports = SupabaseSetup
