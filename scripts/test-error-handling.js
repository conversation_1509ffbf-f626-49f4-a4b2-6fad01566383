#!/usr/bin/env node

/**
 * Error Handling & Resilience Test Suite
 * Tests error handling, rate limiting, and recovery mechanisms
 */

const ccxt = require('ccxt')
const dotenv = require('dotenv')
const path = require('path')

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') })

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

class ErrorHandlingTest {
  constructor() {
    this.exchange = null
    this.testResults = {
      networkResilience: false,
      rateLimitHandling: false,
      invalidSymbolHandling: false,
      authErrorHandling: false,
      timeoutHandling: false,
      retryMechanism: false
    }
  }

  log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`)
  }

  async initializeExchange() {
    this.log('\n🔧 Initializing Exchange for Error Testing...', colors.blue + colors.bright)
    
    try {
      this.exchange = new ccxt.mexc({
        apiKey: process.env.MEXC_API_KEY,
        secret: process.env.MEXC_SECRET_KEY,
        enableRateLimit: true,
        timeout: 5000 // Short timeout for testing
      })

      await this.exchange.loadMarkets()
      this.log('✅ Exchange initialized for error testing', colors.green)
      return true
    } catch (error) {
      this.log(`❌ Failed to initialize exchange: ${error.message}`, colors.red)
      return false
    }
  }

  async testInvalidSymbolHandling() {
    this.log('\n🚫 Testing Invalid Symbol Handling...', colors.cyan + colors.bright)
    
    const invalidSymbols = ['INVALID/USDT', 'FAKE/PAIR', 'NONEXISTENT/BTC']
    
    for (const symbol of invalidSymbols) {
      try {
        await this.exchange.fetchTicker(symbol)
        this.log(`  ❌ ${symbol}: Should have failed but didn't`, colors.red)
      } catch (error) {
        this.log(`  ✅ ${symbol}: Correctly handled - ${error.message.substring(0, 50)}...`, colors.green)
        this.testResults.invalidSymbolHandling = true
      }
    }
  }

  async testTimeoutHandling() {
    this.log('\n⏱️  Testing Timeout Handling...', colors.cyan + colors.bright)
    
    try {
      // Create exchange with very short timeout
      const timeoutExchange = new ccxt.mexc({
        apiKey: process.env.MEXC_API_KEY,
        secret: process.env.MEXC_SECRET_KEY,
        timeout: 1 // 1ms timeout - should fail
      })

      await timeoutExchange.fetchTicker('BTC/USDT')
      this.log('  ❌ Timeout test failed - request should have timed out', colors.red)
    } catch (error) {
      if (error.message.includes('timeout') || error.message.includes('TIMEOUT')) {
        this.log('  ✅ Timeout correctly handled', colors.green)
        this.testResults.timeoutHandling = true
      } else {
        this.log(`  ⚠️  Different error occurred: ${error.message}`, colors.yellow)
      }
    }
  }

  async testRateLimitHandling() {
    this.log('\n🚦 Testing Rate Limit Handling...', colors.cyan + colors.bright)
    
    try {
      // Make rapid requests to trigger rate limiting
      const promises = []
      for (let i = 0; i < 20; i++) {
        promises.push(this.exchange.fetchTicker('BTC/USDT'))
      }
      
      await Promise.all(promises)
      this.log('  ⚠️  No rate limiting encountered (exchange may have high limits)', colors.yellow)
    } catch (error) {
      if (error.message.includes('rate') || error.message.includes('limit') || error.message.includes('429')) {
        this.log('  ✅ Rate limiting correctly detected and handled', colors.green)
        this.testResults.rateLimitHandling = true
      } else {
        this.log(`  ❌ Different error: ${error.message}`, colors.red)
      }
    }
  }

  async testAuthErrorHandling() {
    this.log('\n🔐 Testing Authentication Error Handling...', colors.cyan + colors.bright)
    
    try {
      // Create exchange with invalid credentials
      const invalidExchange = new ccxt.mexc({
        apiKey: 'invalid_key',
        secret: 'invalid_secret',
        enableRateLimit: true
      })

      await invalidExchange.fetchBalance()
      this.log('  ❌ Auth test failed - should have been rejected', colors.red)
    } catch (error) {
      if (error.message.includes('API') || error.message.includes('auth') || error.message.includes('key')) {
        this.log('  ✅ Authentication error correctly handled', colors.green)
        this.testResults.authErrorHandling = true
      } else {
        this.log(`  ⚠️  Different error: ${error.message}`, colors.yellow)
      }
    }
  }

  async testRetryMechanism() {
    this.log('\n🔄 Testing Retry Mechanism...', colors.cyan + colors.bright)
    
    let attempts = 0
    const maxRetries = 3
    
    const retryOperation = async () => {
      attempts++
      this.log(`    Attempt ${attempts}/${maxRetries}`, colors.yellow)
      
      if (attempts < 3) {
        throw new Error('Simulated failure')
      }
      
      return 'Success!'
    }

    try {
      const result = await this.retryWithBackoff(retryOperation, maxRetries)
      if (result === 'Success!' && attempts === 3) {
        this.log('  ✅ Retry mechanism working correctly', colors.green)
        this.testResults.retryMechanism = true
      }
    } catch (error) {
      this.log(`  ❌ Retry mechanism failed: ${error.message}`, colors.red)
    }
  }

  async retryWithBackoff(operation, maxRetries = 3, baseDelay = 100) {
    let lastError
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        if (attempt === maxRetries) {
          break
        }
        
        const delay = baseDelay * Math.pow(2, attempt - 1)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    
    throw lastError
  }

  async testNetworkResilience() {
    this.log('\n🌐 Testing Network Resilience...', colors.cyan + colors.bright)
    
    try {
      // Test with multiple rapid requests
      const startTime = Date.now()
      const results = await Promise.allSettled([
        this.exchange.fetchTicker('BTC/USDT'),
        this.exchange.fetchTicker('ETH/USDT'),
        this.exchange.fetchTicker('BNB/USDT')
      ])
      
      const successful = results.filter(r => r.status === 'fulfilled').length
      const failed = results.filter(r => r.status === 'rejected').length
      const duration = Date.now() - startTime
      
      this.log(`  📊 Results: ${successful} successful, ${failed} failed in ${duration}ms`, colors.green)
      
      if (successful >= 2) {
        this.log('  ✅ Network resilience test passed', colors.green)
        this.testResults.networkResilience = true
      } else {
        this.log('  ❌ Network resilience test failed', colors.red)
      }
      
    } catch (error) {
      this.log(`  ❌ Network test error: ${error.message}`, colors.red)
    }
  }

  async testErrorRecovery() {
    this.log('\n🔧 Testing Error Recovery...', colors.cyan + colors.bright)
    
    try {
      // Simulate error and recovery
      let errorOccurred = false
      
      try {
        await this.exchange.fetchTicker('INVALID/SYMBOL')
      } catch (error) {
        errorOccurred = true
        this.log('  📝 Error caught, attempting recovery...', colors.yellow)
      }
      
      if (errorOccurred) {
        // Try valid operation after error
        const ticker = await this.exchange.fetchTicker('BTC/USDT')
        if (ticker && ticker.last) {
          this.log('  ✅ Successfully recovered after error', colors.green)
          return true
        }
      }
      
    } catch (error) {
      this.log(`  ❌ Recovery failed: ${error.message}`, colors.red)
    }
    
    return false
  }

  generateErrorHandlingReport() {
    this.log('\n📊 ERROR HANDLING TEST REPORT', colors.magenta + colors.bright)
    this.log('=' * 50, colors.magenta)

    const tests = [
      { name: 'Network Resilience', result: this.testResults.networkResilience },
      { name: 'Rate Limit Handling', result: this.testResults.rateLimitHandling },
      { name: 'Invalid Symbol Handling', result: this.testResults.invalidSymbolHandling },
      { name: 'Auth Error Handling', result: this.testResults.authErrorHandling },
      { name: 'Timeout Handling', result: this.testResults.timeoutHandling },
      { name: 'Retry Mechanism', result: this.testResults.retryMechanism }
    ]

    const passed = tests.filter(t => t.result).length
    const total = tests.length

    this.log(`\n🎯 Overall Score: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`, 
      passed === total ? colors.green + colors.bright : colors.yellow + colors.bright)

    tests.forEach(test => {
      const status = test.result ? '✅' : '❌'
      this.log(`   ${status} ${test.name}`, test.result ? colors.green : colors.red)
    })

    this.log('\n🚀 Recommendations:', colors.blue + colors.bright)
    if (passed === total) {
      this.log('   ✅ Error handling is robust and production-ready!', colors.green)
    } else {
      this.log('   🔧 Implement missing error handling mechanisms', colors.yellow)
      this.log('   📊 Add monitoring and alerting for failed operations', colors.yellow)
      this.log('   🔄 Enhance retry logic with exponential backoff', colors.yellow)
    }
  }

  async runAllTests() {
    this.log('🚀 Starting Error Handling & Resilience Tests', colors.magenta + colors.bright)
    this.log('=' * 60, colors.magenta)

    const initialized = await this.initializeExchange()
    if (!initialized) {
      this.log('\n❌ Cannot proceed without exchange connection', colors.red + colors.bright)
      return
    }

    await this.testInvalidSymbolHandling()
    await this.testTimeoutHandling()
    await this.testRateLimitHandling()
    await this.testAuthErrorHandling()
    await this.testRetryMechanism()
    await this.testNetworkResilience()
    
    const recoveryWorked = await this.testErrorRecovery()
    if (recoveryWorked) {
      this.testResults.networkResilience = true
    }

    this.generateErrorHandlingReport()
  }
}

// Run the tests
if (require.main === module) {
  const tester = new ErrorHandlingTest()
  tester.runAllTests().catch(error => {
    console.error('❌ Error handling test failed:', error)
    process.exit(1)
  })
}

module.exports = ErrorHandlingTest
