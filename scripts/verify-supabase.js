#!/usr/bin/env node

/**
 * CryptoAgent Pro - Supabase Setup Verificatie
 * Controleert of je Supabase database correct is ingesteld
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')
const dotenv = require('dotenv')

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') })

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

class SupabaseVerification {
  constructor() {
    this.supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    this.supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    this.supabase = null
    this.results = {
      credentials: false,
      connection: false,
      tables: {},
      policies: {},
      functions: {},
      overall: false
    }
  }

  async verifyCredentials() {
    log('\n🔐 STAP 1: Credentials Verificatie', colors.cyan + colors.bright)
    log('=' * 40, colors.cyan)

    // Check .env.local file
    const envPath = path.join(__dirname, '../.env.local')
    if (!fs.existsSync(envPath)) {
      log('❌ .env.local bestand niet gevonden', colors.red)
      return false
    }
    log('✅ .env.local bestand gevonden', colors.green)

    // Check URLs
    if (!this.supabaseUrl) {
      log('❌ NEXT_PUBLIC_SUPABASE_URL ontbreekt', colors.red)
      return false
    }
    
    if (!this.supabaseUrl.includes('supabase.co')) {
      log('❌ Supabase URL format lijkt incorrect', colors.red)
      log(`   Gevonden: ${this.supabaseUrl}`, colors.yellow)
      return false
    }
    log(`✅ Supabase URL correct: ${this.supabaseUrl.substring(0, 30)}...`, colors.green)

    // Check keys
    if (!this.supabaseKey) {
      log('❌ Supabase key ontbreekt', colors.red)
      log('   Zorg dat je SUPABASE_SERVICE_ROLE_KEY hebt ingesteld', colors.yellow)
      return false
    }
    
    const keyLength = this.supabaseKey.length
    if (keyLength < 100) {
      log('⚠️  Supabase key lijkt te kort (mogelijk anon key i.p.v. service role key)', colors.yellow)
    } else {
      log('✅ Supabase key correct formaat', colors.green)
    }

    this.results.credentials = true
    return true
  }

  async verifyConnection() {
    log('\n🔌 STAP 2: Database Connectie', colors.cyan + colors.bright)
    log('=' * 30, colors.cyan)

    try {
      this.supabase = createClient(this.supabaseUrl, this.supabaseKey, {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      })

      // Test basic connection
      const { data, error } = await this.supabase
        .rpc('version')
        .limit(1)

      if (error && error.code !== 'PGRST202') {
        // Try alternative connection test
        const { data: testData, error: testError } = await this.supabase
          .from('pg_tables')
          .select('count')
          .limit(1)

        if (testError) {
          throw testError
        }
      }

      log('✅ Database connectie succesvol', colors.green)
      this.results.connection = true
      return true

    } catch (error) {
      log(`❌ Connectie gefaald: ${error.message}`, colors.red)
      
      if (error.message.includes('Invalid API key')) {
        log('💡 Probeer je Service Role Key i.p.v. Anon Key', colors.yellow)
      }
      
      return false
    }
  }

  async verifyTables() {
    log('\n📊 STAP 3: Database Tabellen', colors.cyan + colors.bright)
    log('=' * 30, colors.cyan)

    const requiredTables = [
      'profiles',
      'trading_sessions', 
      'trades',
      'portfolios',
      'alerts'
    ]

    const optionalTables = [
      'portfolio_holdings',
      'market_data',
      'ai_interactions',
      'trading_strategies'
    ]

    let allRequiredExist = true

    // Check required tables
    log('\n📋 Vereiste tabellen:', colors.blue)
    for (const table of requiredTables) {
      try {
        const { data, error } = await this.supabase
          .from(table)
          .select('count')
          .limit(0)

        if (error && error.code === 'PGRST116') {
          log(`❌ ${table} - Ontbreekt`, colors.red)
          this.results.tables[table] = false
          allRequiredExist = false
        } else if (error) {
          log(`⚠️  ${table} - Error: ${error.message}`, colors.yellow)
          this.results.tables[table] = false
        } else {
          log(`✅ ${table} - OK`, colors.green)
          this.results.tables[table] = true
        }
      } catch (error) {
        log(`❌ ${table} - Exception: ${error.message}`, colors.red)
        this.results.tables[table] = false
        allRequiredExist = false
      }
    }

    // Check optional tables
    log('\n📋 Optionele tabellen:', colors.blue)
    for (const table of optionalTables) {
      try {
        const { data, error } = await this.supabase
          .from(table)
          .select('count')
          .limit(0)

        if (error && error.code === 'PGRST116') {
          log(`⚪ ${table} - Niet aanwezig (optioneel)`, colors.yellow)
          this.results.tables[table] = false
        } else if (error) {
          log(`⚠️  ${table} - Error: ${error.message}`, colors.yellow)
          this.results.tables[table] = false
        } else {
          log(`✅ ${table} - OK`, colors.green)
          this.results.tables[table] = true
        }
      } catch (error) {
        log(`⚪ ${table} - Niet beschikbaar`, colors.yellow)
        this.results.tables[table] = false
      }
    }

    return allRequiredExist
  }

  generateReport() {
    log('\n📋 VERIFICATIE RAPPORT', colors.magenta + colors.bright)
    log('=' * 50, colors.magenta)

    // Overall status
    const requiredTablesOK = ['profiles', 'trading_sessions', 'trades', 'portfolios', 'alerts']
      .every(table => this.results.tables[table])

    this.results.overall = this.results.credentials && 
                          this.results.connection && 
                          requiredTablesOK

    if (this.results.overall) {
      log('\n🎉 OVERALL STATUS: SUCCESVOL ✅', colors.green + colors.bright)
      log('Je Supabase database is correct ingesteld!', colors.green)
    } else {
      log('\n⚠️  OVERALL STATUS: INCOMPLETE ⚠️', colors.yellow + colors.bright)
      log('Er zijn nog enkele stappen nodig...', colors.yellow)
    }

    // Detailed status
    log('\n📊 DETAILED STATUS:', colors.blue + colors.bright)
    log(`Credentials: ${this.results.credentials ? '✅' : '❌'}`, colors.blue)
    log(`Connection: ${this.results.connection ? '✅' : '❌'}`, colors.blue)
    
    log('\nTabellen:', colors.blue)
    Object.entries(this.results.tables).forEach(([table, status]) => {
      const required = ['profiles', 'trading_sessions', 'trades', 'portfolios', 'alerts'].includes(table)
      const symbol = status ? '✅' : (required ? '❌' : '⚪')
      log(`  ${table}: ${symbol}`, colors.blue)
    })

    // Next steps
    this.generateNextSteps()
  }

  generateNextSteps() {
    log('\n🚀 VOLGENDE STAPPEN:', colors.cyan + colors.bright)

    if (!this.results.credentials) {
      log('1. ❌ Fix credentials in .env.local', colors.red)
      log('   - Voeg NEXT_PUBLIC_SUPABASE_URL toe', colors.yellow)
      log('   - Voeg SUPABASE_SERVICE_ROLE_KEY toe', colors.yellow)
    }

    if (!this.results.connection) {
      log('2. ❌ Fix database connectie', colors.red)
      log('   - Check Supabase URL format', colors.yellow)
      log('   - Gebruik Service Role Key i.p.v. Anon Key', colors.yellow)
    }

    const missingTables = Object.entries(this.results.tables)
      .filter(([table, exists]) => !exists && 
        ['profiles', 'trading_sessions', 'trades', 'portfolios', 'alerts'].includes(table))
      .map(([table]) => table)

    if (missingTables.length > 0) {
      log('3. ❌ Maak ontbrekende tabellen aan', colors.red)
      log('   - Ga naar Supabase Dashboard → SQL Editor', colors.yellow)
      log('   - Voer het database schema uit (zie artifacts)', colors.yellow)
      log(`   - Ontbrekende tabellen: ${missingTables.join(', ')}`, colors.yellow)
    }

    if (this.results.overall) {
      log('\n✅ DATABASE SETUP COMPLEET!', colors.green + colors.bright)
      log('\n📝 VOLGENDE FASE - CryptoAgent Pro Development:', colors.blue + colors.bright)
      log('1. Frontend componenten ontwikkelen', colors.blue)
      log('2. API routes implementeren', colors.blue)
      log('3. LM Studio integratie toevoegen', colors.blue)
      log('4. Authentication implementeren', colors.blue)
      log('5. Trading functionaliteit bouwen', colors.blue)
    }
  }

  async run() {
    log('🔍 CryptoAgent Pro - Supabase Verificatie', colors.magenta + colors.bright)
    log('=' * 50, colors.magenta)

    const credentialsOK = await this.verifyCredentials()
    if (!credentialsOK) {
      this.generateReport()
      return
    }

    const connectionOK = await this.verifyConnection()
    if (!connectionOK) {
      this.generateReport()
      return
    }

    await this.verifyTables()
    this.generateReport()
  }
}

// Run the verification
if (require.main === module) {
  const verification = new SupabaseVerification()
  verification.run().catch(error => {
    console.error('❌ Verificatie gefaald:', error)
    process.exit(1)
  })
}

module.exports = SupabaseVerification