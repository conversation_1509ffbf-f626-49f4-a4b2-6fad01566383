#!/usr/bin/env node

/**
 * Environment Setup Script voor CryptoAgent Pro
 * Maakt .env bestand aan en configureert alle benodigde variabelen
 */

const fs = require('fs');
const path = require('path');

const envVariables = [
  {
    name: 'NEXT_PUBLIC_SUPABASE_URL',
    description: 'Supabase project URL',
    example: 'https://your-project.supabase.co',
    required: true
  },
  {
    name: 'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    description: 'Supabase anonymous key',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: true
  },
  {
    name: 'SUPABASE_SERVICE_ROLE_KEY',
    description: 'Supabase service role key',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: true
  },
  {
    name: 'ENABLE_AUTO_MODE',
    description: 'Enable auto trading mode',
    example: 'false',
    required: false,
    default: 'false'
  },
  {
    name: 'DEFAULT_EXCHANGE',
    description: 'Default trading exchange',
    example: 'binance',
    required: false,
    default: 'binance'
  },
  {
    name: 'SANDBOX_MODE',
    description: 'Enable sandbox mode for testing',
    example: 'true',
    required: false,
    default: 'true'
  },
  {
    name: 'OPENAI_API_KEY',
    description: 'OpenAI API key (optional)',
    example: 'sk-...',
    required: false
  },
  {
    name: 'ANTHROPIC_API_KEY',
    description: 'Anthropic API key (optional)',
    example: 'sk-ant-...',
    required: false
  },
  {
    name: 'OPENROUTER_API_KEY',
    description: 'OpenRouter API key (optional)',
    example: 'sk-or-...',
    required: false
  },
  {
    name: 'LM_STUDIO_BASE_URL',
    description: 'LM Studio local server URL',
    example: 'http://************:1235/v1',
    required: false,
    default: 'http://************:1235/v1'
  },
  {
    name: 'LM_STUDIO_MODEL',
    description: 'LM Studio default model',
    example: 'deepseek-coder-33b-instruct',
    required: false,
    default: 'deepseek-coder-33b-instruct'
  },
  {
    name: 'BINANCE_API_KEY',
    description: 'Binance API key (for production)',
    example: 'your_binance_api_key',
    required: false
  },
  {
    name: 'BINANCE_SECRET_KEY',
    description: 'Binance secret key (for production)',
    example: 'your_binance_secret_key',
    required: false
  },
  {
    name: 'MEXC_API_KEY',
    description: 'MEXC API key (for production)',
    example: 'your_mexc_api_key',
    required: false
  },
  {
    name: 'MEXC_SECRET_KEY',
    description: 'MEXC secret key (for production)',
    example: 'your_mexc_secret_key',
    required: false
  },
  {
    name: 'KUCOIN_API_KEY',
    description: 'KuCoin API key (for production)',
    example: 'your_kucoin_api_key',
    required: false
  },
  {
    name: 'KUCOIN_SECRET_KEY',
    description: 'KuCoin secret key (for production)',
    example: 'your_kucoin_secret_key',
    required: false
  },
  {
    name: 'KUCOIN_PASSPHRASE',
    description: 'KuCoin passphrase (for production)',
    example: 'your_kucoin_passphrase',
    required: false
  },
  {
    name: 'BYBIT_API_KEY',
    description: 'Bybit API key (for production)',
    example: 'your_bybit_api_key',
    required: false
  },
  {
    name: 'BYBIT_SECRET_KEY',
    description: 'Bybit secret key (for production)',
    example: 'your_bybit_secret_key',
    required: false
  }
];

function createEnvFile() {
  console.log('🔧 CryptoAgent Pro Environment Setup\n');
  
  const envPath = path.join(process.cwd(), '.env');
  
  // Check if .env already exists
  if (fs.existsSync(envPath)) {
    console.log('⚠️  .env bestand bestaat al!');
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    rl.question('Wil je het overschrijven? (y/N): ', (answer) => {
      if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
        generateEnvFile(envPath);
      } else {
        console.log('❌ Setup geannuleerd');
        rl.close();
      }
    });
  } else {
    generateEnvFile(envPath);
  }
}

function generateEnvFile(envPath) {
  let envContent = '# CryptoAgent Pro Environment Variables\n\n';
  
  // Add comments and variables
  envContent += '# Supabase Configuration\n';
  envContent += 'NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here\n';
  envContent += 'NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here\n';
  envContent += 'SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here\n\n';
  
  envContent += '# Trading Configuration\n';
  envContent += 'ENABLE_AUTO_MODE=false\n';
  envContent += 'DEFAULT_EXCHANGE=binance\n';
  envContent += 'SANDBOX_MODE=true\n\n';
  
  envContent += '# AI Configuration\n';
  envContent += 'OPENAI_API_KEY=your_openai_key_here\n';
  envContent += 'ANTHROPIC_API_KEY=your_anthropic_key_here\n';
  envContent += 'OPENROUTER_API_KEY=your_openrouter_key_here\n\n';
  
  envContent += '# LM Studio Configuration (for local AI)\n';
  envContent += 'LM_STUDIO_BASE_URL=http://************:1235/v1\n';
  envContent += 'LM_STUDIO_MODEL=deepseek-coder-33b-instruct\n\n';
  
  envContent += '# Exchange API Keys (for production)\n';
  envContent += 'BINANCE_API_KEY=your_binance_api_key\n';
  envContent += 'BINANCE_SECRET_KEY=your_binance_secret_key\n';
  envContent += 'MEXC_API_KEY=your_mexc_api_key\n';
  envContent += 'MEXC_SECRET_KEY=your_mexc_secret_key\n';
  envContent += 'KUCOIN_API_KEY=your_kucoin_api_key\n';
  envContent += 'KUCOIN_SECRET_KEY=your_kucoin_secret_key\n';
  envContent += 'KUCOIN_PASSPHRASE=your_kucoin_passphrase\n';
  envContent += 'BYBIT_API_KEY=your_bybit_api_key\n';
  envContent += 'BYBIT_SECRET_KEY=your_bybit_secret_key\n';
  
  // Write to file
  fs.writeFileSync(envPath, envContent);
  
  console.log('✅ .env bestand aangemaakt!');
  console.log('\n📋 Volgende stappen:');
  console.log('1. Edit .env met je Supabase credentials');
  console.log('2. Configureer AI providers (optioneel)');
  console.log('3. Start de development server: npm run dev');
  console.log('\n🔗 Supabase Setup:');
  console.log('1. Ga naar https://supabase.com');
  console.log('2. Maak een nieuw project');
  console.log('3. Kopieer de URL en keys naar .env');
  console.log('4. Voer database script uit in Supabase SQL Editor');
  
  // Show database setup instructions
  console.log('\n🗄️  Database Setup:');
  console.log('1. Ga naar je Supabase project');
  console.log('2. Open SQL Editor');
  console.log('3. Voer database-schema.sql uit');
  
  console.log('\n🧪 Test de setup:');
  console.log('1. npm run dev');
  console.log('2. Ga naar http://localhost:3000');
  console.log('3. Test de AI configuratie: /ai-config');
}

// Run setup
createEnvFile(); 