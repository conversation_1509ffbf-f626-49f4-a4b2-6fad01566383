-- CryptoAgent Pro Database Setup Script
-- Voer dit uit in Supabase SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Organizations (multi-tenant)
CREATE TABLE IF NOT EXISTS organizations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  slug text UNIQUE NOT NULL,
  plan text DEFAULT 'FREE' CHECK (plan IN ('FREE', 'PRO', 'ENTERPRISE')),
  status text DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'SUSPENDED', 'CANCELLED')),
  owner_id uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Profiles (extends auth.users)
CREATE TABLE IF NOT EXISTS profiles (
  id uuid REFERENCES auth.users(id) PRIMARY KEY,
  email text UNIQUE NOT NULL,
  name text,
  organization_id uuid REFERENCES organizations(id),
  role text DEFAULT 'USER' CHECK (role IN ('OWNER', 'ADMIN', 'USER', 'VIEWER')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Trading Agents
CREATE TABLE IF NOT EXISTS trading_agents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  specialization text NOT NULL CHECK (specialization IN ('technical_analysis', 'sentiment_analysis', 'risk_management', 'portfolio_optimization', 'market_making')),
  ai_provider text NOT NULL,
  ai_model text NOT NULL,
  status text DEFAULT 'IDLE' CHECK (status IN ('ACTIVE', 'THINKING', 'ERROR', 'IDLE', 'TRADING', 'PAUSED')),
  performance_score decimal(5,2) DEFAULT 0,
  current_task text,
  last_activity timestamptz DEFAULT now(),
  config jsonb DEFAULT '{}',
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- AI Model Configurations
CREATE TABLE IF NOT EXISTS ai_models (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  provider text NOT NULL,
  model_name text NOT NULL,
  api_key text NOT NULL,
  endpoint text NOT NULL,
  specializations text[] DEFAULT '{}',
  cost_per_token decimal(10,8) DEFAULT 0,
  max_tokens integer DEFAULT 4096,
  temperature decimal(3,2) DEFAULT 0.7,
  is_active boolean DEFAULT true,
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Exchange Configurations
CREATE TABLE IF NOT EXISTS exchange_configs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  exchange_name text NOT NULL,
  api_key text NOT NULL,
  api_secret text NOT NULL,
  passphrase text,
  sandbox_mode boolean DEFAULT true,
  is_active boolean DEFAULT true,
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Trading Positions
CREATE TABLE IF NOT EXISTS trading_positions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id uuid REFERENCES trading_agents(id) NOT NULL,
  exchange text NOT NULL,
  symbol text NOT NULL,
  side text NOT NULL CHECK (side IN ('BUY', 'SELL')),
  size decimal(18,8) NOT NULL,
  entry_price decimal(18,8) NOT NULL,
  current_price decimal(18,8),
  pnl decimal(18,8) DEFAULT 0,
  pnl_percentage decimal(8,4) DEFAULT 0,
  status text DEFAULT 'OPEN' CHECK (status IN ('OPEN', 'CLOSED', 'CANCELLED', 'PENDING')),
  opened_at timestamptz DEFAULT now(),
  closed_at timestamptz,
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- AI Analysis Results
CREATE TABLE IF NOT EXISTS ai_analysis (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id uuid REFERENCES trading_agents(id) NOT NULL,
  analysis_type text NOT NULL,
  symbol text NOT NULL,
  timeframe text NOT NULL,
  raw_data jsonb DEFAULT '{}',
  analysis_result jsonb DEFAULT '{}',
  confidence_score decimal(5,2) DEFAULT 0,
  recommendation text CHECK (recommendation IN ('STRONG_BUY', 'BUY', 'NEUTRAL', 'SELL', 'STRONG_SELL')),
  ai_model_used text NOT NULL,
  tokens_used integer DEFAULT 0,
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Trading Signals
CREATE TABLE IF NOT EXISTS trading_signals (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id uuid REFERENCES trading_agents(id) NOT NULL,
  symbol text NOT NULL,
  signal_type text NOT NULL CHECK (signal_type IN ('BUY', 'SELL', 'HOLD')),
  strength decimal(5,2) NOT NULL,
  price_target decimal(18,8),
  stop_loss decimal(18,8),
  reasoning text NOT NULL,
  expires_at timestamptz,
  executed boolean DEFAULT false,
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- System Logs
CREATE TABLE IF NOT EXISTS trading_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id uuid REFERENCES trading_agents(id),
  log_level text DEFAULT 'INFO' CHECK (log_level IN ('DEBUG', 'INFO', 'WARN', 'ERROR', 'CRITICAL')),
  message text NOT NULL,
  metadata jsonb DEFAULT '{}',
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Portfolio Snapshots
CREATE TABLE IF NOT EXISTS portfolio_snapshots (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  total_value decimal(18,8) NOT NULL,
  total_pnl decimal(18,8) NOT NULL,
  currency text DEFAULT 'USD',
  positions_count integer DEFAULT 0,
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_trading_agents_org_id ON trading_agents(organization_id);
CREATE INDEX IF NOT EXISTS idx_trading_positions_org_id ON trading_positions(organization_id);
CREATE INDEX IF NOT EXISTS idx_trading_positions_status ON trading_positions(status);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_org_id ON ai_analysis(organization_id);
CREATE INDEX IF NOT EXISTS idx_trading_signals_org_id ON trading_signals(organization_id);
CREATE INDEX IF NOT EXISTS idx_trading_logs_org_id ON trading_logs(organization_id);
CREATE INDEX IF NOT EXISTS idx_profiles_org_id ON profiles(organization_id);

-- Enable Row Level Security (RLS)
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE trading_agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE exchange_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE trading_positions ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE trading_signals ENABLE ROW LEVEL SECURITY;
ALTER TABLE trading_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolio_snapshots ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can access their organization data" ON trading_agents
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their organization ai_models" ON ai_models
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their organization exchanges" ON exchange_configs
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their organization positions" ON trading_positions
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their organization analysis" ON ai_analysis
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their organization signals" ON trading_signals
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their organization logs" ON trading_logs
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their organization snapshots" ON portfolio_snapshots
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their own profile" ON profiles
FOR ALL USING (id = auth.uid());

CREATE POLICY "Users can access their organization" ON organizations
FOR ALL USING (id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_trading_agents_updated_at BEFORE UPDATE ON trading_agents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_models_updated_at BEFORE UPDATE ON ai_models FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_exchange_configs_updated_at BEFORE UPDATE ON exchange_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_trading_positions_updated_at BEFORE UPDATE ON trading_positions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_trading_signals_updated_at BEFORE UPDATE ON trading_signals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default organization for testing (if not exists)
INSERT INTO organizations (name, slug, plan, status) 
VALUES ('CryptoAgent Pro Demo', 'cryptoagent-pro-demo', 'FREE', 'ACTIVE')
ON CONFLICT (slug) DO NOTHING;

-- Insert sample trading agents for demo organization
INSERT INTO trading_agents (name, specialization, ai_provider, ai_model, status, performance_score, organization_id)
SELECT 
  'Technical Analyst',
  'technical_analysis',
  'openrouter',
  'meta-llama/llama-3.1-405b-instruct',
  'IDLE',
  85,
  id
FROM organizations WHERE slug = 'cryptoagent-pro-demo'
ON CONFLICT DO NOTHING;

INSERT INTO trading_agents (name, specialization, ai_provider, ai_model, status, performance_score, organization_id)
SELECT 
  'Sentiment Monitor',
  'sentiment_analysis',
  'glm',
  'glm-4-plus',
  'IDLE',
  78,
  id
FROM organizations WHERE slug = 'cryptoagent-pro-demo'
ON CONFLICT DO NOTHING;

INSERT INTO trading_agents (name, specialization, ai_provider, ai_model, status, performance_score, organization_id)
SELECT 
  'Risk Manager',
  'risk_management',
  'anthropic',
  'claude-3-sonnet',
  'IDLE',
  92,
  id
FROM organizations WHERE slug = 'cryptoagent-pro-demo'
ON CONFLICT DO NOTHING;

-- Insert sample portfolio snapshot
INSERT INTO portfolio_snapshots (total_value, total_pnl, currency, positions_count, organization_id)
SELECT 
  52840.50,
  1240.75,
  'USD',
  2,
  id
FROM organizations WHERE slug = 'cryptoagent-pro-demo'
ON CONFLICT DO NOTHING; 