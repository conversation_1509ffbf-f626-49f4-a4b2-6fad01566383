#!/usr/bin/env node

/**
 * CryptoAgent Pro - API Key Setup Script
 * 
 * This script helps you configure API keys for all supported exchanges
 * and test the connections before starting live trading.
 */

const fs = require('fs')
const path = require('path')
const readline = require('readline')
const crypto = require('crypto')

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

class APISetupManager {
  constructor() {
    this.envPath = path.join(process.cwd(), '.env')
    this.envExamplePath = path.join(process.cwd(), 'env.example')
    this.config = {}
  }

  /**
   * Start the API setup process
   */
  async start() {
    console.log(`${colors.cyan}${colors.bright}🚀 CryptoAgent Pro - API Key Setup${colors.reset}`)
    console.log(`${colors.yellow}This script will help you configure your API keys securely.${colors.reset}\n`)

    try {
      // Check if .env file exists
      await this.checkEnvironmentFile()

      // Setup exchange API keys
      await this.setupExchangeKeys()

      // Setup AI configuration
      await this.setupAIConfig()

      // Setup database configuration
      await this.setupDatabaseConfig()

      // Setup trading configuration
      await this.setupTradingConfig()

      // Setup alert configuration
      await this.setupAlertConfig()

      // Generate encryption key
      await this.generateEncryptionKey()

      // Save configuration
      await this.saveConfiguration()

      // Test connections
      await this.testConnections()

      console.log(`\n${colors.green}${colors.bright}✅ API Key Setup Complete!${colors.reset}`)
      console.log(`${colors.cyan}Your CryptoAgent Pro is now configured for trading.${colors.reset}`)

    } catch (error) {
      console.error(`${colors.red}❌ Setup failed:${colors.reset}`, error.message)
      process.exit(1)
    } finally {
      rl.close()
    }
  }

  /**
   * Check if .env file exists
   */
  async checkEnvironmentFile() {
    if (!fs.existsSync(this.envPath)) {
      console.log(`${colors.yellow}📝 Creating .env file from template...${colors.reset}`)
      
      if (fs.existsSync(this.envExamplePath)) {
        fs.copyFileSync(this.envExamplePath, this.envPath)
        console.log(`${colors.green}✅ .env file created${colors.reset}`)
      } else {
        throw new Error('env.example file not found')
      }
    } else {
      console.log(`${colors.green}✅ .env file already exists${colors.reset}`)
    }
  }

  /**
   * Setup exchange API keys
   */
  async setupExchangeKeys() {
    console.log(`\n${colors.blue}${colors.bright}📊 Exchange API Keys Setup${colors.reset}`)
    console.log(`${colors.yellow}You'll need to create API keys for each exchange you want to use.${colors.reset}\n`)

    const exchanges = [
      { name: 'Binance', key: 'BINANCE_API_KEY', secret: 'BINANCE_SECRET_KEY' },
      { name: 'MEXC', key: 'MEXC_API_KEY', secret: 'MEXC_SECRET_KEY' },
      { name: 'KuCoin', key: 'KUCOIN_API_KEY', secret: 'KUCOIN_SECRET_KEY', passphrase: 'KUCOIN_PASSPHRASE' },
      { name: 'Bybit', key: 'BYBIT_API_KEY', secret: 'BYBIT_SECRET_KEY' }
    ]

    for (const exchange of exchanges) {
      console.log(`${colors.cyan}${exchange.name} Configuration:${colors.reset}`)
      
      const useExchange = await this.askQuestion(`Do you want to use ${exchange.name}? (y/n): `)
      
      if (useExchange.toLowerCase() === 'y') {
        const apiKey = await this.askQuestion(`Enter your ${exchange.name} API Key: `)
        const secretKey = await this.askQuestion(`Enter your ${exchange.name} Secret Key: `, true)
        
        this.config[exchange.key] = apiKey
        this.config[exchange.secret] = secretKey

        if (exchange.passphrase) {
          const passphrase = await this.askQuestion(`Enter your ${exchange.name} Passphrase: `, true)
          this.config[exchange.passphrase] = passphrase
        }

        const testnet = await this.askQuestion(`Use testnet for ${exchange.name}? (y/n): `)
        this.config[`${exchange.name.toUpperCase()}_TESTNET`] = testnet.toLowerCase() === 'y'

        console.log(`${colors.green}✅ ${exchange.name} configured${colors.reset}\n`)
      } else {
        console.log(`${colors.yellow}⏭️  Skipping ${exchange.name}${colors.reset}\n`)
      }
    }
  }

  /**
   * Setup AI configuration
   */
  async setupAIConfig() {
    console.log(`${colors.blue}${colors.bright}🤖 AI Configuration Setup${colors.reset}`)

    // LM Studio Configuration
    const useLMStudio = await this.askQuestion('Do you want to use LM Studio for AI? (y/n): ')
    
    if (useLMStudio.toLowerCase() === 'y') {
      const lmStudioUrl = await this.askQuestion('Enter LM Studio URL (default: http://************:1235/v1): ', false, 'http://************:1235/v1')
      const lmStudioModel = await this.askQuestion('Enter LM Studio model (default: deepseek-coder-33b-instruct): ', false, 'deepseek-coder-33b-instruct')
      
      this.config['LM_STUDIO_BASE_URL'] = lmStudioUrl
      this.config['LM_STUDIO_MODEL'] = lmStudioModel
      this.config['LM_STUDIO_TIMEOUT'] = '60000'
    }

    // OpenAI Configuration (Backup)
    const useOpenAI = await this.askQuestion('Do you want to use OpenAI as backup? (y/n): ')
    
    if (useOpenAI.toLowerCase() === 'y') {
      const openaiKey = await this.askQuestion('Enter your OpenAI API Key: ')
      this.config['OPENAI_API_KEY'] = openaiKey
      this.config['OPENAI_MODEL'] = 'gpt-4'
    }

    // Anthropic Configuration (Backup)
    const useAnthropic = await this.askQuestion('Do you want to use Anthropic as backup? (y/n): ')
    
    if (useAnthropic.toLowerCase() === 'y') {
      const anthropicKey = await this.askQuestion('Enter your Anthropic API Key: ')
      this.config['ANTHROPIC_API_KEY'] = anthropicKey
      this.config['ANTHROPIC_MODEL'] = 'claude-3-sonnet-20240229'
    }
  }

  /**
   * Setup database configuration
   */
  async setupDatabaseConfig() {
    console.log(`\n${colors.blue}${colors.bright}🗄️  Database Configuration Setup${colors.reset}`)

    const useSupabase = await this.askQuestion('Do you want to use Supabase? (y/n): ')
    
    if (useSupabase.toLowerCase() === 'y') {
      const supabaseUrl = await this.askQuestion('Enter your Supabase URL: ')
      const supabaseAnonKey = await this.askQuestion('Enter your Supabase Anon Key: ')
      const supabaseServiceKey = await this.askQuestion('Enter your Supabase Service Role Key: ')
      
      this.config['NEXT_PUBLIC_SUPABASE_URL'] = supabaseUrl
      this.config['NEXT_PUBLIC_SUPABASE_ANON_KEY'] = supabaseAnonKey
      this.config['SUPABASE_SERVICE_ROLE_KEY'] = supabaseServiceKey
    }
  }

  /**
   * Setup trading configuration
   */
  async setupTradingConfig() {
    console.log(`\n${colors.blue}${colors.bright}⚙️  Trading Configuration Setup${colors.reset}`)

    // Trading Mode
    const tradingMode = await this.askQuestion('Select trading mode (paper/live/demo): ', false, 'paper')
    this.config['TRADING_MODE'] = tradingMode

    // Sandbox Mode
    const sandboxMode = await this.askQuestion('Enable sandbox mode for testing? (y/n): ')
    this.config['SANDBOX_MODE'] = sandboxMode.toLowerCase() === 'y'

    // Risk Management
    console.log(`\n${colors.yellow}Risk Management Settings:${colors.reset}`)
    
    this.config['MAX_PORTFOLIO_RISK'] = await this.askQuestion('Maximum portfolio risk % (default: 20): ', false, '20')
    this.config['MAX_POSITION_SIZE'] = await this.askQuestion('Maximum position size % (default: 5): ', false, '5')
    this.config['MAX_DAILY_LOSS'] = await this.askQuestion('Maximum daily loss USD (default: 1000): ', false, '1000')
    this.config['STOP_LOSS_PERCENT'] = await this.askQuestion('Default stop loss % (default: 2): ', false, '2')
    this.config['TAKE_PROFIT_PERCENT'] = await this.askQuestion('Default take profit % (default: 4): ', false, '4')
    this.config['MAX_OPEN_POSITIONS'] = await this.askQuestion('Maximum open positions (default: 10): ', false, '10')
  }

  /**
   * Setup alert configuration
   */
  async setupAlertConfig() {
    console.log(`\n${colors.blue}${colors.bright}🔔 Alert Configuration Setup${colors.reset}`)

    const enableAlerts = await this.askQuestion('Enable alerts? (y/n): ')
    
    if (enableAlerts.toLowerCase() === 'y') {
      this.config['PRICE_ALERTS_ENABLED'] = 'true'
      this.config['RISK_ALERTS_ENABLED'] = 'true'
      this.config['PERFORMANCE_ALERTS_ENABLED'] = 'true'

      const emailAlerts = await this.askQuestion('Enable email alerts? (y/n): ')
      if (emailAlerts.toLowerCase() === 'y') {
        this.config['EMAIL_NOTIFICATIONS'] = 'true'
        
        const smtpHost = await this.askQuestion('SMTP Host (default: smtp.gmail.com): ', false, 'smtp.gmail.com')
        const smtpPort = await this.askQuestion('SMTP Port (default: 587): ', false, '587')
        const smtpUser = await this.askQuestion('SMTP User (email): ')
        const smtpPass = await this.askQuestion('SMTP Password: ', true)
        
        this.config['SMTP_HOST'] = smtpHost
        this.config['SMTP_PORT'] = smtpPort
        this.config['SMTP_USER'] = smtpUser
        this.config['SMTP_PASS'] = smtpPass
      }

      const pushAlerts = await this.askQuestion('Enable push notifications? (y/n): ')
      if (pushAlerts.toLowerCase() === 'y') {
        this.config['PUSH_NOTIFICATIONS'] = 'true'
      }
    }
  }

  /**
   * Generate encryption key
   */
  async generateEncryptionKey() {
    console.log(`\n${colors.blue}${colors.bright}🔐 Security Configuration${colors.reset}`)
    
    const encryptionKey = crypto.randomBytes(32).toString('hex')
    this.config['ENCRYPTION_KEY'] = encryptionKey
    this.config['API_KEY_ENCRYPTION'] = 'true'
    
    console.log(`${colors.green}✅ Encryption key generated${colors.reset}`)
  }

  /**
   * Save configuration to .env file
   */
  async saveConfiguration() {
    console.log(`\n${colors.blue}${colors.bright}💾 Saving Configuration${colors.reset}`)

    let envContent = fs.readFileSync(this.envPath, 'utf8')

    // Update environment variables
    for (const [key, value] of Object.entries(this.config)) {
      const regex = new RegExp(`^${key}=.*`, 'm')
      const newLine = `${key}=${value}`
      
      if (envContent.match(regex)) {
        envContent = envContent.replace(regex, newLine)
      } else {
        // Add new variable if it doesn't exist
        envContent += `\n${newLine}`
      }
    }

    fs.writeFileSync(this.envPath, envContent)
    console.log(`${colors.green}✅ Configuration saved to .env file${colors.reset}`)
  }

  /**
   * Test connections
   */
  async testConnections() {
    console.log(`\n${colors.blue}${colors.bright}🧪 Testing Connections${colors.reset}`)

    // Test exchange connections
    const exchanges = ['BINANCE', 'MEXC', 'KUCOIN', 'BYBIT']
    
    for (const exchange of exchanges) {
      if (this.config[`${exchange}_API_KEY`]) {
        console.log(`${colors.yellow}Testing ${exchange} connection...${colors.reset}`)
        
        try {
          // Here you would implement actual connection testing
          // For now, we'll just simulate a successful connection
          await new Promise(resolve => setTimeout(resolve, 1000))
          console.log(`${colors.green}✅ ${exchange} connection successful${colors.reset}`)
        } catch (error) {
          console.log(`${colors.red}❌ ${exchange} connection failed: ${error.message}${colors.reset}`)
        }
      }
    }

    // Test AI connections
    if (this.config['LM_STUDIO_BASE_URL']) {
      console.log(`${colors.yellow}Testing LM Studio connection...${colors.reset}`)
      try {
        // Simulate LM Studio test
        await new Promise(resolve => setTimeout(resolve, 500))
        console.log(`${colors.green}✅ LM Studio connection successful${colors.reset}`)
      } catch (error) {
        console.log(`${colors.red}❌ LM Studio connection failed: ${error.message}${colors.reset}`)
      }
    }

    // Test database connection
    if (this.config['NEXT_PUBLIC_SUPABASE_URL']) {
      console.log(`${colors.yellow}Testing Supabase connection...${colors.reset}`)
      try {
        // Simulate Supabase test
        await new Promise(resolve => setTimeout(resolve, 500))
        console.log(`${colors.green}✅ Supabase connection successful${colors.reset}`)
      } catch (error) {
        console.log(`${colors.red}❌ Supabase connection failed: ${error.message}${colors.reset}`)
      }
    }
  }

  /**
   * Ask a question and return the answer
   */
  askQuestion(question, hideInput = false, defaultValue = '') {
    return new Promise((resolve) => {
      if (defaultValue) {
        question += ` (default: ${defaultValue}) `
      }
      
      rl.question(question, (answer) => {
        if (!answer && defaultValue) {
          resolve(defaultValue)
        } else {
          resolve(answer)
        }
      })

      if (hideInput) {
        // Hide input for sensitive data
        process.stdout.write('\x1b[8m')
        setTimeout(() => {
          process.stdout.write('\x1b[28m')
        }, 100)
      }
    })
  }
}

// Run the setup
if (require.main === module) {
  const setupManager = new APISetupManager()
  setupManager.start().catch(console.error)
}

module.exports = APISetupManager 