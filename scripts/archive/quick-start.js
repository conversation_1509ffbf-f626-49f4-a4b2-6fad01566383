#!/usr/bin/env node

/**
 * CryptoAgent Pro - Quick Start Script
 * 
 * This script gets you up and running immediately with demo data
 * Perfect for testing the interface without setting up databases or APIs
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 CryptoAgent Pro - Quick Start Mode\n');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Check if .env exists, create minimal one if not
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
  const minimalEnv = `# CryptoAgent Pro - Quick Start Configuration
# This is a minimal setup for demo mode

# Demo Mode Settings
TRADING_MODE=demo
SANDBOX_MODE=true
TEST_MODE=true
MOCK_TRADING_ENABLED=true

# Demo Portfolio
SIMULATED_BALANCE=50000

# App Configuration
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Demo AI Configuration (no real API keys needed)
LM_STUDIO_BASE_URL=http://localhost:1235/v1
LM_STUDIO_MODEL=demo-model

# Demo Database (using mock data)
NEXT_PUBLIC_SUPABASE_URL=demo-mode
NEXT_PUBLIC_SUPABASE_ANON_KEY=demo-mode

# Security (demo values)
ENCRYPTION_KEY=demo_key_32_characters_long_demo
`;

  fs.writeFileSync(envPath, minimalEnv);
  log('✅ Created minimal .env file for demo mode', 'green');
}

log('🎯 Quick Start Mode Features:', 'cyan');
log('✅ Demo AI agents with realistic behavior', 'green');
log('✅ Mock trading positions and portfolio', 'green');
log('✅ Real-time price simulations', 'green');
log('✅ Interactive mobile interface', 'green');
log('✅ No API keys or database setup required', 'green');

log('\n🚀 Starting CryptoAgent Pro in Demo Mode...', 'yellow');

// Start the Next.js development server
const nextProcess = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  cwd: process.cwd()
});

// Handle process termination
process.on('SIGINT', () => {
  log('\n🛑 Shutting down CryptoAgent Pro...', 'yellow');
  nextProcess.kill();
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('\n🛑 Received termination signal...', 'yellow');
  nextProcess.kill();
  process.exit(0);
});

// Show instructions after a delay
setTimeout(() => {
  console.log('\n' + '='.repeat(60));
  log('🎉 CryptoAgent Pro is now running in Demo Mode!', 'green');
  console.log('='.repeat(60));
  
  log('\n📱 Mobile Interface:', 'cyan');
  log('   http://localhost:3000/mobile-control', 'blue');
  
  log('\n🖥️  Desktop Dashboard:', 'cyan');
  log('   http://localhost:3000/dashboard', 'blue');
  
  log('\n🤖 What you can test:', 'yellow');
  log('• View AI agents analyzing markets', 'cyan');
  log('• See live portfolio performance', 'cyan');
  log('• Test voice commands (if supported)', 'cyan');
  log('• Monitor trading positions', 'cyan');
  log('• Toggle auto-trading mode', 'cyan');
  
  log('\n⚡ Demo Features:', 'yellow');
  log('• 5 specialized AI agents', 'cyan');
  log('• $34,241 demo portfolio', 'cyan');
  log('• 4 active trading positions', 'cyan');
  log('• Real-time market simulation', 'cyan');
  log('• Mobile-optimized interface', 'cyan');
  
  log('\n🔧 Next Steps (Optional):', 'yellow');
  log('1. Set up real exchange APIs for live trading', 'cyan');
  log('2. Configure Supabase for data persistence', 'cyan');
  log('3. Add AI model APIs (OpenAI, Anthropic, etc.)', 'cyan');
  log('4. Enable real-time market data feeds', 'cyan');
  
  log('\n⚠️  Demo Mode Notes:', 'red');
  log('• All trading is simulated', 'red');
  log('• No real money is involved', 'red');
  log('• Data resets on restart', 'red');
  log('• Perfect for testing and development', 'green');
  
  log('\n🚀 Enjoy exploring CryptoAgent Pro!', 'green');
  console.log('='.repeat(60) + '\n');
}, 3000);

// Keep the process alive
nextProcess.on('close', (code) => {
  if (code !== 0) {
    log(`❌ Process exited with code ${code}`, 'red');
  }
  process.exit(code);
});
