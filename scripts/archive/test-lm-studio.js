#!/usr/bin/env node

/**
 * Test script voor LM Studio configuratie
 * Test de verbinding met je LM Studio modellen
 */

// Import fetch for Node.js
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const LM_STUDIO_URL = 'http://************:1235/v1';

async function testLMStudio() {
  console.log('🧪 Testing LM Studio connection...\n');
  
  try {
    // Test 1: Check if server is running
    console.log('1. Testing server connection...');
    const healthResponse = await fetch(`${LM_STUDIO_URL}/models`);
    
    if (!healthResponse.ok) {
      throw new Error(`Server not responding: ${healthResponse.status}`);
    }
    
    const models = await healthResponse.json();
    console.log('✅ Server is running');
    console.log(`📋 Available models: ${models.data?.length || 0}\n`);
    
    // Test 2: Test each model
    const testModels = [
      'deepseek-coder-33b-instruct',
      'qwen/qwen2.5-coder-14b', 
      'codellama-7b-kstack'
    ];
    
    for (const model of testModels) {
      console.log(`2. Testing model: ${model}`);
      
      try {
        const response = await fetch(`${LM_STUDIO_URL}/chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: model,
            messages: [
              {
                role: 'system',
                content: 'You are a helpful coding assistant. Respond briefly.'
              },
              {
                role: 'user',
                content: 'Say "Hello from LM Studio!"'
              }
            ],
            temperature: 0.1,
            max_tokens: 50,
            stream: false
          })
        });
        
        if (!response.ok) {
          console.log(`❌ Model ${model} failed: ${response.status}`);
          continue;
        }
        
        const data = await response.json();
        console.log(`✅ Model ${model} working`);
        console.log(`   Response: ${data.choices[0]?.message?.content?.trim()}`);
        
      } catch (error) {
        console.log(`❌ Model ${model} error: ${error.message}`);
      }
      
      console.log('');
    }
    
    // Test 3: Test coding task
    console.log('3. Testing coding capability...');
    
    const codingResponse = await fetch(`${LM_STUDIO_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-coder-33b-instruct',
        messages: [
          {
            role: 'system',
            content: 'You are a professional software developer. Write clean, efficient code.'
          },
          {
            role: 'user',
            content: 'Write a simple React component that displays "Hello World"'
          }
        ],
        temperature: 0.1,
        max_tokens: 200,
        stream: false
      })
    });
    
    if (codingResponse.ok) {
      const codingData = await codingResponse.json();
      console.log('✅ Coding test successful');
      console.log('📝 Generated code:');
      console.log(codingData.choices[0]?.message?.content);
    } else {
      console.log('❌ Coding test failed');
    }
    
  } catch (error) {
    console.error('❌ LM Studio test failed:', error.message);
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Make sure LM Studio is running');
    console.log('2. Check if the URL is correct: http://************:1235/v1');
    console.log('3. Verify models are loaded in LM Studio');
    console.log('4. Check firewall settings');
  }
}

// Run the test
testLMStudio(); 