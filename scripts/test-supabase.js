#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') });

async function testSupabase() {
  console.log('🧪 Testing Supabase Connection...\n');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Supabase credentials not found in environment variables');
    process.exit(1);
  }
  
  console.log(`📍 Supabase URL: ${supabaseUrl}`);
  console.log(`🔑 Key: ${supabaseKey.substring(0, 20)}...`);
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test 1: Basic connection
    console.log('\n1️⃣ Testing basic connection...');
    const { data, error } = await supabase.from('users').select('count').limit(1);
    
    if (error && error.code !== 'PGRST116') { // PGRST116 = table doesn't exist
      throw error;
    }
    
    console.log('✅ Connection successful');
    
    // Test 2: Check if tables exist
    console.log('\n2️⃣ Checking database tables...');
    
    const tables = [
      'users',
      'trading_sessions',
      'trades',
      'portfolios',
      'alerts',
      'ai_configs',
      'exchange_configs'
    ];
    
    const tableStatus = [];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table).select('*').limit(1);
        if (error) {
          tableStatus.push({ table, exists: false, error: error.message });
        } else {
          tableStatus.push({ table, exists: true, records: data ? data.length : 0 });
        }
      } catch (e) {
        tableStatus.push({ table, exists: false, error: e.message });
      }
    }
    
    console.log('📊 Table Status:');
    tableStatus.forEach(status => {
      if (status.exists) {
        console.log(`   ✅ ${status.table} - OK`);
      } else {
        console.log(`   ❌ ${status.table} - ${status.error}`);
      }
    });
    
    // Test 3: Test authentication
    console.log('\n3️⃣ Testing authentication...');
    const { data: authData, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      console.log(`🟡 Auth test: ${authError.message}`);
    } else {
      console.log('✅ Authentication system accessible');
    }
    
    // Test 4: Check RLS policies
    console.log('\n4️⃣ Testing Row Level Security...');
    try {
      const { data: rlsData, error: rlsError } = await supabase
        .from('users')
        .select('*')
        .limit(1);
        
      if (rlsError && rlsError.code === '42501') {
        console.log('✅ RLS is enabled (access denied as expected)');
      } else if (rlsError) {
        console.log(`🟡 RLS test: ${rlsError.message}`);
      } else {
        console.log('🟡 RLS may not be enabled (data accessible without auth)');
      }
    } catch (e) {
      console.log(`🟡 RLS test error: ${e.message}`);
    }
    
    console.log('\n🎉 Supabase tests completed!');
    
  } catch (error) {
    console.error('\n❌ Supabase test failed:');
    console.error(`   Error: ${error.message}`);
    
    if (error.message.includes('Invalid API key')) {
      console.log('\n💡 Suggestions:');
      console.log('   - Check your NEXT_PUBLIC_SUPABASE_ANON_KEY');
      console.log('   - Verify the key in your Supabase dashboard');
    } else if (error.message.includes('Invalid URL')) {
      console.log('\n💡 Suggestions:');
      console.log('   - Check your NEXT_PUBLIC_SUPABASE_URL');
      console.log('   - Ensure the URL format is correct');
    }
    
    process.exit(1);
  }
}

testSupabase();
