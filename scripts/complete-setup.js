#!/usr/bin/env node

/**
 * CryptoAgent Pro - Complete Setup Script
 * 
 * This script sets up the entire CryptoAgent Pro system:
 * 1. Environment configuration
 * 2. Database setup
 * 3. AI agent initialization
 * 4. Trading engine startup
 * 5. Real-time data feeds
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 CryptoAgent Pro - Complete Setup Starting...\n');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function step(stepNumber, title) {
  log(`\n${stepNumber}. ${title}`, 'cyan');
  log('='.repeat(50), 'blue');
}

// Step 1: Check Prerequisites
step(1, 'Checking Prerequisites');

try {
  // Check Node.js version
  const nodeVersion = process.version;
  log(`✅ Node.js version: ${nodeVersion}`, 'green');
  
  // Check if npm is available
  execSync('npm --version', { stdio: 'pipe' });
  log('✅ npm is available', 'green');
  
  // Check if git is available
  execSync('git --version', { stdio: 'pipe' });
  log('✅ git is available', 'green');
  
} catch (error) {
  log('❌ Prerequisites check failed', 'red');
  log('Please ensure Node.js, npm, and git are installed', 'red');
  process.exit(1);
}

// Step 2: Environment Setup
step(2, 'Setting up Environment Variables');

const envPath = path.join(process.cwd(), '.env');
const envExamplePath = path.join(process.cwd(), 'env.example');

if (!fs.existsSync(envPath)) {
  if (fs.existsSync(envExamplePath)) {
    fs.copyFileSync(envExamplePath, envPath);
    log('✅ Created .env file from env.example', 'green');
    log('⚠️  Please edit .env file with your actual API keys', 'yellow');
  } else {
    log('❌ env.example file not found', 'red');
    process.exit(1);
  }
} else {
  log('✅ .env file already exists', 'green');
}

// Step 3: Install Dependencies
step(3, 'Installing Dependencies');

try {
  log('Installing npm packages...', 'yellow');
  execSync('npm install', { stdio: 'inherit' });
  log('✅ Dependencies installed successfully', 'green');
} catch (error) {
  log('❌ Failed to install dependencies', 'red');
  process.exit(1);
}

// Step 4: Database Setup
step(4, 'Database Setup Instructions');

log('📋 Database Setup Required:', 'yellow');
log('1. Create a Supabase account at https://supabase.com', 'cyan');
log('2. Create a new project', 'cyan');
log('3. Go to SQL Editor in your Supabase dashboard', 'cyan');
log('4. Copy and paste the contents of database-schema.sql', 'cyan');
log('5. Run the SQL to create all tables and sample data', 'cyan');
log('6. Update your .env file with Supabase credentials:', 'cyan');
log('   - NEXT_PUBLIC_SUPABASE_URL', 'magenta');
log('   - NEXT_PUBLIC_SUPABASE_ANON_KEY', 'magenta');
log('   - SUPABASE_SERVICE_ROLE_KEY', 'magenta');

// Step 5: AI Model Setup
step(5, 'AI Model Configuration');

log('🤖 AI Model Setup Options:', 'yellow');
log('Option 1: LM Studio (Local AI - Recommended for development)', 'cyan');
log('  - Download LM Studio from https://lmstudio.ai/', 'cyan');
log('  - Download a model (e.g., deepseek-coder-33b-instruct)', 'cyan');
log('  - Start the local server on port 1235', 'cyan');
log('  - Update LM_STUDIO_BASE_URL in .env', 'cyan');

log('\nOption 2: OpenAI API (Cloud AI)', 'cyan');
log('  - Get API key from https://platform.openai.com/', 'cyan');
log('  - Update OPENAI_API_KEY in .env', 'cyan');

log('\nOption 3: Anthropic Claude (Cloud AI)', 'cyan');
log('  - Get API key from https://console.anthropic.com/', 'cyan');
log('  - Update ANTHROPIC_API_KEY in .env', 'cyan');

// Step 6: Exchange Setup
step(6, 'Exchange API Configuration');

log('📈 Exchange Setup (Choose one or more):', 'yellow');
log('Binance:', 'cyan');
log('  - Create API keys at https://www.binance.com/en/my/settings/api-management', 'cyan');
log('  - Enable "Enable Trading" permission', 'cyan');
log('  - Update BINANCE_API_KEY and BINANCE_SECRET_KEY in .env', 'cyan');

log('\nBybit:', 'cyan');
log('  - Create API keys at https://www.bybit.com/app/user/api-management', 'cyan');
log('  - Update BYBIT_API_KEY and BYBIT_SECRET_KEY in .env', 'cyan');

log('\n⚠️  IMPORTANT: Start with testnet/sandbox mode!', 'red');
log('Set SANDBOX_MODE=true in .env for safe testing', 'red');

// Step 7: Create startup script
step(7, 'Creating Startup Scripts');

const startupScript = `#!/usr/bin/env node

/**
 * CryptoAgent Pro - Startup Script
 * Starts all services in the correct order
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting CryptoAgent Pro...');

// Start Next.js development server
const nextProcess = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  cwd: process.cwd()
});

// Start trading engine (if configured)
setTimeout(() => {
  console.log('🤖 Starting AI Trading Engine...');
  // This will be implemented when the trading engine is ready
}, 5000);

process.on('SIGINT', () => {
  console.log('\\n🛑 Shutting down CryptoAgent Pro...');
  nextProcess.kill();
  process.exit(0);
});
`;

fs.writeFileSync(path.join(process.cwd(), 'scripts', 'start.js'), startupScript);
fs.chmodSync(path.join(process.cwd(), 'scripts', 'start.js'), '755');
log('✅ Created startup script', 'green');

// Final instructions
step(8, 'Setup Complete - Next Steps');

log('🎉 Basic setup is complete!', 'green');
log('\n📋 Next Steps:', 'yellow');
log('1. Edit .env file with your actual API keys', 'cyan');
log('2. Set up Supabase database using database-schema.sql', 'cyan');
log('3. Configure at least one AI provider (LM Studio recommended)', 'cyan');
log('4. Set up exchange API keys (start with testnet)', 'cyan');
log('5. Run: npm run dev', 'cyan');
log('6. Visit: http://localhost:3000/mobile-control', 'cyan');

log('\n🔧 Available Commands:', 'yellow');
log('npm run dev          - Start development server', 'cyan');
log('node scripts/start.js - Start complete system', 'cyan');
log('npm run test         - Run tests', 'cyan');

log('\n⚠️  Important Security Notes:', 'red');
log('- Never commit .env file to git', 'red');
log('- Start with sandbox/testnet mode', 'red');
log('- Use small amounts for initial testing', 'red');
log('- Monitor your positions closely', 'red');

log('\n🚀 CryptoAgent Pro Setup Complete!', 'green');
