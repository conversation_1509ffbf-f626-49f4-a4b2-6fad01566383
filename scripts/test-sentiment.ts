import { SentimentAnalyzer } from '../lib/analysis/SentimentAnalyzer'

// Test environment setup
process.env.TWITTER_API_KEY = 'test-key'
process.env.TWITTER_API_SECRET = 'test-secret'
process.env.TWITTER_BEARER_TOKEN = 'test-token'
process.env.REDDIT_CLIENT_ID = 'test-client-id'
process.env.REDDIT_CLIENT_SECRET = 'test-client-secret'
process.env.REDDIT_USERNAME = 'test-username'
process.env.REDDIT_PASSWORD = 'test-password'
process.env.NEWS_API_KEY = 'test-news-key'
process.env.SENTIMENT_WEIGHT = '0.3'
process.env.SENTIMENT_ANALYSIS_ENABLED = 'true'

async function testSentimentAnalyzer() {
  console.log('🧪 Testing SentimentAnalyzer...\n')

  try {
    const analyzer = new SentimentAnalyzer()

    // Test 1: Constructor and basic properties
    console.log('✅ Test 1: Constructor')
    console.log(`- Sentiment weight: ${analyzer.getSentimentWeight()}`)
    console.log(`- Analysis enabled: ${analyzer.isEnabled()}`)
    console.log()

    // Test 2: Text sentiment analysis
    console.log('✅ Test 2: Text Sentiment Analysis')
    
    const positiveText = 'Bitcoin is bullish and going to the moon! This is a great investment opportunity.'
    const negativeText = 'Bitcoin is crashing and dumping hard. This is a bear market and everyone is selling.'
    const neutralText = 'Bitcoin price is stable today. The market shows normal trading activity.'

    const positiveResult = (analyzer as any).analyzeTextSentiment(positiveText)
    const negativeResult = (analyzer as any).analyzeTextSentiment(negativeText)
    const neutralResult = (analyzer as any).analyzeTextSentiment(neutralText)

    console.log(`Positive text: "${positiveText.substring(0, 50)}..."`)
    console.log(`- Sentiment: ${positiveResult.sentiment}, Score: ${positiveResult.score}`)
    
    console.log(`Negative text: "${negativeText.substring(0, 50)}..."`)
    console.log(`- Sentiment: ${negativeResult.sentiment}, Score: ${negativeResult.score}`)
    
    console.log(`Neutral text: "${neutralText.substring(0, 50)}..."`)
    console.log(`- Sentiment: ${neutralResult.sentiment}, Score: ${neutralResult.score}`)
    console.log()

    // Test 3: Keyword extraction
    console.log('✅ Test 3: Keyword Extraction')
    
    const testText = 'Bitcoin trading volume is high and the crypto market is showing strong liquidity.'
    const keywords = (analyzer as any).extractKeywords(testText)
    
    console.log(`Text: "${testText}"`)
    console.log(`- Extracted keywords: ${keywords.join(', ')}`)
    console.log()

    // Test 4: Weighted score calculation
    console.log('✅ Test 4: Weighted Score Calculation')
    
    const mockData = [
      {
        source: 'twitter' as const,
        timestamp: new Date(),
        sentiment: 'positive' as const,
        score: 0.8,
        volume: 100,
        keywords: ['bitcoin'],
        text: 'Bitcoin is bullish!'
      },
      {
        source: 'twitter' as const,
        timestamp: new Date(),
        sentiment: 'negative' as const,
        score: -0.3,
        volume: 50,
        keywords: ['crash'],
        text: 'Bitcoin is crashing'
      }
    ]

    const weightedScore = (analyzer as any).calculateWeightedScore(mockData)
    console.log(`- Weighted score: ${weightedScore}`)
    console.log()

    // Test 5: Trending topics
    console.log('✅ Test 5: Trending Topics')
    
    const twitterData = [
      {
        source: 'twitter' as const,
        timestamp: new Date(),
        sentiment: 'positive' as const,
        score: 0.5,
        volume: 10,
        keywords: ['bitcoin', 'trading'],
        text: 'Bitcoin trading is great'
      }
    ]

    const redditData = [
      {
        source: 'reddit' as const,
        timestamp: new Date(),
        sentiment: 'neutral' as const,
        score: 0,
        volume: 5,
        keywords: ['bitcoin', 'market'],
        text: 'Bitcoin market discussion'
      }
    ]

    const trendingTopics = (analyzer as any).extractTrendingTopics(twitterData, redditData, [])
    console.log(`- Trending topics: ${trendingTopics.join(', ')}`)
    console.log()

    // Test 6: Confidence calculation
    console.log('✅ Test 6: Confidence Calculation')
    
    const confidence = (analyzer as any).calculateConfidence(twitterData, redditData, [])
    console.log(`- Confidence: ${confidence}`)
    console.log()

    // Test 7: Full sentiment analysis (will fail due to API calls, but shows structure)
    console.log('✅ Test 7: Full Sentiment Analysis')
    console.log('⚠️  This test will fail due to missing API keys, but shows the structure')
    
    try {
      const result = await analyzer.analyzeSentiment('BTC')
      console.log(`- Overall sentiment: ${result.overallSentiment}`)
      console.log(`- Sentiment score: ${result.sentimentScore}`)
      console.log(`- Confidence: ${result.confidence}`)
      console.log(`- Twitter data points: ${result.volume.twitter}`)
      console.log(`- Reddit data points: ${result.volume.reddit}`)
      console.log(`- News data points: ${result.volume.news}`)
    } catch (error) {
      console.log(`- Expected error: ${(error as Error).message}`)
    }

    console.log('\n🎉 All tests completed!')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the tests
testSentimentAnalyzer() 