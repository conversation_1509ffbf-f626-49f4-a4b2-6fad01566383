import { SentimentAnalysisAgentV3 } from '../lib/agents/SentimentAnalysisAgentV3'

async function testSentimentAnalysisV3() {
  console.log('🚀 Testing Advanced Multi-Model Sentiment Analysis System V3...\n')

  const agent = new SentimentAnalysisAgentV3()
  
  // Test symbols
  const testSymbols = ['BTC', 'ETH', 'ADA', 'SOL']
  
  console.log(`📊 Testing ${testSymbols.length} cryptocurrencies with multiple AI models:\n`)

  for (const symbol of testSymbols) {
    try {
      console.log(`\n🔍 Analyzing ${symbol}...`)
      console.log('=' .repeat(50))
      
      const startTime = Date.now()
      const analysis = await agent.analyze(symbol)
      const endTime = Date.now()
      
      console.log(`✅ Analysis completed in ${endTime - startTime}ms`)
      console.log(`\n📈 ${symbol} Sentiment Analysis Results:`)
      console.log(`   Recommendation: ${analysis.data.recommendation}`)
      console.log(`   Sentiment Score: ${analysis.data.sentimentScore}/100`)
      console.log(`   Confidence: ${analysis.data.confidence}%`)
      console.log(`   Social Volume: ${analysis.data.socialVolume}`)
      console.log(`   Market Mood: ${analysis.data.marketMood}`)
      console.log(`   Risk Level: ${analysis.data.riskLevel}`)
      console.log(`   Technical Alignment: ${analysis.data.technicalAlignment}`)
      
      console.log(`\n🤖 AI Model Consensus:`)
      Object.entries(analysis.data.aiConsensus).forEach(([provider, data]) => {
        const providerData = data as any
        console.log(`   ${provider}: Score ${providerData.score}, Confidence ${providerData.confidence}%`)
      })
      
      console.log(`\n💡 Key Factors:`)
      analysis.data.keyFactors.forEach((factor: any, i: number) => {
        console.log(`   ${i + 1}. ${factor}`)
      })
      
      console.log(`\n📱 Social Media Trends:`)
      analysis.data.socialMediaTrends.forEach((trend: any, i: number) => {
        console.log(`   ${i + 1}. ${trend}`)
      })
      
      console.log(`\n🔮 Emerging Narratives:`)
      analysis.data.emergingNarratives.forEach((narrative: any, i: number) => {
        console.log(`   ${i + 1}. ${narrative}`)
      })
      
      console.log(`\n📊 Correlations:`)
      console.log(`   Price Correlation: ${(analysis.data.correlationWithPrice * 100).toFixed(1)}%`)
      console.log(`   Volume Correlation: ${(analysis.data.volumeCorrelation * 100).toFixed(1)}%`)
      
      console.log(`\n💭 AI Reasoning:`)
      console.log(`   ${analysis.data.reasoning}`)
      
      // Small delay to be respectful to APIs
      await new Promise(resolve => setTimeout(resolve, 2000))
      
    } catch (error) {
      console.error(`❌ Error analyzing ${symbol}:`, error instanceof Error ? error.message : 'Unknown error')
    }
  }
  
  console.log('\n' + '='.repeat(60))
  console.log('🎉 Multi-Model Sentiment Analysis Test Complete!')
  console.log('='.repeat(60))
}

async function testBulkSentimentAnalysis() {
  console.log('\n🔄 Testing Bulk Sentiment Analysis...\n')
  
  try {
    const response = await fetch('http://localhost:3000/api/sentiment/bulk', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        symbols: ['BTC', 'ETH', 'ADA'],
        maxConcurrency: 2
      })
    })
    
    const data = await response.json()
    
    if (data.success) {
      console.log('✅ Bulk analysis successful!')
      console.log(`📊 Results: ${data.data.summary.successful}/${data.data.summary.totalRequested} symbols analyzed`)
      
      data.data.results.forEach((result: any) => {
        console.log(`\n${result.symbol}:`)
        console.log(`  Recommendation: ${result.data.data.recommendation}`)
        console.log(`  Score: ${result.data.data.sentimentScore}`)
        console.log(`  Confidence: ${result.data.data.confidence}%`)
      })
    } else {
      console.error('❌ Bulk analysis failed:', data.error)
    }
    
  } catch (error) {
    console.error('❌ Bulk analysis error:', error instanceof Error ? error.message : 'Unknown error')
  }
}

async function testWebSocketSimulation() {
  console.log('\n📡 Testing WebSocket Simulation...\n')
  
  const { SentimentWebSocketManager } = await import('../lib/websocket/SentimentWebSocket')
  const wsManager = new SentimentWebSocketManager()
  
  // Subscribe to updates
  wsManager.subscribe('BTC', (update) => {
    console.log(`📈 BTC Sentiment Update:`, update)
  })
  
  wsManager.subscribe('ETH', (update) => {
    console.log(`📈 ETH Sentiment Update:`, update)
  })
  
  console.log('📡 Subscribed to BTC and ETH sentiment updates')
  console.log('⏱️  Waiting for updates... (will run for 30 seconds)')
  
  // Let it run for 30 seconds
  setTimeout(() => {
    console.log('\n🔌 Disconnecting from sentiment updates...')
    wsManager.disconnect()
    console.log('✅ WebSocket simulation complete!')
  }, 30000)
}

async function runAllTests() {
  try {
    await testSentimentAnalysisV3()
    await testBulkSentimentAnalysis()
    await testWebSocketSimulation()
    
  } catch (error) {
    console.error('❌ Test suite error:', error)
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().then(() => {
    console.log('\n🎯 All tests completed!')
    // Keep process alive for WebSocket test
    setTimeout(() => process.exit(0), 35000)
  })
}

export { testSentimentAnalysisV3, testBulkSentimentAnalysis, testWebSocketSimulation }