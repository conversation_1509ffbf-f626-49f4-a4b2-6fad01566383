#!/bin/bash

echo "🚀 LM Studio + Cursor Setup Guide"
echo "=================================="
echo ""

echo "📋 STAP 1: LM Studio Server Starten"
echo "1. Open LM Studio applicatie"
echo "2. Ga naar 'Local Server' tab"
echo "3. Laad een model (bijv. CodeLlama, DeepSeek Coder)"
echo "4. Klik 'Start Server'"
echo "5. Controleer dat server draait op localhost:1234"
echo ""

echo "📋 STAP 2: Test de Verbinding"
echo "Voer dit commando uit om te testen:"
echo "curl http://localhost:1234/v1/models"
echo ""

echo "📋 STAP 3: Cursor Configureren"
echo "1. Open Cursor IDE"
echo "2. Ga naar Settings (Cmd/Ctrl + ,)"
echo "3. Zoek naar 'Models'"
echo "4. Voeg toe:"
echo "   - API Base: http://localhost:1234/v1"
echo "   - API Key: lm-studio"
echo "   - Model: local-model"
echo ""

echo "📋 STAP 4: Test in Cursor"
echo "1. Open een .ts of .js bestand"
echo "2. Druk Cmd/Ctrl + K voor inline editing"
echo "3. Druk Cmd/Ctrl + L voor chat"
echo "4. Gebruik Tab voor autocomplete"
echo ""

echo "🎯 AANBEVOLEN MODELLEN VOOR CODING:"
echo "- DeepSeek Coder 6.7B (snel, goed voor TypeScript)"
echo "- CodeLlama 7B (uitstekend voor algemene programmering)"
echo "- Llama 3 8B (beste overall performance)"
echo "- Mistral 7B (zeer snel, minder geheugen)"
echo ""

echo "⚡ PERFORMANCE TIPS:"
echo "- Gebruik GPU als beschikbaar"
echo "- Minimaal 8GB RAM voor 7B modellen"
echo "- 16GB+ RAM voor 13B+ modellen"
echo ""

echo "🔧 TROUBLESHOOTING:"
echo "- Server niet bereikbaar? Check of LM Studio draait"
echo "- Langzame responses? Probeer een kleiner model"
echo "- Cursor verbindt niet? Herstart Cursor na configuratie"
echo ""

echo "✅ Klaar! Je kunt nu AI-assistentie gebruiken in Cursor!"
echo "Happy coding! 🎉"
