#!/usr/bin/env node

const ccxt = require('ccxt');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') });

async function testConnections() {
  console.log('🧪 Testing Exchange Connections...\n');

  const exchanges = [
    {
      name: 'Binance',
      id: 'binance',
      creds: {
        apiKey: process.env.BINANCE_API_KEY,
        secret: process.env.BINANCE_SECRET_KEY,
        options: {
          defaultType: 'spot',
        },
      },
      testnet: process.env.BINANCE_TESTNET === 'true',
    },
    {
      name: '<PERSON><PERSON>oin',
      id: 'kucoin',
      creds: {
        apiKey: process.env.KUCOIN_API_KEY,
        secret: process.env.KUCOIN_SECRET_KEY,
        password: process.env.KUCOIN_PASSPHRASE,
      },
       testnet: process.env.KUCOIN_TESTNET === 'true',
    },
    {
      name: 'Bybit',
      id: 'bybit',
      creds: {
        apiKey: process.env.BYBIT_API_KEY,
        secret: process.env.BYBIT_SECRET_KEY,
      },
      testnet: process.env.BYBIT_TESTNET === 'true',
    },
     {
      name: 'MEXC',
      id: 'mexc',
      creds: {
        apiKey: process.env.MEXC_API_KEY,
        secret: process.env.MEXC_SECRET_KEY,
      },
      testnet: process.env.MEXC_TESTNET === 'true',
    }
  ];

  for (const exchangeConfig of exchanges) {
    console.log(`--- Testing ${exchangeConfig.name} ---`);
    const { name, id, creds, testnet } = exchangeConfig;

    if (!creds.apiKey || creds.apiKey.includes('your_') || !creds.secret || creds.secret.includes('your_')) {
      console.log(`🟡 Skipping ${name}: API keys not set.\n`);
      continue;
    }

    try {
      const exchange = new ccxt[id](creds);
      exchange.setSandboxMode(testnet);
      
      console.log(`🌐 Connecting to ${name} ${testnet ? '(Testnet)' : '(Mainnet)'}...`);
      await exchange.loadMarkets();
      console.log('✅ Markets loaded successfully.');

      console.log('💰 Fetching balance...');
      const balance = await exchange.fetchBalance();
      console.log('✅ Balance fetched successfully.');
      console.log(`   Total assets: ${Object.keys(balance.total).length}`);

      console.log(`✅ ${name} connection successful!\n`);
    } catch (e) {
      console.error(`❌ Error connecting to ${name}:`);
      if (e instanceof ccxt.AuthenticationError) {
        console.error('   Authentication failed. Please check your API keys, secret, and passphrase.');
      } else if (e instanceof ccxt.NetworkError) {
         console.error('   Network error. Check your connection and firewall settings.');
      } else {
        console.error(`   ${e.constructor.name}: ${e.message}`);
      }
      console.log('\n');
    }
  }
}

testConnections();
