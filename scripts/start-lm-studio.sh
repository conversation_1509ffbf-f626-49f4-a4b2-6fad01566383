#!/bin/bash

# LM Studio Server Startup Script
# This script helps start LM Studio in server mode for IDE integration

echo "🚀 Starting LM Studio Server for IDE Integration..."

# Check if LM Studio is installed
if ! command -v lmstudio &> /dev/null; then
    echo "❌ LM Studio CLI not found. Please install LM Studio first."
    echo "📥 Download from: https://lmstudio.ai/"
    exit 1
fi

# Start LM Studio server
echo "🔧 Starting LM Studio server on localhost:1234..."
echo "📡 API Base: http://localhost:1234/v1"
echo "🔑 API Key: lm-studio"
echo ""
echo "⚡ Server will be available for:"
echo "   - Cursor IDE integration"
echo "   - VS Code Copilot alternative"
echo "   - Direct API calls from your app"
echo ""
echo "🛑 Press Ctrl+C to stop the server"
echo ""

# Start the server (you'll need to run this command in LM Studio)
echo "📋 Manual steps:"
echo "1. Open LM Studio"
echo "2. Go to 'Local Server' tab"
echo "3. Load your preferred model"
echo "4. Click 'Start Server'"
echo "5. Server will run on http://localhost:1234/v1"
echo ""
echo "🔧 For Cursor IDE:"
echo "1. Open Cursor Settings"
echo "2. Go to 'Models' section"
echo "3. Add custom model:"
echo "   - API Base: http://localhost:1234/v1"
echo "   - API Key: lm-studio"
echo "   - Model: (auto-detected)"
