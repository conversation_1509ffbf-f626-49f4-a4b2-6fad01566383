#!/usr/bin/env node

/**
 * Security Audit for CryptoAgent Pro
 * Checks API key security, environment variables, and sensitive data handling
 */

const fs = require('fs')
const path = require('path')
const crypto = require('crypto')

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

class SecurityAudit {
  constructor() {
    this.issues = []
    this.warnings = []
    this.passed = []
  }

  log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`)
  }

  addIssue(category, message, severity = 'medium') {
    this.issues.push({ category, message, severity })
  }

  addWarning(category, message) {
    this.warnings.push({ category, message })
  }

  addPassed(category, message) {
    this.passed.push({ category, message })
  }

  checkEnvironmentFiles() {
    this.log('\n🔍 Checking Environment Files...', colors.cyan + colors.bright)
    
    const envFiles = ['.env', '.env.local', '.env.example']
    
    envFiles.forEach(file => {
      const filePath = path.join(process.cwd(), file)
      
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8')
        
        // Check for exposed API keys
        const apiKeyPatterns = [
          /API_KEY\s*=\s*[a-zA-Z0-9]{20,}/,
          /SECRET_KEY\s*=\s*[a-zA-Z0-9-]{20,}/,
          /PASSPHRASE\s*=\s*\w+/
        ]
        
        let hasRealKeys = false
        apiKeyPatterns.forEach(pattern => {
          if (pattern.test(content) && !content.includes('your_') && !content.includes('placeholder')) {
            hasRealKeys = true
          }
        })
        
        if (file === '.env.local' && hasRealKeys) {
          this.addPassed('Environment', `${file} contains real API keys (correct for local development)`)
        } else if (file === '.env' && hasRealKeys) {
          this.addIssue('Environment', `${file} contains real API keys (should use placeholders)`, 'high')
        } else if (file === '.env.example' && hasRealKeys) {
          this.addIssue('Environment', `${file} contains real API keys (should use examples only)`, 'high')
        } else {
          this.addPassed('Environment', `${file} properly configured`)
        }
        
        this.log(`  📄 ${file}: Found`, colors.green)
      } else {
        if (file === '.env.local') {
          this.addWarning('Environment', `${file} not found - API keys may not be configured`)
        }
        this.log(`  📄 ${file}: Not found`, colors.yellow)
      }
    })
  }

  checkGitIgnore() {
    this.log('\n🚫 Checking .gitignore Configuration...', colors.cyan + colors.bright)
    
    const gitignorePath = path.join(process.cwd(), '.gitignore')
    
    if (fs.existsSync(gitignorePath)) {
      const content = fs.readFileSync(gitignorePath, 'utf8')
      
      const requiredEntries = ['.env.local', '.env', '*.env']
      const missingEntries = []
      
      requiredEntries.forEach(entry => {
        if (!content.includes(entry)) {
          missingEntries.push(entry)
        }
      })
      
      if (missingEntries.length === 0) {
        this.addPassed('Git', '.gitignore properly configured for environment files')
        this.log('  ✅ .gitignore properly protects environment files', colors.green)
      } else {
        this.addIssue('Git', `Missing .gitignore entries: ${missingEntries.join(', ')}`, 'high')
        this.log(`  ❌ Missing entries: ${missingEntries.join(', ')}`, colors.red)
      }
    } else {
      this.addIssue('Git', '.gitignore file not found', 'medium')
      this.log('  ❌ .gitignore file not found', colors.red)
    }
  }

  checkCodeForHardcodedSecrets() {
    this.log('\n🔍 Scanning Code for Hardcoded Secrets...', colors.cyan + colors.bright)
    
    const scanDirectories = ['src', 'lib', 'components', 'scripts']
    const secretPatterns = [
      { pattern: /[a-zA-Z0-9]{32,}/, name: 'Potential API Key' },
      { pattern: /sk-[a-zA-Z0-9]{48}/, name: 'OpenAI API Key' },
      { pattern: /xoxb-[a-zA-Z0-9-]+/, name: 'Slack Token' },
      { pattern: /ghp_[a-zA-Z0-9]{36}/, name: 'GitHub Token' }
    ]
    
    let filesScanned = 0
    let issuesFound = 0
    
    const scanFile = (filePath) => {
      if (!filePath.endsWith('.js') && !filePath.endsWith('.ts') && !filePath.endsWith('.tsx')) {
        return
      }
      
      try {
        const content = fs.readFileSync(filePath, 'utf8')
        filesScanned++
        
        secretPatterns.forEach(({ pattern, name }) => {
          const matches = content.match(pattern)
          if (matches && !content.includes('process.env') && !content.includes('placeholder')) {
            this.addIssue('Code', `Potential hardcoded secret in ${filePath}: ${name}`, 'high')
            issuesFound++
          }
        })
      } catch (error) {
        // Skip files that can't be read
      }
    }
    
    const scanDirectory = (dir) => {
      const fullPath = path.join(process.cwd(), dir)
      if (!fs.existsSync(fullPath)) return
      
      const items = fs.readdirSync(fullPath, { withFileTypes: true })
      
      items.forEach(item => {
        const itemPath = path.join(fullPath, item.name)
        
        if (item.isDirectory() && !item.name.startsWith('.') && item.name !== 'node_modules') {
          scanDirectory(path.relative(process.cwd(), itemPath))
        } else if (item.isFile()) {
          scanFile(itemPath)
        }
      })
    }
    
    scanDirectories.forEach(dir => scanDirectory(dir))
    
    this.log(`  📊 Scanned ${filesScanned} files`, colors.blue)
    
    if (issuesFound === 0) {
      this.addPassed('Code', 'No hardcoded secrets found in source code')
      this.log('  ✅ No hardcoded secrets detected', colors.green)
    } else {
      this.log(`  ❌ Found ${issuesFound} potential security issues`, colors.red)
    }
  }

  checkAPIKeyStrength() {
    this.log('\n🔐 Checking API Key Strength...', colors.cyan + colors.bright)
    
    const envPath = path.join(process.cwd(), '.env.local')
    
    if (fs.existsSync(envPath)) {
      const content = fs.readFileSync(envPath, 'utf8')
      const lines = content.split('\n')
      
      lines.forEach(line => {
        if (line.includes('API_KEY=') || line.includes('SECRET_KEY=')) {
          const [key, value] = line.split('=')
          
          if (value && value.length > 10 && !value.includes('your_')) {
            const entropy = this.calculateEntropy(value)
            
            if (entropy > 4.0) {
              this.addPassed('Crypto', `${key} has good entropy (${entropy.toFixed(2)})`)
            } else {
              this.addWarning('Crypto', `${key} has low entropy (${entropy.toFixed(2)}) - consider regenerating`)
            }
          }
        }
      })
    }
  }

  calculateEntropy(str) {
    const freq = {}
    str.split('').forEach(char => {
      freq[char] = (freq[char] || 0) + 1
    })
    
    const len = str.length
    return Object.values(freq).reduce((entropy, count) => {
      const p = count / len
      return entropy - p * Math.log2(p)
    }, 0)
  }

  checkPermissions() {
    this.log('\n🔒 Checking File Permissions...', colors.cyan + colors.bright)
    
    const sensitiveFiles = ['.env.local', '.env']
    
    sensitiveFiles.forEach(file => {
      const filePath = path.join(process.cwd(), file)
      
      if (fs.existsSync(filePath)) {
        try {
          const stats = fs.statSync(filePath)
          const mode = stats.mode & parseInt('777', 8)
          
          // Check if file is readable by others
          if (mode & parseInt('044', 8)) {
            this.addWarning('Permissions', `${file} is readable by others (mode: ${mode.toString(8)})`)
          } else {
            this.addPassed('Permissions', `${file} has secure permissions`)
          }
        } catch (error) {
          this.addWarning('Permissions', `Could not check permissions for ${file}`)
        }
      }
    })
  }

  checkDependencyVulnerabilities() {
    this.log('\n📦 Checking for Known Vulnerabilities...', colors.cyan + colors.bright)
    
    const packageJsonPath = path.join(process.cwd(), 'package.json')
    
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
      const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }
      
      // Check for known vulnerable packages (simplified check)
      const knownVulnerable = [
        'lodash@4.17.15',
        'minimist@1.2.0',
        'serialize-javascript@3.1.0'
      ]
      
      let vulnerableFound = false
      
      Object.entries(dependencies).forEach(([pkg, version]) => {
        const pkgVersion = `${pkg}@${version.replace('^', '').replace('~', '')}`
        
        if (knownVulnerable.some(vuln => pkgVersion.startsWith(vuln))) {
          this.addIssue('Dependencies', `Potentially vulnerable package: ${pkgVersion}`, 'medium')
          vulnerableFound = true
        }
      })
      
      if (!vulnerableFound) {
        this.addPassed('Dependencies', 'No known vulnerable packages detected')
      }
      
      this.log(`  📊 Checked ${Object.keys(dependencies).length} dependencies`, colors.blue)
    }
  }

  generateSecurityReport() {
    this.log('\n📊 SECURITY AUDIT REPORT', colors.magenta + colors.bright)
    this.log('=' * 50, colors.magenta)

    const totalChecks = this.issues.length + this.warnings.length + this.passed.length
    const criticalIssues = this.issues.filter(i => i.severity === 'high').length
    const mediumIssues = this.issues.filter(i => i.severity === 'medium').length

    this.log(`\n🎯 Security Score: ${this.passed.length}/${totalChecks} checks passed`, 
      criticalIssues === 0 ? colors.green + colors.bright : colors.yellow + colors.bright)

    if (criticalIssues > 0) {
      this.log(`\n🚨 CRITICAL ISSUES (${criticalIssues}):`, colors.red + colors.bright)
      this.issues.filter(i => i.severity === 'high').forEach(issue => {
        this.log(`   ❌ [${issue.category}] ${issue.message}`, colors.red)
      })
    }

    if (mediumIssues > 0) {
      this.log(`\n⚠️  MEDIUM ISSUES (${mediumIssues}):`, colors.yellow + colors.bright)
      this.issues.filter(i => i.severity === 'medium').forEach(issue => {
        this.log(`   ⚠️  [${issue.category}] ${issue.message}`, colors.yellow)
      })
    }

    if (this.warnings.length > 0) {
      this.log(`\n💡 WARNINGS (${this.warnings.length}):`, colors.yellow + colors.bright)
      this.warnings.forEach(warning => {
        this.log(`   💡 [${warning.category}] ${warning.message}`, colors.yellow)
      })
    }

    this.log(`\n✅ PASSED CHECKS (${this.passed.length}):`, colors.green + colors.bright)
    this.passed.forEach(check => {
      this.log(`   ✅ [${check.category}] ${check.message}`, colors.green)
    })

    this.log('\n🚀 RECOMMENDATIONS:', colors.blue + colors.bright)
    if (criticalIssues === 0 && mediumIssues === 0) {
      this.log('   🎉 Excellent! No critical security issues found', colors.green)
      this.log('   🔒 Your API key management is secure', colors.green)
      this.log('   ✅ Ready for production deployment', colors.green)
    } else {
      this.log('   🔧 Fix critical issues before production deployment', colors.yellow)
      this.log('   🔐 Review API key storage and access patterns', colors.yellow)
      this.log('   📊 Consider implementing additional security monitoring', colors.yellow)
    }
  }

  async runSecurityAudit() {
    this.log('🚀 Starting Security Audit for CryptoAgent Pro', colors.magenta + colors.bright)
    this.log('=' * 60, colors.magenta)

    this.checkEnvironmentFiles()
    this.checkGitIgnore()
    this.checkCodeForHardcodedSecrets()
    this.checkAPIKeyStrength()
    this.checkPermissions()
    this.checkDependencyVulnerabilities()

    this.generateSecurityReport()
  }
}

// Run the audit
if (require.main === module) {
  const audit = new SecurityAudit()
  audit.runSecurityAudit().catch(error => {
    console.error('❌ Security audit failed:', error)
    process.exit(1)
  })
}

module.exports = SecurityAudit
