#!/usr/bin/env node

/**
 * CryptoAgent Pro - Verbeterde Supabase Database Setup
 * Oplossing voor credentials en database schema problemen
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')
const dotenv = require('dotenv')
const readline = require('readline')

// Load environment variables
const envPath = path.join(__dirname, '../.env.local')
dotenv.config({ path: envPath })

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

class CryptoAgentSupabaseSetup {
  constructor() {
    this.supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    this.supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    this.supabase = null
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    })
  }

  async promptForCredentials() {
    log('\n🔐 Supabase Credentials Setup', colors.cyan + colors.bright)
    log('=' * 40, colors.cyan)

    if (!this.supabaseUrl) {
      this.supabaseUrl = await this.askQuestion('Enter your Supabase URL: ')
    }

    if (!this.supabaseKey) {
      this.supabaseKey = await this.askQuestion('Enter your Supabase Service Role Key (or Anon Key): ')
    }

    // Save to .env.local
    await this.saveCredentials()
  }

  async askQuestion(question) {
    return new Promise((resolve) => {
      this.rl.question(question, (answer) => {
        resolve(answer.trim())
      })
    })
  }

  async saveCredentials() {
    try {
      let envContent = fs.readFileSync(this.envPath, 'utf8')
      
      // Update existing values or add new ones
      if (envContent.includes('NEXT_PUBLIC_SUPABASE_URL=')) {
        envContent = envContent.replace(/NEXT_PUBLIC_SUPABASE_URL=.*/, `NEXT_PUBLIC_SUPABASE_URL=${this.supabaseUrl}`)
      } else {
        envContent += `\nNEXT_PUBLIC_SUPABASE_URL=${this.supabaseUrl}`
      }
      
      if (envContent.includes('SUPABASE_SERVICE_ROLE_KEY=')) {
        envContent = envContent.replace(/SUPABASE_SERVICE_ROLE_KEY=.*/, `SUPABASE_SERVICE_ROLE_KEY=${this.supabaseKey}`)
      } else {
        envContent += `\nSUPABASE_SERVICE_ROLE_KEY=${this.supabaseKey}`
      }

      fs.writeFileSync(this.envPath, envContent)
      log('✅ Credentials saved to .env.local', colors.green)
    } catch (error) {
      log(`❌ Failed to save credentials: ${error.message}`, colors.red)
    }
  }

  async initialize() {
    log('🚀 Initializing CryptoAgent Pro Supabase Setup...', colors.cyan + colors.bright)
    
    if (!this.supabaseUrl || !this.supabaseKey) {
      log('⚠️  Missing Supabase credentials', colors.yellow)
      await this.promptForCredentials()
    }

    if (!this.supabaseUrl || !this.supabaseKey) {
      log('❌ Still missing credentials. Cannot continue.', colors.red)
      return false
    }

    try {
      this.supabase = createClient(this.supabaseUrl, this.supabaseKey, {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      })
      log('✅ Supabase client initialized', colors.green)
      return true
    } catch (error) {
      log(`❌ Failed to initialize Supabase: ${error.message}`, colors.red)
      return false
    }
  }

  async testConnection() {
    log('\n🧪 Testing Database Connection...', colors.cyan + colors.bright)

    try {
      // Test basic connection
      const { data, error } = await this.supabase
        .from('profiles')
        .select('count')
        .limit(0)

      if (error && error.code !== 'PGRST116') { // PGRST116 = table doesn't exist (which is expected)
        throw error
      }

      log('✅ Database connection successful', colors.green)
      return true
    } catch (error) {
      log(`❌ Connection test failed: ${error.message}`, colors.red)
      
      if (error.message.includes('Invalid API key')) {
        log('💡 Try using your Service Role Key instead of Anon Key', colors.yellow)
      }
      
      return false
    }
  }

  async executeDatabaseSchema() {
    log('\n📊 Setting up Database Schema...', colors.cyan + colors.bright)

    const schemaPath = path.join(__dirname, '../database-schema.sql')
    
    // Create schema file if it doesn't exist
    if (!fs.existsSync(schemaPath)) {
      log('📝 Creating database schema file...', colors.blue)
      
      const schemaContent = await this.generateSchemaSQL()
      fs.writeFileSync(schemaPath, schemaContent)
      log('✅ Schema file created: database-schema.sql', colors.green)
    }

    log('\n📋 MANUAL SCHEMA SETUP REQUIRED', colors.magenta + colors.bright)
    log('=' * 50, colors.magenta)
    
    log('\n1. Open Supabase Dashboard:', colors.blue)
    log(`   ${this.supabaseUrl}/project/default/sql`, colors.blue)
    
    log('\n2. Copy and paste the SQL from:', colors.blue)
    log(`   ${schemaPath}`, colors.green)
    
    log('\n3. Execute the SQL in the SQL Editor', colors.blue)
    
    log('\n4. Verify tables were created:', colors.blue)
    const tables = ['profiles', 'trading_sessions', 'trades', 'portfolios', 'alerts']
    tables.forEach(table => log(`   - ${table}`, colors.green))

    return true
  }

  async generateSchemaSQL() {
    return `-- CryptoAgent Pro Database Schema
-- Execute this SQL in your Supabase SQL Editor

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create basic tables
CREATE TABLE IF NOT EXISTS public.profiles (
    id uuid REFERENCES auth.users(id) PRIMARY KEY,
    email text UNIQUE NOT NULL,
    name text,
    role text DEFAULT 'USER',
    settings jsonb DEFAULT '{}',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.trading_sessions (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    session_name text NOT NULL,
    exchange text NOT NULL,
    status text DEFAULT 'ACTIVE',
    config jsonb DEFAULT '{}',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.trades (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    session_id uuid REFERENCES public.trading_sessions(id),
    exchange text NOT NULL,
    symbol text NOT NULL,
    side text NOT NULL CHECK (side IN ('buy', 'sell')),
    amount decimal(18,8) NOT NULL,
    price decimal(18,8) NOT NULL,
    fee decimal(18,8) DEFAULT 0,
    status text DEFAULT 'completed',
    order_id text,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.portfolios (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name text NOT NULL,
    exchange text NOT NULL,
    total_value decimal(18,8) DEFAULT 0,
    holdings jsonb DEFAULT '{}',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.alerts (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    type text NOT NULL,
    symbol text,
    condition_type text NOT NULL,
    condition_value decimal(18,8),
    message text NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trading_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.alerts ENABLE ROW LEVEL SECURITY;

-- Basic policies
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users manage own trading sessions" ON public.trading_sessions
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users manage own trades" ON public.trades
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users manage own portfolios" ON public.portfolios
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users manage own alerts" ON public.alerts
    FOR ALL USING (auth.uid() = user_id);

-- Function to auto-create profile
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
    INSERT INTO public.profiles (id, email, name)
    VALUES (new.id, new.email, COALESCE(new.raw_user_meta_data->>'name', new.email));
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new users
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Setup complete!
`
  }

  async verifySetup() {
    log('\n✅ Verifying Database Setup...', colors.cyan + colors.bright)

    const tablesToCheck = [
      'profiles',
      'trading_sessions',
      'trades',
      'portfolios',
      'alerts'
    ]

    let allTablesExist = true

    for (const table of tablesToCheck) {
      try {
        const { error } = await this.supabase
          .from(table)
          .select('count')
          .limit(0)

        if (error && error.code === 'PGRST116') {
          log(`❌ Table '${table}' does not exist`, colors.red)
          allTablesExist = false
        } else if (error) {
          log(`⚠️  Table '${table}': ${error.message}`, colors.yellow)
        } else {
          log(`✅ Table '${table}' exists`, colors.green)
        }
      } catch (error) {
        log(`❌ Error checking table '${table}': ${error.message}`, colors.red)
        allTablesExist = false
      }
    }

    return allTablesExist
  }

  async createNextJSIntegration() {
    log('\n⚛️  Creating Next.js Integration Files...', colors.cyan + colors.bright)

    const libDir = path.join(__dirname, '../lib')
    if (!fs.existsSync(libDir)) {
      fs.mkdirSync(libDir, { recursive: true })
    }

    // Create Supabase client file
    const supabaseClientContent = `import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Type definitions
export interface Profile {
  id: string
  email: string
  name?: string
  role: 'USER' | 'ADMIN' | 'PRO'
  settings: any
  created_at: string
  updated_at: string
}

export interface TradingSession {
  id: string
  user_id: string
  session_name: string
  exchange: string
  status: 'ACTIVE' | 'PAUSED' | 'STOPPED' | 'COMPLETED'
  config: any
  created_at: string
  updated_at: string
}

export interface Trade {
  id: string
  user_id: string
  session_id?: string
  exchange: string
  symbol: string
  side: 'buy' | 'sell'
  amount: number
  price: number
  fee: number
  status: string
  order_id?: string
  created_at: string
}`

    fs.writeFileSync(path.join(libDir, 'supabase.ts'), supabaseClientContent)
    log('✅ Created lib/supabase.ts', colors.green)

    return true
  }

  async generateAPIRoutes() {
    log('\n🛠️  Generating API Routes...', colors.cyan + colors.bright)

    const apiDir = path.join(__dirname, '../src/app/api')
    if (!fs.existsSync(apiDir)) {
      fs.mkdirSync(apiDir, { recursive: true })
    }

    // Create test API route
    const testAPIContent = `import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function GET() {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Test database connection
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1)

    if (error && error.code !== 'PGRST116') {
      throw error
    }

    return NextResponse.json({
      success: true,
      message: 'Supabase connection successful!',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}`

    const testDir = path.join(apiDir, 'test-supabase')
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true })
    }

    fs.writeFileSync(path.join(testDir, 'route.ts'), testAPIContent)
    log('✅ Created src/app/api/test-supabase/route.ts', colors.green)

    return true
  }

  async run() {
    log('🗄️  CryptoAgent Pro - Supabase Database Setup', colors.magenta + colors.bright)
    log('=' * 50, colors.magenta)

    try {
      // Step 1: Initialize
      const initialized = await this.initialize()
      if (!initialized) {
        log('❌ Setup failed during initialization', colors.red)
        this.rl.close()
        return
      }

      // Step 2: Test connection
      const connected = await this.testConnection()
      if (!connected) {
        log('❌ Setup failed during connection test', colors.red)
        this.rl.close()
        return
      }

      // Step 3: Database schema
      await this.executeDatabaseSchema()

      // Step 4: Create integration files
      await this.createNextJSIntegration()
      await this.generateAPIRoutes()

      // Step 5: Final instructions
      log('\n🎉 Setup Process Completed!', colors.green + colors.bright)
      log('=' * 40, colors.green)

      log('\n📋 NEXT STEPS:', colors.blue + colors.bright)
      log('1. Execute the SQL schema in Supabase Dashboard', colors.blue)
      log('2. Test your setup: npm run dev', colors.blue)
      log('3. Visit: http://localhost:3000/api/test-supabase', colors.blue)
      log('4. Check browser console for any errors', colors.blue)

      log('\n💡 TROUBLESHOOTING:', colors.yellow + colors.bright)
      log('- If tables already exist, that\'s fine!', colors.yellow)
      log('- Service Role Key is preferred over Anon Key', colors.yellow)
      log('- Make sure .env.local is in your .gitignore', colors.yellow)

      this.rl.close()

    } catch (error) {
      log(`❌ Setup failed: ${error.message}`, colors.red)
      this.rl.close()
      process.exit(1)
    }
  }
}

// Run the setup
if (require.main === module) {
  const setup = new CryptoAgentSupabaseSetup()
  setup.run()
}

module.exports = CryptoAgentSupabaseSetup
