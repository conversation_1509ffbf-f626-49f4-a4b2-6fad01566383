#!/usr/bin/env node

/**
 * Quick Supabase Status Checker
 * Shows current configuration and identifies issues
 */

const fs = require('fs')
const path = require('path')
const dotenv = require('dotenv')

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') })

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

function checkSupabaseStatus() {
  log('🔍 Supabase Configuration Status Check', colors.cyan + colors.bright)
  log('=' * 45, colors.cyan)

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  log('\n📊 Current Configuration:', colors.blue + colors.bright)
  
  // Check URL
  if (supabaseUrl) {
    log(`✅ Supabase URL: ${supabaseUrl}`, colors.green)
  } else {
    log('❌ Supabase URL: Missing', colors.red)
  }

  // Check Anon Key
  if (anonKey) {
    log(`✅ Anon Key: ${anonKey.substring(0, 20)}...`, colors.green)
  } else {
    log('❌ Anon Key: Missing', colors.red)
  }

  // Check Service Role Key
  if (serviceKey) {
    if (serviceKey.includes('your_service_role_key_here')) {
      log('❌ Service Role Key: Placeholder (not set)', colors.red)
    } else if (serviceKey === anonKey) {
      log('❌ Service Role Key: Same as Anon Key (WRONG!)', colors.red)
    } else {
      log(`✅ Service Role Key: ${serviceKey.substring(0, 20)}... (looks good)`, colors.green)
    }
  } else {
    log('❌ Service Role Key: Missing', colors.red)
  }

  log('\n🔍 Issues Detected:', colors.yellow + colors.bright)
  
  const issues = []
  
  if (!supabaseUrl) issues.push('Missing Supabase URL')
  if (!anonKey) issues.push('Missing Anon Key')
  if (!serviceKey || serviceKey.includes('your_service_role_key_here')) {
    issues.push('Service Role Key not set')
  }
  if (serviceKey === anonKey) {
    issues.push('Service Role Key is same as Anon Key')
  }

  if (issues.length === 0) {
    log('🎉 No issues detected! Configuration looks good.', colors.green)
  } else {
    issues.forEach((issue, index) => {
      log(`${index + 1}. ${issue}`, colors.red)
    })
  }

  log('\n🔧 Solutions:', colors.blue + colors.bright)
  
  if (issues.length > 0) {
    log('Run this to fix the main issue:', colors.blue)
    log('node scripts/fix-supabase-key.js', colors.green)
    
    log('\nOr manually:', colors.blue)
    log('1. Go to: https://supabase.com/dashboard/project/zcyqjnbtojltgymtqjnp/settings/api', colors.blue)
    log('2. Copy the SERVICE_ROLE key (not anon key)', colors.blue)
    log('3. Update SUPABASE_SERVICE_ROLE_KEY in .env.local', colors.blue)
  } else {
    log('Configuration is ready! Next steps:', colors.blue)
    log('1. Run: node scripts/setup-supabase-advanced.js', colors.green)
    log('2. Execute database schema in Supabase dashboard', colors.green)
    log('3. Test your setup!', colors.green)
  }

  log('\n📚 Helpful Links:', colors.magenta + colors.bright)
  log('- Supabase Dashboard: https://supabase.com/dashboard/project/wwchpqroenvomcvkecuh', colors.magenta)
  log('- API Settings: https://supabase.com/dashboard/project/wwchpqroenvomcvkecuh/settings/api', colors.magenta)
  log('- SQL Editor: https://supabase.com/dashboard/project/wwchpqroenvomcvkecuh/sql', colors.magenta)
}

// Run the check
if (require.main === module) {
  checkSupabaseStatus()
}

module.exports = checkSupabaseStatus
