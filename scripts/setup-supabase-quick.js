#!/usr/bin/env node

/**
 * Quick Supabase Setup Helper
 * Guides you through the setup process
 */

const readline = require('readline')
const fs = require('fs')
const path = require('path')

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim())
    })
  })
}

class SupabaseQuickSetup {
  constructor() {
    this.envPath = path.join(__dirname, '../.env.local')
  }

  async run() {
    log('🗄️  Supabase Quick Setup voor CryptoAgent Pro', colors.magenta + colors.bright)
    log('=' * 50, colors.magenta)

    log('\n📋 Je hebt al een Supabase project:', colors.blue)
    log('   Project: zcyqjnbtojltgymtqjnp', colors.green)
    log('   URL: https://zcyqjnbtojltgymtqjnp.supabase.co', colors.green)

    log('\n🔑 We gaan je Service Role Key updaten...', colors.cyan)
    
    const needsServiceKey = await askQuestion('\nHeb je je Service Role Key al gekopieerd van Supabase? (y/n): ')
    
    if (needsServiceKey.toLowerCase() !== 'y') {
      log('\n📋 Volg deze stappen:', colors.yellow)
      log('1. Open: https://supabase.com/dashboard/project/zcyqjnbtojltgymtqjnp/settings/api', colors.yellow)
      log('2. Kopieer de "service_role" key (NIET de anon key!)', colors.yellow)
      log('3. Kom terug naar dit script', colors.yellow)
      
      await askQuestion('\nDruk Enter als je de key hebt gekopieerd...')
    }

    const serviceKey = await askQuestion('\nPlak je Service Role Key hier: ')
    
    if (serviceKey && serviceKey.length > 50 && serviceKey.startsWith('eyJ')) {
      await this.updateEnvFile(serviceKey)
      log('\n✅ Service Role Key succesvol geüpdatet!', colors.green)
    } else {
      log('\n❌ Ongeldige key. Zorg dat je de juiste service_role key kopieert.', colors.red)
      process.exit(1)
    }

    log('\n🗃️  Database Schema Setup...', colors.cyan)
    const setupDatabase = await askQuestion('Wil je de database schema automatisch instellen? (y/n): ')
    
    if (setupDatabase.toLowerCase() === 'y') {
      await this.showDatabaseInstructions()
    }

    log('\n🧪 Test de verbinding...', colors.cyan)
    const testNow = await askQuestion('Wil je de Supabase verbinding nu testen? (y/n): ')
    
    if (testNow.toLowerCase() === 'y') {
      await this.runTest()
    }

    this.showNextSteps()
    rl.close()
  }

  async updateEnvFile(serviceKey) {
    try {
      let envContent = fs.readFileSync(this.envPath, 'utf8')
      
      // Replace the service role key
      envContent = envContent.replace(
        /SUPABASE_SERVICE_ROLE_KEY=.*/,
        `SUPABASE_SERVICE_ROLE_KEY=${serviceKey}`
      )
      
      fs.writeFileSync(this.envPath, envContent)
      log('📝 .env.local bestand geüpdatet', colors.green)
    } catch (error) {
      log(`❌ Fout bij updaten .env.local: ${error.message}`, colors.red)
    }
  }

  async showDatabaseInstructions() {
    log('\n📋 Database Schema Setup:', colors.blue + colors.bright)
    log('1. Open: https://supabase.com/dashboard/project/zcyqjnbtojltgymtqjnp/sql/new', colors.blue)
    log('2. Kopieer de SQL van: docs/supabase-setup-guide.md', colors.blue)
    log('3. Plak en voer uit in de SQL Editor', colors.blue)
    log('4. Herhaal voor de RLS policies', colors.blue)
    
    await askQuestion('\nDruk Enter als je de database schema hebt ingesteld...')
  }

  async runTest() {
    log('\n🧪 Running Supabase test...', colors.cyan)
    
    const { spawn } = require('child_process')
    
    return new Promise((resolve) => {
      const test = spawn('node', ['scripts/test-supabase.js'], {
        cwd: path.join(__dirname, '..'),
        stdio: 'inherit'
      })
      
      test.on('close', (code) => {
        if (code === 0) {
          log('\n✅ Supabase test succesvol!', colors.green)
        } else {
          log('\n⚠️  Test had issues. Check de output hierboven.', colors.yellow)
        }
        resolve()
      })
    })
  }

  showNextSteps() {
    log('\n🚀 VOLGENDE STAPPEN:', colors.blue + colors.bright)
    log('=' * 30, colors.blue)
    
    log('\n1. 🗃️  Database Schema (als nog niet gedaan):', colors.blue)
    log('   - Open SQL Editor in Supabase', colors.blue)
    log('   - Voer schema uit van docs/supabase-setup-guide.md', colors.blue)
    
    log('\n2. 🔒 Row Level Security:', colors.blue)
    log('   - Voer RLS policies uit (zie guide)', colors.blue)
    
    log('\n3. 🧪 Test alles:', colors.blue)
    log('   node scripts/test-supabase.js', colors.green)
    
    log('\n4. 🎯 Integreer met je app:', colors.blue)
    log('   - User registration/login', colors.blue)
    log('   - Trading data opslag', colors.blue)
    log('   - AI analysis results', colors.blue)
    
    log('\n📚 Complete guide: docs/supabase-setup-guide.md', colors.cyan)
    log('\n🎉 Supabase setup voltooid!', colors.green + colors.bright)
  }
}

// Run the setup
if (require.main === module) {
  const setup = new SupabaseQuickSetup()
  setup.run().catch(error => {
    console.error('❌ Setup failed:', error)
    process.exit(1)
  })
}

module.exports = SupabaseQuickSetup
