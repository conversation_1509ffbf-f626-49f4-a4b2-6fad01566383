#!/usr/bin/env node

/**
 * CryptoAgent Pro - Startup Script
 * Starts all services in the correct order
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting CryptoAgent Pro...');

// Start Next.js development server
const nextProcess = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  cwd: process.cwd()
});

// Start trading engine (if configured)
setTimeout(() => {
  console.log('🤖 Starting AI Trading Engine...');
  // This will be implemented when the trading engine is ready
}, 5000);

process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down CryptoAgent Pro...');
  nextProcess.kill();
  process.exit(0);
});
