#!/usr/bin/env node

/**
 * CryptoAgent Pro - Trading Engine Starter
 * 
 * This script initializes and starts the AI trading engine with:
 * - Multiple specialized AI agents
 * - Real-time market data feeds
 * - Risk management systems
 * - Portfolio optimization
 */

require('dotenv').config();
const path = require('path');

// Import our trading components
const TradingOrchestrator = require('../lib/trading/TradingOrchestrator');
const AgentOrchestrator = require('../lib/agents/AgentOrchestrator');
const ExchangeManager = require('../lib/exchanges/ExchangeManager');

console.log('🤖 CryptoAgent Pro - AI Trading Engine Starting...\n');

class TradingEngineStarter {
  constructor() {
    this.orchestrator = null;
    this.agentOrchestrator = null;
    this.exchangeManager = null;
    this.isRunning = false;
  }

  async start() {
    try {
      console.log('🔧 Initializing Trading Engine Components...');
      
      // 1. Initialize Exchange Manager
      await this.initializeExchangeManager();
      
      // 2. Initialize Agent Orchestrator
      await this.initializeAgentOrchestrator();
      
      // 3. Initialize Trading Orchestrator
      await this.initializeTradingOrchestrator();
      
      // 4. Start all systems
      await this.startAllSystems();
      
      // 5. Setup monitoring and health checks
      this.setupMonitoring();
      
      console.log('✅ CryptoAgent Pro Trading Engine is now LIVE!');
      console.log('🚀 AI Agents are analyzing markets and ready to trade...\n');
      
      this.isRunning = true;
      
    } catch (error) {
      console.error('❌ Failed to start trading engine:', error.message);
      process.exit(1);
    }
  }

  async initializeExchangeManager() {
    console.log('📈 Setting up Exchange Connections...');
    
    this.exchangeManager = new ExchangeManager();
    
    // Add configured exchanges
    const exchanges = this.getConfiguredExchanges();
    
    for (const exchange of exchanges) {
      try {
        await this.exchangeManager.addExchange(exchange.name, exchange.config);
        console.log(`✅ Connected to ${exchange.name}`);
      } catch (error) {
        console.log(`⚠️  Failed to connect to ${exchange.name}: ${error.message}`);
      }
    }
    
    if (this.exchangeManager.getActiveExchanges().length === 0) {
      throw new Error('No exchanges connected. Please check your API keys.');
    }
  }

  async initializeAgentOrchestrator() {
    console.log('🤖 Initializing AI Agents...');
    
    this.agentOrchestrator = new AgentOrchestrator();
    
    // Create specialized agents
    const agents = [
      {
        name: 'Technical Analyst',
        type: 'technical_analysis',
        model: process.env.LM_STUDIO_MODEL || 'gpt-4',
        specialization: 'Chart patterns, indicators, price action'
      },
      {
        name: 'Sentiment Monitor',
        type: 'sentiment_analysis',
        model: process.env.LM_STUDIO_MODEL || 'gpt-4',
        specialization: 'Social media, news, market sentiment'
      },
      {
        name: 'Risk Manager',
        type: 'risk_management',
        model: process.env.LM_STUDIO_MODEL || 'gpt-4',
        specialization: 'Portfolio risk, position sizing'
      },
      {
        name: 'Market Scanner',
        type: 'market_scanning',
        model: process.env.LM_STUDIO_MODEL || 'gpt-4',
        specialization: 'Opportunity detection, market screening'
      }
    ];

    for (const agentConfig of agents) {
      try {
        await this.agentOrchestrator.createAgent(agentConfig);
        console.log(`✅ Created ${agentConfig.name}`);
      } catch (error) {
        console.log(`⚠️  Failed to create ${agentConfig.name}: ${error.message}`);
      }
    }
  }

  async initializeTradingOrchestrator() {
    console.log('⚙️  Setting up Trading Orchestrator...');
    
    this.orchestrator = new TradingOrchestrator({
      exchangeManager: this.exchangeManager,
      agentOrchestrator: this.agentOrchestrator,
      riskManagement: {
        maxPortfolioRisk: parseFloat(process.env.MAX_PORTFOLIO_RISK) || 20,
        maxPositionSize: parseFloat(process.env.MAX_POSITION_SIZE) || 5,
        maxDailyLoss: parseFloat(process.env.MAX_DAILY_LOSS) || 1000,
        stopLossPercent: parseFloat(process.env.STOP_LOSS_PERCENT) || 2,
        takeProfitPercent: parseFloat(process.env.TAKE_PROFIT_PERCENT) || 4
      },
      tradingMode: process.env.TRADING_MODE || 'paper',
      sandboxMode: process.env.SANDBOX_MODE === 'true'
    });
    
    console.log('✅ Trading Orchestrator initialized');
  }

  async startAllSystems() {
    console.log('🚀 Starting All Trading Systems...');
    
    // Start the main trading loop
    await this.orchestrator.start();
    
    // Start agent analysis loops
    await this.agentOrchestrator.startAll();
    
    // Start real-time data feeds
    this.startDataFeeds();
    
    console.log('✅ All systems started successfully');
  }

  startDataFeeds() {
    console.log('📊 Starting Real-time Data Feeds...');
    
    // Start price feeds for major pairs
    const tradingPairs = (process.env.CUSTOM_TRADING_PAIRS || 'BTC/USDT,ETH/USDT,ADA/USDT').split(',');
    
    for (const pair of tradingPairs) {
      this.exchangeManager.subscribeToPriceUpdates(pair.trim(), (data) => {
        // Forward price updates to agents and trading engine
        this.agentOrchestrator.onPriceUpdate(pair.trim(), data);
        this.orchestrator.onPriceUpdate(pair.trim(), data);
      });
    }
    
    console.log(`✅ Subscribed to ${tradingPairs.length} trading pairs`);
  }

  setupMonitoring() {
    console.log('📊 Setting up System Monitoring...');
    
    // Health check interval
    setInterval(() => {
      this.performHealthCheck();
    }, 60000); // Every minute
    
    // Performance reporting
    setInterval(() => {
      this.reportPerformance();
    }, 300000); // Every 5 minutes
    
    console.log('✅ Monitoring systems active');
  }

  performHealthCheck() {
    const status = {
      timestamp: new Date().toISOString(),
      exchangeManager: this.exchangeManager?.isHealthy() || false,
      agentOrchestrator: this.agentOrchestrator?.isHealthy() || false,
      tradingOrchestrator: this.orchestrator?.isHealthy() || false,
      activeAgents: this.agentOrchestrator?.getActiveAgentCount() || 0,
      activeExchanges: this.exchangeManager?.getActiveExchanges().length || 0
    };
    
    if (process.env.DEBUG_MODE === 'true') {
      console.log('🔍 Health Check:', status);
    }
    
    // Log any issues
    if (!status.exchangeManager || !status.agentOrchestrator || !status.tradingOrchestrator) {
      console.log('⚠️  System health check detected issues');
    }
  }

  reportPerformance() {
    if (this.orchestrator) {
      const performance = this.orchestrator.getPerformanceMetrics();
      console.log('📊 Performance Update:', {
        totalTrades: performance.totalTrades,
        winRate: performance.winRate,
        totalPnL: performance.totalPnL,
        activePositions: performance.activePositions
      });
    }
  }

  getConfiguredExchanges() {
    const exchanges = [];
    
    // Binance
    if (process.env.BINANCE_API_KEY && process.env.BINANCE_SECRET_KEY) {
      exchanges.push({
        name: 'binance',
        config: {
          apiKey: process.env.BINANCE_API_KEY,
          secret: process.env.BINANCE_SECRET_KEY,
          sandbox: process.env.BINANCE_TESTNET === 'true'
        }
      });
    }
    
    // Bybit
    if (process.env.BYBIT_API_KEY && process.env.BYBIT_SECRET_KEY) {
      exchanges.push({
        name: 'bybit',
        config: {
          apiKey: process.env.BYBIT_API_KEY,
          secret: process.env.BYBIT_SECRET_KEY,
          sandbox: process.env.BYBIT_TESTNET === 'true'
        }
      });
    }
    
    return exchanges;
  }

  async stop() {
    console.log('🛑 Stopping Trading Engine...');
    
    if (this.orchestrator) {
      await this.orchestrator.stop();
    }
    
    if (this.agentOrchestrator) {
      await this.agentOrchestrator.stopAll();
    }
    
    if (this.exchangeManager) {
      await this.exchangeManager.disconnectAll();
    }
    
    this.isRunning = false;
    console.log('✅ Trading Engine stopped');
  }
}

// Start the trading engine
const engine = new TradingEngineStarter();

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received shutdown signal...');
  await engine.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received termination signal...');
  await engine.stop();
  process.exit(0);
});

// Start the engine
engine.start().catch(console.error);

module.exports = TradingEngineStarter;
