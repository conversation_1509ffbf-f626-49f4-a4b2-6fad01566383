#!/usr/bin/env node

/**
 * Comprehensive Exchange Connection Test Suite
 * Tests all configured exchanges with real API calls
 */

const ccxt = require('ccxt')
const dotenv = require('dotenv')
const path = require('path')

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') })

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

class ExchangeConnectionTester {
  constructor() {
    this.results = {}
    this.exchanges = {}
  }

  log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`)
  }

  async initializeExchanges() {
    this.log('\n🔧 Initializing Exchange Connections...', colors.blue + colors.bright)
    
    const exchangeConfigs = {
      mexc: {
        apiKey: process.env.MEXC_API_KEY,
        secret: process.env.MEXC_SECRET_KEY,
        testnet: process.env.MEXC_TESTNET === 'true',
        sandbox: false // MEXC doesn't support sandbox
      },
      kucoin: {
        apiKey: process.env.KUCOIN_API_KEY,
        secret: process.env.KUCOIN_SECRET_KEY,
        passphrase: process.env.KUCOIN_PASSPHRASE,
        testnet: process.env.KUCOIN_TESTNET === 'true',
        sandbox: process.env.KUCOIN_TESTNET === 'true'
      },
      binance: {
        apiKey: process.env.BINANCE_API_KEY,
        secret: process.env.BINANCE_SECRET_KEY,
        testnet: process.env.BINANCE_TESTNET === 'true',
        sandbox: process.env.BINANCE_TESTNET === 'true'
      },
      bybit: {
        apiKey: process.env.BYBIT_API_KEY,
        secret: process.env.BYBIT_SECRET_KEY,
        testnet: process.env.BYBIT_TESTNET === 'true',
        sandbox: process.env.BYBIT_TESTNET === 'true'
      }
    }

    for (const [exchangeId, config] of Object.entries(exchangeConfigs)) {
      // Skip exchanges with placeholder values
      if (config.apiKey && config.secret &&
          !config.apiKey.includes('your_') &&
          !config.secret.includes('your_')) {
        try {
          const exchangeClass = ccxt[exchangeId]
          if (!exchangeClass) {
            this.log(`❌ ${exchangeId}: Exchange class not found in CCXT`, colors.red)
            continue
          }

          const exchangeConfig = {
            apiKey: config.apiKey,
            secret: config.secret,
            enableRateLimit: true,
            timeout: 30000
          }

          // Add exchange-specific configurations
          if (exchangeId === 'kucoin') {
            if (config.passphrase) {
              exchangeConfig.password = config.passphrase  // KuCoin uses 'password' not 'passphrase'
              this.log(`  🔑 KuCoin passphrase configured`, colors.green)
            } else {
              this.log(`❌ ${exchangeId}: Missing passphrase for KuCoin`, colors.red)
              continue
            }
          }

          // Only set sandbox for exchanges that support it
          if (config.sandbox && exchangeId === 'binance') {
            exchangeConfig.sandbox = true
          }

          if (config.testnet && exchangeId === 'bybit') {
            exchangeConfig.testnet = true
          }

          // MEXC doesn't support sandbox/testnet in CCXT
          if (exchangeId === 'mexc') {
            // Use live API for MEXC
            this.log(`⚠️  ${exchangeId}: Using live API (no testnet support)`, colors.yellow)
          }

          const exchange = new exchangeClass(exchangeConfig)
          this.exchanges[exchangeId] = exchange
          
          this.log(`✅ ${exchangeId}: Initialized (${config.testnet ? 'testnet' : 'live'})`, colors.green)
        } catch (error) {
          this.log(`❌ ${exchangeId}: Initialization failed - ${error.message}`, colors.red)
        }
      } else {
        this.log(`⚠️  ${exchangeId}: Missing API credentials`, colors.yellow)
      }
    }
  }

  async testExchangeConnection(exchangeId, exchange) {
    this.log(`\n🧪 Testing ${exchangeId.toUpperCase()} Connection...`, colors.cyan + colors.bright)
    
    const results = {
      exchangeId,
      connected: false,
      markets: false,
      balance: false,
      ticker: false,
      orderbook: false,
      permissions: [],
      errors: [],
      responseTime: 0
    }

    const startTime = Date.now()

    try {
      // Test 1: Load Markets
      this.log(`  📊 Loading markets...`, colors.yellow)
      const markets = await exchange.loadMarkets()
      const marketCount = Object.keys(markets).length
      results.markets = true
      results.marketCount = marketCount
      this.log(`  ✅ Markets loaded: ${marketCount} pairs`, colors.green)

      // Test 2: Fetch Balance
      this.log(`  💰 Fetching account balance...`, colors.yellow)
      const balance = await exchange.fetchBalance()
      results.balance = true
      results.balanceData = this.summarizeBalance(balance)
      this.log(`  ✅ Balance fetched: ${results.balanceData.currencies} currencies`, colors.green)

      // Test 3: Fetch Ticker
      this.log(`  📈 Fetching BTC/USDT ticker...`, colors.yellow)
      const ticker = await exchange.fetchTicker('BTC/USDT')
      results.ticker = true
      results.tickerPrice = ticker.last
      this.log(`  ✅ Ticker fetched: BTC/USDT = $${ticker.last}`, colors.green)

      // Test 4: Fetch Order Book
      this.log(`  📖 Fetching order book...`, colors.yellow)
      const orderbook = await exchange.fetchOrderBook('BTC/USDT', 5)
      results.orderbook = true
      results.orderbookDepth = {
        bids: orderbook.bids.length,
        asks: orderbook.asks.length
      }
      this.log(`  ✅ Order book fetched: ${orderbook.bids.length} bids, ${orderbook.asks.length} asks`, colors.green)

      // Test 5: Check Permissions
      await this.testPermissions(exchange, results)

      results.connected = true
      results.responseTime = Date.now() - startTime

      this.log(`\n✅ ${exchangeId.toUpperCase()} - All tests passed! (${results.responseTime}ms)`, colors.green + colors.bright)

    } catch (error) {
      results.errors.push(error.message)
      results.responseTime = Date.now() - startTime
      this.log(`\n❌ ${exchangeId.toUpperCase()} - Connection failed: ${error.message}`, colors.red + colors.bright)
    }

    this.results[exchangeId] = results
    return results
  }

  async testPermissions(exchange, results) {
    this.log(`  🔐 Testing API permissions...`, colors.yellow)
    
    try {
      // Test trading permissions by checking if we can fetch orders
      await exchange.fetchOpenOrders('BTC/USDT', undefined, 1)
      results.permissions.push('read_orders')
      this.log(`    ✅ Read orders permission`, colors.green)
    } catch (error) {
      this.log(`    ⚠️  Read orders permission limited: ${error.message.substring(0, 50)}...`, colors.yellow)
    }

    try {
      // Test if we can fetch trade history
      await exchange.fetchMyTrades('BTC/USDT', undefined, 1)
      results.permissions.push('read_trades')
      this.log(`    ✅ Read trades permission`, colors.green)
    } catch (error) {
      this.log(`    ⚠️  Read trades permission limited: ${error.message.substring(0, 50)}...`, colors.yellow)
    }
  }

  summarizeBalance(balance) {
    const currencies = Object.keys(balance.total || {}).filter(currency => 
      balance.total[currency] > 0
    )
    
    const totalValue = Object.values(balance.total || {}).reduce((sum, amount) => sum + (amount || 0), 0)
    
    return {
      currencies: currencies.length,
      totalValue: totalValue.toFixed(8),
      nonZeroCurrencies: currencies
    }
  }

  async runAllTests() {
    this.log('🚀 Starting Comprehensive Exchange Connection Tests', colors.magenta + colors.bright)
    this.log('=' * 60, colors.magenta)

    await this.initializeExchanges()

    const exchangeIds = Object.keys(this.exchanges)
    if (exchangeIds.length === 0) {
      this.log('\n❌ No exchanges configured! Please check your .env.local file.', colors.red + colors.bright)
      return
    }

    this.log(`\n📋 Testing ${exchangeIds.length} exchanges: ${exchangeIds.join(', ')}`, colors.blue)

    for (const exchangeId of exchangeIds) {
      await this.testExchangeConnection(exchangeId, this.exchanges[exchangeId])
      await new Promise(resolve => setTimeout(resolve, 1000)) // Rate limiting
    }

    this.generateReport()
  }

  generateReport() {
    this.log('\n📊 FINAL TEST REPORT', colors.magenta + colors.bright)
    this.log('=' * 60, colors.magenta)

    const successful = Object.values(this.results).filter(r => r.connected).length
    const total = Object.keys(this.results).length

    this.log(`\n🎯 Overall Success Rate: ${successful}/${total} (${((successful/total)*100).toFixed(1)}%)`, 
      successful === total ? colors.green + colors.bright : colors.yellow + colors.bright)

    for (const [exchangeId, result] of Object.entries(this.results)) {
      this.log(`\n📈 ${exchangeId.toUpperCase()} Summary:`, colors.cyan + colors.bright)
      this.log(`   Status: ${result.connected ? '✅ Connected' : '❌ Failed'}`, 
        result.connected ? colors.green : colors.red)
      this.log(`   Response Time: ${result.responseTime}ms`)
      
      if (result.connected) {
        this.log(`   Markets: ${result.marketCount} pairs`)
        this.log(`   Balance: ${result.balanceData.currencies} currencies`)
        this.log(`   Permissions: ${result.permissions.join(', ') || 'Limited'}`)
        if (result.tickerPrice) {
          this.log(`   BTC Price: $${result.tickerPrice}`)
        }
      } else {
        this.log(`   Errors: ${result.errors.join(', ')}`, colors.red)
      }
    }

    this.log('\n🔧 Next Steps:', colors.blue + colors.bright)
    if (successful < total) {
      this.log('   1. Fix failed exchange connections', colors.yellow)
      this.log('   2. Verify API credentials and permissions', colors.yellow)
      this.log('   3. Check network connectivity and rate limits', colors.yellow)
    } else {
      this.log('   ✅ All exchanges connected successfully!', colors.green)
      this.log('   🚀 Ready to proceed with trading engine integration', colors.green)
    }
  }
}

// Run the tests
if (require.main === module) {
  const tester = new ExchangeConnectionTester()
  tester.runAllTests().catch(error => {
    console.error('❌ Test suite failed:', error)
    process.exit(1)
  })
}

module.exports = ExchangeConnectionTester
