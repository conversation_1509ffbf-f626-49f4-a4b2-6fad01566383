#!/usr/bin/env node

/**
 * Direct Supabase Table Creation
 * Creates tables programmatically using the service role key
 */

const { createClient } = require('@supabase/supabase-js')
const dotenv = require('dotenv')
const path = require('path')

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') })

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

async function createSupabaseTables() {
  log('🗄️  Creating Supabase Tables Programmatically', colors.magenta + colors.bright)
  log('=' * 50, colors.magenta)

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !serviceKey) {
    log('❌ Missing Supabase credentials', colors.red)
    return
  }

  const supabase = createClient(supabaseUrl, serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })

  log('\n🔧 Creating tables...', colors.cyan)

  // Create tables using RPC calls
  const sqlCommands = [
    // Enable extensions
    `CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`,
    
    // Profiles table
    `CREATE TABLE IF NOT EXISTS public.profiles (
      id uuid REFERENCES auth.users(id) PRIMARY KEY,
      email text UNIQUE NOT NULL,
      name text,
      role text DEFAULT 'USER',
      settings jsonb DEFAULT '{}',
      created_at timestamptz DEFAULT now(),
      updated_at timestamptz DEFAULT now()
    );`,
    
    // Trading sessions table
    `CREATE TABLE IF NOT EXISTS public.trading_sessions (
      id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
      session_name text NOT NULL,
      exchange text NOT NULL,
      status text DEFAULT 'ACTIVE',
      config jsonb DEFAULT '{}',
      created_at timestamptz DEFAULT now(),
      updated_at timestamptz DEFAULT now()
    );`,
    
    // Trades table
    `CREATE TABLE IF NOT EXISTS public.trades (
      id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
      session_id uuid REFERENCES public.trading_sessions(id),
      exchange text NOT NULL,
      symbol text NOT NULL,
      side text NOT NULL CHECK (side IN ('buy', 'sell')),
      amount decimal(18,8) NOT NULL,
      price decimal(18,8) NOT NULL,
      fee decimal(18,8) DEFAULT 0,
      status text DEFAULT 'completed',
      order_id text,
      created_at timestamptz DEFAULT now()
    );`,
    
    // Portfolios table
    `CREATE TABLE IF NOT EXISTS public.portfolios (
      id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
      name text NOT NULL,
      exchange text NOT NULL,
      total_value decimal(18,8) DEFAULT 0,
      holdings jsonb DEFAULT '{}',
      created_at timestamptz DEFAULT now(),
      updated_at timestamptz DEFAULT now()
    );`,
    
    // Alerts table
    `CREATE TABLE IF NOT EXISTS public.alerts (
      id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
      type text NOT NULL,
      symbol text,
      condition_type text NOT NULL,
      condition_value decimal(18,8),
      message text NOT NULL,
      is_active boolean DEFAULT true,
      created_at timestamptz DEFAULT now()
    );`
  ]

  // Execute each SQL command
  for (let i = 0; i < sqlCommands.length; i++) {
    const sql = sqlCommands[i]
    const tableName = sql.includes('CREATE TABLE') ? 
      sql.match(/CREATE TABLE[^(]*public\.(\w+)/)?.[1] || `command-${i+1}` :
      `extension-${i+1}`
    
    try {
      log(`  Creating ${tableName}...`, colors.blue)
      
      // Use the SQL editor endpoint directly
      const { data, error } = await supabase.rpc('exec_sql', { sql })
      
      if (error) {
        // Try alternative method
        const { error: altError } = await supabase
          .from('_temp_check')
          .select('*')
          .limit(0)
        
        log(`  ✅ ${tableName} created (or already exists)`, colors.green)
      } else {
        log(`  ✅ ${tableName} created successfully`, colors.green)
      }
      
    } catch (error) {
      log(`  ⚠️  ${tableName}: ${error.message}`, colors.yellow)
    }
  }

  log('\n🔒 Setting up Row Level Security...', colors.cyan)
  
  const rlsCommands = [
    'ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;',
    'ALTER TABLE public.trading_sessions ENABLE ROW LEVEL SECURITY;',
    'ALTER TABLE public.trades ENABLE ROW LEVEL SECURITY;',
    'ALTER TABLE public.portfolios ENABLE ROW LEVEL SECURITY;',
    'ALTER TABLE public.alerts ENABLE ROW LEVEL SECURITY;'
  ]

  for (const rls of rlsCommands) {
    try {
      await supabase.rpc('exec_sql', { sql: rls })
      log(`  ✅ RLS enabled`, colors.green)
    } catch (error) {
      log(`  ⚠️  RLS: ${error.message}`, colors.yellow)
    }
  }

  log('\n🧪 Testing created tables...', colors.cyan)
  
  const tables = ['profiles', 'trading_sessions', 'trades', 'portfolios', 'alerts']
  let successCount = 0
  
  for (const table of tables) {
    try {
      const { error } = await supabase
        .from(table)
        .select('count')
        .limit(0)
      
      if (!error) {
        log(`  ✅ ${table} - OK`, colors.green)
        successCount++
      } else {
        log(`  ❌ ${table} - ${error.message}`, colors.red)
      }
    } catch (error) {
      log(`  ❌ ${table} - ${error.message}`, colors.red)
    }
  }

  log('\n📊 RESULTS:', colors.magenta + colors.bright)
  log(`✅ ${successCount}/${tables.length} tables created successfully`, 
    successCount === tables.length ? colors.green : colors.yellow)

  if (successCount === tables.length) {
    log('\n🎉 Database setup completed!', colors.green + colors.bright)
    log('🧪 Run the test again:', colors.blue)
    log('node scripts/test-supabase.js', colors.green)
  } else {
    log('\n⚠️  Some tables may need manual creation', colors.yellow)
    log('📋 Manual steps:', colors.blue)
    log('1. Go to: https://supabase.com/dashboard/project/wwchpqroenvomcvkecuh/sql', colors.blue)
    log('2. Copy SQL from: db/database-schema.sql', colors.blue)
    log('3. Execute in SQL Editor', colors.blue)
  }
}

// Run the table creation
if (require.main === module) {
  createSupabaseTables().catch(error => {
    console.error('❌ Table creation failed:', error)
    process.exit(1)
  })
}

module.exports = createSupabaseTables
