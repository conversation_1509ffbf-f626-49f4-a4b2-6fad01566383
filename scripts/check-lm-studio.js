#!/usr/bin/env node

/**
 * Simple LM Studio Status Checker
 */

const { spawn } = require('child_process')

const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

async function checkLMStudio() {
  log('🔍 Checking LM Studio Status...', colors.cyan)
  
  try {
    const response = await fetch('http://localhost:1234/v1/models')
    
    if (response.ok) {
      const data = await response.json()
      log('✅ LM Studio server is running!', colors.green)
      log(`📡 Server: http://localhost:1234/v1`, colors.green)
      
      if (data.data && data.data.length > 0) {
        log(`🤖 Loaded models: ${data.data.length}`, colors.green)
        data.data.forEach((model, i) => {
          log(`   ${i + 1}. ${model.id}`, colors.green)
        })
      } else {
        log('⚠️  No models loaded', colors.yellow)
      }
      
      log('\n🎯 Ready for Cursor integration!', colors.green)
      log('Configure Cursor with:', colors.blue)
      log('  API Base: http://localhost:1234/v1', colors.blue)
      log('  API Key: lm-studio', colors.blue)
      
    } else {
      log(`❌ Server responded with status: ${response.status}`, colors.red)
    }
    
  } catch (error) {
    log('❌ LM Studio server is not running', colors.red)
    log('\n📋 To start LM Studio:', colors.yellow)
    log('1. Open LM Studio app', colors.yellow)
    log('2. Go to "Local Server" tab', colors.yellow)
    log('3. Load a model', colors.yellow)
    log('4. Click "Start Server"', colors.yellow)
    log('5. Run this script again to verify', colors.yellow)
  }
}

// Add fetch polyfill for older Node versions
if (!global.fetch) {
  global.fetch = require('node-fetch').default
}

checkLMStudio()
