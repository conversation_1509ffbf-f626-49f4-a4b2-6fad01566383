#!/usr/bin/env node

const { default: fetch } = require('node-fetch');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env.local') });
dotenv.config({ path: path.join(__dirname, '..', '.env') });

async function testLMStudio() {
  console.log('🧪 Testing LM Studio Connection...\n');
  
  const baseUrl = process.env.LM_STUDIO_BASE_URL || 'http://192.168.0.33:1235/v1';
  const model = process.env.LM_STUDIO_MODEL || 'deepseek-coder-33b-instruct';
  
  console.log(`📍 Base URL: ${baseUrl}`);
  console.log(`🤖 Model: ${model}\n`);
  
  try {
    // Test 1: Check if LM Studio server is running
    console.log('1️⃣ Testing server connectivity...');
    const healthResponse = await fetch(`${baseUrl}/models`, {
      method: 'GET',
      timeout: 10000
    });
    
    if (!healthResponse.ok) {
      throw new Error(`Server returned ${healthResponse.status}: ${healthResponse.statusText}`);
    }
    
    const models = await healthResponse.json();
    console.log('✅ Server is running');
    console.log(`📊 Available models: ${models.data?.length || 0}`);
    
    if (models.data && models.data.length > 0) {
      console.log('   Models found:');
      models.data.forEach(model => {
        console.log(`   - ${model.id}`);
      });
    }
    
    // Test 2: Test chat completion
    console.log('\n2️⃣ Testing chat completion...');
    const chatResponse = await fetch(`${baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'user',
            content: 'Hello! This is a test message. Please respond with "LM Studio is working correctly!"'
          }
        ],
        max_tokens: 50,
        temperature: 0.7
      }),
      timeout: 30000
    });
    
    if (!chatResponse.ok) {
      const errorText = await chatResponse.text();
      throw new Error(`Chat completion failed: ${chatResponse.status} - ${errorText}`);
    }
    
    const chatResult = await chatResponse.json();
    console.log('✅ Chat completion successful');
    console.log(`💬 Response: ${chatResult.choices?.[0]?.message?.content || 'No content received'}`);
    
    console.log('\n🎉 All tests passed! LM Studio is working correctly.');
    
  } catch (error) {
    console.error('\n❌ Test failed:');
    console.error(`   Error: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Suggestions:');
      console.log('   - Make sure LM Studio is running');
      console.log('   - Check if the server is listening on the correct port');
      console.log('   - Verify the base URL in your .env.local file');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('\n💡 Suggestions:');
      console.log('   - The server might be slow to respond');
      console.log('   - Check your network connection');
      console.log('   - Try increasing the timeout value');
    }
    
    process.exit(1);
  }
}

// Run the test
testLMStudio();
