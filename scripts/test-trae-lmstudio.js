#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' });

const fs = require('fs');
const path = require('path');

class TraeLMStudioTester {
  constructor() {
    this.baseUrl = process.env.LM_STUDIO_BASE_URL || 'http://localhost:1234/v1';
    this.model = process.env.LM_STUDIO_MODEL || 'deepseek-coder-33b-instruct';
    this.fallbackModel = process.env.LM_STUDIO_FALLBACK_MODEL || 'wizardlm-2-7b';
  }

  async testConnection() {
    console.log('🧪 Testing Trae + LM Studio Integration...\n');
    console.log(`📍 Base URL: ${this.baseUrl}`);
    console.log(`🤖 Primary Model: ${this.model}`);
    console.log(`🔄 Fallback Model: ${this.fallbackModel}\n`);

    try {
      // Test 1: Server connectivity
      console.log('1️⃣ Testing server connectivity...');
      const modelsResponse = await fetch(`${this.baseUrl}/models`);
      
      if (!modelsResponse.ok) {
        throw new Error(`Server responded with status: ${modelsResponse.status}`);
      }
      
      const modelsData = await modelsResponse.json();
      console.log('✅ Server is running');
      console.log(`📊 Available models: ${modelsData.data?.length || 0}\n`);

      // Test 2: Primary model test
      console.log('2️⃣ Testing primary model (coding task)...');
      await this.testCodingTask();

      // Test 3: Fallback model test  
      console.log('3️⃣ Testing fallback model (quick response)...');
      await this.testQuickResponse();

      // Test 4: Crypto-specific task
      console.log('4️⃣ Testing crypto trading assistance...');
      await this.testCryptoTask();

      // Test 5: Dutch language support
      console.log('5️⃣ Testing Dutch language support...');
      await this.testDutchSupport();

      console.log('\n🎉 All Trae + LM Studio tests passed!');
      console.log('✅ Ready for Dutch crypto trading development');
      
    } catch (error) {
      console.error(`❌ Test failed: ${error.message}`);
      this.troubleshoot();
    }
  }

  async testCodingTask() {
    const prompt = `Create a TypeScript function to calculate RSI (Relative Strength Index) for crypto trading. Include proper type definitions.`;
    
    const response = await this.makeRequest(this.model, prompt, 0.3);
    
    if (response && response.length > 50) {
      console.log('✅ Coding task successful');
      console.log(`📝 Generated ${response.length} characters of code\n`);
    } else {
      throw new Error('Coding task failed - insufficient response');
    }
  }

  async testQuickResponse() {
    const prompt = `Wat is het verschil tussen market order en limit order in crypto trading? Kort antwoord in Nederlands.`;
    
    const response = await this.makeRequest(this.fallbackModel, prompt, 0.2);
    
    if (response && response.includes('order')) {
      console.log('✅ Quick response successful');
      console.log(`💬 Response: "${response.substring(0, 100)}..."\n`);
    } else {
      throw new Error('Quick response test failed');
    }
  }

  async testCryptoTask() {
    const prompt = `Explain how to implement stop-loss logic in a crypto trading bot. Focus on practical implementation.`;
    
    const response = await this.makeRequest(this.model, prompt, 0.4);
    
    if (response && (response.includes('stop') || response.includes('loss'))) {
      console.log('✅ Crypto trading assistance successful');
      console.log(`📊 Generated trading guidance\n`);
    } else {
      throw new Error('Crypto task test failed');
    }
  }

  async testDutchSupport() {
    const prompt = `Leg uit hoe DCA (Dollar Cost Averaging) werkt in crypto trading. Geef een praktisch voorbeeld.`;
    
    const response = await this.makeRequest(this.model, prompt, 0.5);
    
    if (response && response.length > 50) {
      console.log('✅ Dutch language support confirmed');
      console.log(`🇳🇱 Dutch response received\n`);
    } else {
      throw new Error('Dutch language test failed');
    }
  }

  async makeRequest(model, prompt, temperature = 0.3) {
    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: 'system',
              content: 'Je bent een Nederlandse AI coding assistent voor crypto trading applicaties. Geef praktische, bruikbare antwoorden.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: temperature,
          max_tokens: 1000,
          stream: false
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      return data.choices[0]?.message?.content || '';
      
    } catch (error) {
      console.log(`⚠️  Request failed for model ${model}: ${error.message}`);
      return null;
    }
  }

  troubleshoot() {
    console.log('\\n🔧 Troubleshooting Guide:');
    console.log('1. Ensure LM Studio is running');
    console.log('2. Load the required models in LM Studio:');
    console.log(`   - ${this.model}`);
    console.log(`   - ${this.fallbackModel}`);
    console.log('3. Start Local Server in LM Studio');
    console.log('4. Check environment variables in .env.local');
    console.log('5. Verify network connectivity');
  }

  async generateReport() {
    const reportData = {
      timestamp: new Date().toISOString(),
      baseUrl: this.baseUrl,
      primaryModel: this.model,
      fallbackModel: this.fallbackModel,
      status: 'tested',
      features: {
        coding: true,
        dutch: true,
        crypto: true,
        multiModel: true
      }
    };

    const reportPath = path.join(__dirname, '..', 'trae-lmstudio-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
    console.log(`📊 Test report saved: ${reportPath}`);
  }
}

// Run tests
const tester = new TraeLMStudioTester();
tester.testConnection()
  .then(() => tester.generateReport())
  .catch(console.error);
