#!/usr/bin/env node

/**
 * Advanced LM Studio Manager Test Suite
 * Tests intelligent model selection and crypto-specific features
 */

const { default: fetch } = require('node-fetch')
const dotenv = require('dotenv')
const path = require('path')

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') })

// Make fetch global for our TypeScript code
global.fetch = fetch

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

// Simulate the LM Studio Manager class in JavaScript
class LMStudioManager {
  constructor() {
    this.baseUrl = process.env.LM_STUDIO_BASE_URL || 'http://localhost:1234'
    this.models = {
      'phi-4-mini': {
        name: 'microsoft/phi-4-mini-reasoning',
        size: '2.18GB',
        strengths: ['general', 'reasoning', 'fast'],
        maxTokens: 4096,
        priority: 1
      },
      'gpt-oss': {
        name: 'openai/gpt-oss-20b',
        size: '12.11GB', 
        strengths: ['conversation', 'creative', 'analysis'],
        maxTokens: 8192,
        priority: 2
      },
      'deepseek-coder': {
        name: 'deepseek-coder-33b-instruct',
        size: '19.94GB',
        strengths: ['coding', 'programming', 'technical'],
        maxTokens: 16384,
        priority: 3
      }
    }
    
    this.currentModel = null
    this.isLoading = false
  }

  selectModel(prompt) {
    const text = prompt.toLowerCase()
    
    const tradingKeywords = [
      'trading', 'crypto', 'bitcoin', 'ethereum', 'price', 'market', 'analysis',
      'portfolio', 'exchange', 'order', 'buy', 'sell', 'strategy', 'signal'
    ]
    
    const codingKeywords = [
      'code', 'programming', 'function', 'debug', 'javascript', 'typescript', 'python', 
      'react', 'api', 'algorithm', 'bug', 'script', 'development',
      'html', 'css', 'database', 'sql', 'git', 'repository', 'component'
    ]
    
    const creativeKeywords = [
      'story', 'creative', 'analyze', 'essay', 'complex', 'detailed',
      'research', 'explain in detail', 'comprehensive', 'elaborate'
    ]
    
    if (codingKeywords.some(keyword => text.includes(keyword))) {
      return 'deepseek-coder'
    }
    
    if (tradingKeywords.some(keyword => text.includes(keyword))) {
      return 'gpt-oss'
    }
    
    if (creativeKeywords.some(keyword => text.includes(keyword)) || prompt.length > 500) {
      return 'gpt-oss'
    }
    
    return 'phi-4-mini'
  }

  async callModel(prompt, selectedModel = null) {
    try {
      const modelKey = selectedModel || this.selectModel(prompt)
      const model = this.models[modelKey]
      
      log(`🤖 Using model: ${model.name} (${model.size})`, colors.blue)
      
      const response = await fetch(`${this.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.LM_STUDIO_API_KEY || 'lm-studio'}`
        },
        body: JSON.stringify({
          model: model.name,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: Math.min(model.maxTokens, 500), // Limit for testing
          temperature: 0.7,
          stream: false
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      
      return {
        success: true,
        content: data.choices[0].message.content,
        model: model.name,
        usage: data.usage
      }

    } catch (error) {
      return {
        success: false,
        error: error.message,
        fallback: await this.fallbackToSimpleModel(prompt)
      }
    }
  }

  async fallbackToSimpleModel(prompt) {
    try {
      const response = await fetch(`${this.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'local-model',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 200,
          temperature: 0.5
        })
      })

      if (response.ok) {
        const data = await response.json()
        return data.choices[0].message.content
      }
      
      return "Sorry, all models are temporarily unavailable."
    } catch (error) {
      return "Sorry, all models are temporarily unavailable."
    }
  }

  async getSystemStatus() {
    try {
      const response = await fetch(`${this.baseUrl}/v1/models`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }
      
      const data = await response.json()
      
      return {
        available: true,
        loadedModels: data.data?.length || 0,
        models: data.data || []
      }
    } catch (error) {
      return {
        available: false,
        error: 'LM Studio server not reachable'
      }
    }
  }
}

class AdvancedLMStudioTest {
  constructor() {
    this.manager = new LMStudioManager()
    this.testResults = {
      connection: false,
      modelSelection: false,
      codingTask: false,
      tradingTask: false,
      generalTask: false,
      batchProcessing: false
    }
  }

  async testConnection() {
    log('\n🔗 Testing LM Studio Connection...', colors.cyan + colors.bright)
    
    const status = await this.manager.getSystemStatus()
    
    if (status.available) {
      log('✅ LM Studio server is running', colors.green)
      log(`📊 Loaded models: ${status.loadedModels}`, colors.green)
      
      if (status.models && status.models.length > 0) {
        status.models.forEach((model, index) => {
          log(`   ${index + 1}. ${model.id}`, colors.green)
        })
      }
      
      this.testResults.connection = true
      return true
    } else {
      log(`❌ Connection failed: ${status.error}`, colors.red)
      return false
    }
  }

  async testModelSelection() {
    log('\n🧠 Testing Intelligent Model Selection...', colors.cyan + colors.bright)
    
    const testCases = [
      {
        prompt: "Create a React component for displaying crypto prices",
        expected: "deepseek-coder",
        description: "Coding task"
      },
      {
        prompt: "Analyze Bitcoin market trends and provide trading signals",
        expected: "gpt-oss",
        description: "Trading analysis"
      },
      {
        prompt: "Hello, how are you?",
        expected: "phi-4-mini",
        description: "Simple question"
      }
    ]

    let correct = 0
    
    for (const testCase of testCases) {
      const selected = this.manager.selectModel(testCase.prompt)
      const isCorrect = selected === testCase.expected
      
      log(`  ${isCorrect ? '✅' : '❌'} ${testCase.description}: ${selected} ${isCorrect ? '' : `(expected ${testCase.expected})`}`, 
          isCorrect ? colors.green : colors.yellow)
      
      if (isCorrect) correct++
    }
    
    this.testResults.modelSelection = correct === testCases.length
    log(`📊 Model selection accuracy: ${correct}/${testCases.length}`, colors.blue)
  }

  async testCodingTask() {
    log('\n💻 Testing Coding Task...', colors.cyan + colors.bright)
    
    const prompt = "Create a TypeScript function that calculates portfolio value from crypto holdings"
    
    try {
      const result = await this.manager.callModel(prompt)
      
      if (result.success) {
        log('✅ Coding task completed successfully', colors.green)
        log(`🤖 Model used: ${result.model}`, colors.blue)
        log(`📝 Response preview: ${result.content.substring(0, 100)}...`, colors.blue)
        this.testResults.codingTask = true
      } else {
        log(`❌ Coding task failed: ${result.error}`, colors.red)
        if (result.fallback) {
          log(`🔄 Fallback response: ${result.fallback.substring(0, 100)}...`, colors.yellow)
        }
      }
    } catch (error) {
      log(`❌ Coding task error: ${error.message}`, colors.red)
    }
  }

  async testTradingTask() {
    log('\n📈 Testing Trading Analysis Task...', colors.cyan + colors.bright)
    
    const prompt = "Analyze current Bitcoin price action and provide trading recommendations"
    
    try {
      const result = await this.manager.callModel(prompt)
      
      if (result.success) {
        log('✅ Trading analysis completed successfully', colors.green)
        log(`🤖 Model used: ${result.model}`, colors.blue)
        log(`📊 Analysis preview: ${result.content.substring(0, 100)}...`, colors.blue)
        this.testResults.tradingTask = true
      } else {
        log(`❌ Trading analysis failed: ${result.error}`, colors.red)
        if (result.fallback) {
          log(`🔄 Fallback response: ${result.fallback.substring(0, 100)}...`, colors.yellow)
        }
      }
    } catch (error) {
      log(`❌ Trading analysis error: ${error.message}`, colors.red)
    }
  }

  async testGeneralTask() {
    log('\n💬 Testing General Task...', colors.cyan + colors.bright)
    
    const prompt = "What is the current state of cryptocurrency adoption?"
    
    try {
      const result = await this.manager.callModel(prompt)
      
      if (result.success) {
        log('✅ General task completed successfully', colors.green)
        log(`🤖 Model used: ${result.model}`, colors.blue)
        log(`💭 Response preview: ${result.content.substring(0, 100)}...`, colors.blue)
        this.testResults.generalTask = true
      } else {
        log(`❌ General task failed: ${result.error}`, colors.red)
        if (result.fallback) {
          log(`🔄 Fallback response: ${result.fallback.substring(0, 100)}...`, colors.yellow)
        }
      }
    } catch (error) {
      log(`❌ General task error: ${error.message}`, colors.red)
    }
  }

  generateReport() {
    log('\n📊 ADVANCED LM STUDIO TEST REPORT', colors.magenta + colors.bright)
    log('=' * 50, colors.magenta)

    const tests = [
      { name: 'Server Connection', result: this.testResults.connection },
      { name: 'Model Selection', result: this.testResults.modelSelection },
      { name: 'Coding Task', result: this.testResults.codingTask },
      { name: 'Trading Analysis', result: this.testResults.tradingTask },
      { name: 'General Task', result: this.testResults.generalTask }
    ]

    const passed = tests.filter(t => t.result).length
    const total = tests.length

    log(`\n🎯 Success Rate: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`, 
      passed === total ? colors.green + colors.bright : colors.yellow + colors.bright)

    tests.forEach(test => {
      const status = test.result ? '✅' : '❌'
      log(`   ${status} ${test.name}`, test.result ? colors.green : colors.red)
    })

    log('\n🚀 INTEGRATION STATUS:', colors.blue + colors.bright)
    if (this.testResults.connection) {
      log('   ✅ Ready for CryptoAgent Pro integration!', colors.green)
      log('   🤖 Intelligent model selection working', colors.green)
      log('   💻 Coding assistance available', colors.green)
      log('   📈 Trading analysis ready', colors.green)
    } else {
      log('   🔧 Start LM Studio server first', colors.yellow)
      log('   📋 Load at least one model', colors.yellow)
      log('   🔄 Re-run test after setup', colors.yellow)
    }

    log('\n📚 NEXT STEPS:', colors.blue + colors.bright)
    log('   1. 🎯 Integrate with CryptoAgent Pro components', colors.blue)
    log('   2. 🔧 Configure model preferences', colors.blue)
    log('   3. 📊 Monitor performance and memory usage', colors.blue)
    log('   4. 🚀 Enable AI-assisted development!', colors.blue)
  }

  async runAllTests() {
    log('🚀 Starting Advanced LM Studio Manager Tests', colors.magenta + colors.bright)
    log('=' * 60, colors.magenta)

    const connected = await this.testConnection()
    
    if (connected) {
      await this.testModelSelection()
      await this.testCodingTask()
      await this.testTradingTask()
      await this.testGeneralTask()
    }

    this.generateReport()
  }
}

// Run the tests
if (require.main === module) {
  const tester = new AdvancedLMStudioTest()
  tester.runAllTests().catch(error => {
    console.error('❌ Advanced LM Studio test failed:', error)
    process.exit(1)
  })
}

module.exports = AdvancedLMStudioTest
