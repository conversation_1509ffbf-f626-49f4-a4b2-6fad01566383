#!/usr/bin/env node

/**
 * Interactive Supabase Service Role Key Fixer
 * Helps you get and set the correct Service Role Key
 */

const readline = require('readline')
const fs = require('fs')
const path = require('path')

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim())
    })
  })
}

async function fixSupabaseKey() {
  log('🔧 Supabase Service Role Key Fixer', colors.magenta + colors.bright)
  log('=' * 40, colors.magenta)

  log('\n📋 Het probleem:', colors.red)
  log('Je SUPABASE_SERVICE_ROLE_KEY is hetzelfde als je ANON_KEY', colors.red)
  log('Dit moet een andere, krachtigere key zijn!', colors.red)

  log('\n🔑 Oplossing:', colors.blue + colors.bright)
  log('1. Open: https://supabase.com/dashboard/project/zcyqjnbtojltgymtqjnp/settings/api', colors.blue)
  log('2. Je ziet twee keys:', colors.blue)
  log('   - anon public (deze heb je al)', colors.green)
  log('   - service_role (deze hebben we nodig!)', colors.yellow)
  log('3. Kopieer de SERVICE_ROLE key (niet de anon key!)', colors.blue)

  const ready = await askQuestion('\nHeb je de Supabase dashboard geopend? (y/n): ')
  
  if (ready.toLowerCase() !== 'y') {
    log('\n📋 Stappen om de key te krijgen:', colors.yellow)
    log('1. Ga naar: https://supabase.com/dashboard/project/zcyqjnbtojltgymtqjnp/settings/api', colors.yellow)
    log('2. Scroll naar "Project API keys"', colors.yellow)
    log('3. Kopieer de "service_role" key (NIET de anon key)', colors.yellow)
    log('4. Kom terug naar dit script', colors.yellow)
    
    await askQuestion('\nDruk Enter als je klaar bent...')
  }

  log('\n🔑 Service Role Key invoeren:', colors.cyan)
  log('⚠️  Let op: Deze key is ANDERS dan je anon key!', colors.yellow)
  log('⚠️  De service_role key is veel langer en begint ook met "eyJ"', colors.yellow)
  
  const serviceKey = await askQuestion('\nPlak je Service Role Key hier: ')
  
  if (!serviceKey || serviceKey.length < 100) {
    log('\n❌ Deze key lijkt te kort. Zorg dat je de service_role key kopieert!', colors.red)
    rl.close()
    return
  }

  if (serviceKey === 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpjeXFqbmJ0b2psdGd5bXRxam5wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM4Nzk4MzksImV4cCI6MjA2OTQ1NTgzOX0.1Xnr8HoCu2GUmk-CwTDOaH6AD29x7Tq7zbrmrrYVlEI') {
    log('\n❌ Dit is nog steeds je anon key! We hebben de SERVICE_ROLE key nodig.', colors.red)
    log('💡 Kijk goed in je dashboard naar de "service_role" key', colors.yellow)
    rl.close()
    return
  }

  // Update .env.local
  try {
    const envPath = path.join(__dirname, '../.env.local')
    let envContent = fs.readFileSync(envPath, 'utf8')
    
    // Replace the service role key
    envContent = envContent.replace(
      /SUPABASE_SERVICE_ROLE_KEY=.*/,
      `SUPABASE_SERVICE_ROLE_KEY=${serviceKey}`
    )
    
    fs.writeFileSync(envPath, envContent)
    
    log('\n✅ Service Role Key succesvol geüpdatet!', colors.green)
    log('📝 .env.local bestand is bijgewerkt', colors.green)
    
    // Test the connection
    log('\n🧪 Testen van de nieuwe key...', colors.cyan)
    
    const testConnection = await askQuestion('Wil je de verbinding nu testen? (y/n): ')
    
    if (testConnection.toLowerCase() === 'y') {
      log('\n🔄 Running Supabase setup test...', colors.blue)
      
      const { spawn } = require('child_process')
      
      const test = spawn('node', ['scripts/setup-supabase-advanced.js'], {
        cwd: path.join(__dirname, '..'),
        stdio: 'inherit'
      })
      
      test.on('close', (code) => {
        if (code === 0) {
          log('\n🎉 Perfect! Supabase setup succesvol!', colors.green + colors.bright)
        } else {
          log('\n⚠️  Er zijn nog issues. Check de output hierboven.', colors.yellow)
        }
        rl.close()
      })
    } else {
      log('\n📋 Volgende stappen:', colors.blue)
      log('1. Run: node scripts/setup-supabase-advanced.js', colors.blue)
      log('2. Voer de database schema uit in Supabase', colors.blue)
      log('3. Test je setup!', colors.blue)
      rl.close()
    }
    
  } catch (error) {
    log(`\n❌ Fout bij updaten: ${error.message}`, colors.red)
    rl.close()
  }
}

// Run the fixer
if (require.main === module) {
  fixSupabaseKey().catch(error => {
    console.error('❌ Key fixer failed:', error)
    process.exit(1)
  })
}

module.exports = fixSupabaseKey
