# 🗄️ Complete Supabase Setup Guide voor CryptoAgent Pro

## 📋 Wat je nodig hebt

### 1. **Supabase Account & Project**
- ✅ Je hebt al een project: `zcyqjnbtojltgymtqjnp`
- ✅ Project URL: `https://zcyqjnbtojltgymtqjnp.supabase.co`

### 2. **API Keys Corrigeren**

#### **Stap 1: Ga naar Supabase Dashboard**
1. Open: https://supabase.com/dashboard/project/zcyqjnbtojltgymtqjnp/settings/api
2. Je ziet twee belangrijke keys:

#### **Stap 2: Kopieer de juiste keys**
```
Project URL: https://zcyqjnbtojltgymtqjnp.supabase.co
anon public: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... (deze heb je al)
service_role: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... (ANDERE key!)
```

#### **Stap 3: Update .env.local**
Vervang in je `.env.local` bestand:
```bash
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here_from_supabase_dashboard
```

Met de echte `service_role` key van je dashboard.

## 🗃️ Database Schema Setup

### **Stap 1: Open SQL Editor**
1. Ga naar: https://supabase.com/dashboard/project/zcyqjnbtojltgymtqjnp/sql/new
2. Kopieer en plak de volgende SQL:

```sql
-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Profiles (extends auth.users)
CREATE TABLE IF NOT EXISTS profiles (
  id uuid REFERENCES auth.users(id) PRIMARY KEY,
  email text UNIQUE NOT NULL,
  name text,
  role text DEFAULT 'USER',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Trading Sessions
CREATE TABLE IF NOT EXISTS trading_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  session_name text NOT NULL,
  exchange text NOT NULL,
  status text DEFAULT 'ACTIVE',
  config jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Trades
CREATE TABLE IF NOT EXISTS trades (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  session_id uuid REFERENCES trading_sessions(id),
  exchange text NOT NULL,
  symbol text NOT NULL,
  side text NOT NULL CHECK (side IN ('buy', 'sell')),
  amount decimal(18,8) NOT NULL,
  price decimal(18,8) NOT NULL,
  fee decimal(18,8) DEFAULT 0,
  status text DEFAULT 'completed',
  order_id text,
  created_at timestamptz DEFAULT now()
);

-- Portfolios
CREATE TABLE IF NOT EXISTS portfolios (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  name text NOT NULL,
  exchange text NOT NULL,
  total_value decimal(18,8) DEFAULT 0,
  holdings jsonb DEFAULT '{}',
  performance jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Alerts
CREATE TABLE IF NOT EXISTS alerts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  type text NOT NULL,
  symbol text,
  condition_type text NOT NULL,
  condition_value decimal(18,8),
  message text NOT NULL,
  is_active boolean DEFAULT true,
  triggered_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- AI Analysis Results
CREATE TABLE IF NOT EXISTS ai_analysis (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  symbol text NOT NULL,
  analysis_type text NOT NULL,
  agent_name text NOT NULL,
  confidence decimal(5,2) DEFAULT 0,
  recommendation text,
  reasoning text,
  data jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);
```

### **Stap 2: Voer de SQL uit**
1. Klik **"Run"** om alle tabellen te maken
2. Je zou moeten zien: "Success. No rows returned"

## 🔒 Row Level Security (RLS) Setup

### **Stap 1: Enable RLS**
Voer deze SQL uit in de SQL Editor:

```sql
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE trading_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analysis ENABLE ROW LEVEL SECURITY;
```

### **Stap 2: Create Policies**
```sql
-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles
FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
FOR INSERT WITH CHECK (auth.uid() = id);

-- Trading sessions policies
CREATE POLICY "Users can manage own trading sessions" ON trading_sessions
FOR ALL USING (auth.uid() = user_id);

-- Trades policies
CREATE POLICY "Users can manage own trades" ON trades
FOR ALL USING (auth.uid() = user_id);

-- Portfolios policies
CREATE POLICY "Users can manage own portfolios" ON portfolios
FOR ALL USING (auth.uid() = user_id);

-- Alerts policies
CREATE POLICY "Users can manage own alerts" ON alerts
FOR ALL USING (auth.uid() = user_id);

-- AI Analysis policies
CREATE POLICY "Users can manage own ai analysis" ON ai_analysis
FOR ALL USING (auth.uid() = user_id);
```

## 🔐 Authentication Setup

### **Stap 1: Configure Auth**
1. Ga naar: https://supabase.com/dashboard/project/zcyqjnbtojltgymtqjnp/auth/settings
2. Zet **"Enable email confirmations"** AAN
3. Zet **"Enable secure email change"** AAN

### **Stap 2: Email Templates (Optioneel)**
1. Ga naar: https://supabase.com/dashboard/project/zcyqjnbtojltgymtqjnp/auth/templates
2. Pas de email templates aan naar je wensen

## 🧪 Test de Setup

### **Stap 1: Run de test**
```bash
cd /Users/<USER>/Desktop/cryptoagent-pro
node scripts/test-supabase.js
```

### **Stap 2: Verwachte output**
```
✅ Connection successful
✅ profiles - OK
✅ trading_sessions - OK
✅ trades - OK
✅ portfolios - OK
✅ alerts - OK
✅ ai_analysis - OK
✅ Authentication system accessible
```

## 🚀 Volgende Stappen

### **1. Test User Registration**
```bash
# In je app, test user signup
# Dit zou automatisch een profile record moeten maken
```

### **2. Test Database Operations**
```bash
# Test CRUD operations via je app
# Controleer dat RLS policies werken
```

### **3. Monitor Usage**
1. Ga naar: https://supabase.com/dashboard/project/zcyqjnbtojltgymtqjnp/reports
2. Monitor database usage en performance

## 🔧 Troubleshooting

### **Probleem: "relation does not exist"**
- **Oplossing**: Voer de database schema SQL opnieuw uit

### **Probleem: "JWT expired"**
- **Oplossing**: Controleer dat je keys correct zijn gekopieerd

### **Probleem: "Row Level Security"**
- **Oplossing**: Zorg dat RLS policies correct zijn ingesteld

### **Probleem: "Permission denied"**
- **Oplossing**: Controleer dat je service_role key correct is

## 📊 Database Schema Overzicht

```
auth.users (Supabase managed)
├── profiles (user info)
├── trading_sessions (trading configurations)
│   └── trades (individual trades)
├── portfolios (portfolio tracking)
├── alerts (price/performance alerts)
└── ai_analysis (AI analysis results)
```

## ✅ Checklist

- [ ] Service Role Key geüpdatet in .env.local
- [ ] Database schema uitgevoerd
- [ ] RLS policies ingesteld
- [ ] Authentication geconfigureerd
- [ ] Test script succesvol uitgevoerd
- [ ] User registration getest

**Zodra alle stappen zijn voltooid, is je Supabase volledig operationeel voor CryptoAgent Pro! 🎉**
