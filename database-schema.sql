-- CryptoAgent Pro Database Schema
-- V<PERSON>r dit uit in Supabase SQL Editor

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Organizations (multi-tenant)
CREATE TABLE organizations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  slug text UNIQUE NOT NULL,
  plan text DEFAULT 'FREE',
  status text DEFAULT 'ACTIVE',
  owner_id uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now()
);

-- Profiles (extends auth.users)
CREATE TABLE profiles (
  id uuid REFERENCES auth.users(id) PRIMARY KEY,
  email text UNIQUE NOT NULL,
  name text,
  organization_id uuid REFERENCES organizations(id),
  role text DEFAULT 'USER',
  created_at timestamptz DEFAULT now()
);

-- Trading Agents
CREATE TABLE trading_agents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  specialization text NOT NULL,
  ai_provider text NOT NULL,
  ai_model text NOT NULL,
  status text DEFAULT 'IDLE' CHECK (status IN ('ACTIVE', 'THINKING', 'ERROR', 'IDLE', 'TRADING')),
  performance_score decimal(5,2) DEFAULT 0,
  current_task text,
  last_activity timestamptz DEFAULT now(),
  config jsonb DEFAULT '{}',
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- AI Model Configurations
CREATE TABLE ai_models (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  provider text NOT NULL,
  model_name text NOT NULL,
  api_key text NOT NULL,
  endpoint text NOT NULL,
  specializations text[] DEFAULT '{}',
  cost_per_token decimal(10,8) DEFAULT 0,
  max_tokens integer DEFAULT 4096,
  temperature decimal(3,2) DEFAULT 0.7,
  is_active boolean DEFAULT true,
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Exchange Configurations
CREATE TABLE exchange_configs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  exchange_name text NOT NULL,
  api_key text NOT NULL,
  api_secret text NOT NULL,
  passphrase text,
  sandbox_mode boolean DEFAULT true,
  is_active boolean DEFAULT true,
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Trading Positions
CREATE TABLE trading_positions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id uuid REFERENCES trading_agents(id) NOT NULL,
  exchange text NOT NULL,
  symbol text NOT NULL,
  side text NOT NULL CHECK (side IN ('BUY', 'SELL')),
  size decimal(18,8) NOT NULL,
  entry_price decimal(18,8) NOT NULL,
  current_price decimal(18,8),
  pnl decimal(18,8) DEFAULT 0,
  pnl_percentage decimal(8,4) DEFAULT 0,
  status text DEFAULT 'OPEN' CHECK (status IN ('OPEN', 'CLOSED', 'CANCELLED')),
  opened_at timestamptz DEFAULT now(),
  closed_at timestamptz,
  organization_id uuid REFERENCES organizations(id) NOT NULL
);

-- AI Analysis Results
CREATE TABLE ai_analysis (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id uuid REFERENCES trading_agents(id) NOT NULL,
  analysis_type text NOT NULL,
  symbol text NOT NULL,
  timeframe text NOT NULL,
  raw_data jsonb DEFAULT '{}',
  analysis_result jsonb DEFAULT '{}',
  confidence_score decimal(5,2) DEFAULT 0,
  recommendation text CHECK (recommendation IN ('STRONG_BUY', 'BUY', 'NEUTRAL', 'SELL', 'STRONG_SELL')),
  ai_model_used text NOT NULL,
  tokens_used integer DEFAULT 0,
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Trading Signals
CREATE TABLE trading_signals (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id uuid REFERENCES trading_agents(id) NOT NULL,
  symbol text NOT NULL,
  signal_type text NOT NULL CHECK (signal_type IN ('BUY', 'SELL', 'HOLD')),
  strength decimal(5,2) NOT NULL,
  price_target decimal(18,8),
  stop_loss decimal(18,8),
  reasoning text NOT NULL,
  expires_at timestamptz,
  executed boolean DEFAULT false,
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- System Logs
CREATE TABLE trading_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id uuid REFERENCES trading_agents(id),
  log_level text DEFAULT 'INFO',
  message text NOT NULL,
  metadata jsonb DEFAULT '{}',
  organization_id uuid REFERENCES organizations(id) NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE trading_agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE exchange_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE trading_positions ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE trading_signals ENABLE ROW LEVEL SECURITY;
ALTER TABLE trading_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can access their organization data" ON trading_agents
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their organization ai_models" ON ai_models
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their organization exchanges" ON exchange_configs
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their organization positions" ON trading_positions
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their organization analysis" ON ai_analysis
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their organization signals" ON trading_signals
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

CREATE POLICY "Users can access their organization logs" ON trading_logs
FOR ALL USING (organization_id = (SELECT organization_id FROM profiles WHERE id = auth.uid()));

-- Insert default organization for testing
INSERT INTO organizations (name, slug, plan, status) 
VALUES ('CryptoAgent Pro', 'cryptoagent-pro', 'FREE', 'ACTIVE')
ON CONFLICT (slug) DO NOTHING;

-- Insert sample trading agents
INSERT INTO trading_agents (name, specialization, ai_provider, ai_model, status, performance_score, organization_id)
VALUES 
  ('Technical Analyst', 'technical_analysis', 'openrouter', 'meta-llama/llama-3.1-405b-instruct', 'IDLE', 85, (SELECT id FROM organizations WHERE slug = 'cryptoagent-pro')),
  ('Sentiment Monitor', 'sentiment_analysis', 'glm', 'glm-4-plus', 'IDLE', 78, (SELECT id FROM organizations WHERE slug = 'cryptoagent-pro')),
  ('Risk Manager', 'risk_management', 'anthropic', 'claude-3-sonnet', 'IDLE', 92, (SELECT id FROM organizations WHERE slug = 'cryptoagent-pro'))
ON CONFLICT DO NOTHING; 