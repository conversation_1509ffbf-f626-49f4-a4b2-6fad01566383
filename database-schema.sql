-- CryptoAgent Pro Database Schema
-- Execute this SQL in your Supabase SQL Editor
-- Dashboard: https://supabase.com/dashboard/project/wwchpqroenvomcvkecuh/sql

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create basic tables
CREATE TABLE IF NOT EXISTS public.profiles (
    id uuid REFERENCES auth.users(id) PRIMARY KEY,
    email text UNIQUE NOT NULL,
    name text,
    role text DEFAULT 'USER',
    settings jsonb DEFAULT '{}',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.trading_sessions (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    session_name text NOT NULL,
    exchange text NOT NULL,
    status text DEFAULT 'ACTIVE',
    config jsonb DEFAULT '{}',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.trades (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    session_id uuid REFERENCES public.trading_sessions(id),
    exchange text NOT NULL,
    symbol text NOT NULL,
    side text NOT NULL CHECK (side IN ('buy', 'sell')),
    amount decimal(18,8) NOT NULL,
    price decimal(18,8) NOT NULL,
    fee decimal(18,8) DEFAULT 0,
    status text DEFAULT 'completed',
    order_id text,
    created_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.portfolios (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name text NOT NULL,
    exchange text NOT NULL,
    total_value decimal(18,8) DEFAULT 0,
    holdings jsonb DEFAULT '{}',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.alerts (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    type text NOT NULL,
    symbol text,
    condition_type text NOT NULL,
    condition_value decimal(18,8),
    message text NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamptz DEFAULT now()
);

-- AI Analysis Results table
CREATE TABLE IF NOT EXISTS public.ai_analysis (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    symbol text NOT NULL,
    analysis_type text NOT NULL,
    agent_name text NOT NULL,
    confidence decimal(5,2) DEFAULT 0,
    recommendation text,
    reasoning text,
    data jsonb DEFAULT '{}',
    created_at timestamptz DEFAULT now()
);

-- Market Data Cache table
CREATE TABLE IF NOT EXISTS public.market_data (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol text NOT NULL,
    exchange text NOT NULL,
    price decimal(18,8) NOT NULL,
    volume decimal(18,8),
    change_24h decimal(8,4),
    data jsonb DEFAULT '{}',
    created_at timestamptz DEFAULT now(),
    UNIQUE(symbol, exchange)
);

-- Enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trading_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.market_data ENABLE ROW LEVEL SECURITY;

-- Basic policies
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users manage own trading sessions" ON public.trading_sessions
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users manage own trades" ON public.trades
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users manage own portfolios" ON public.portfolios
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users manage own alerts" ON public.alerts
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users manage own ai analysis" ON public.ai_analysis
    FOR ALL USING (auth.uid() = user_id);

-- Market data is public read, admin write
CREATE POLICY "Anyone can read market data" ON public.market_data
    FOR SELECT USING (true);

CREATE POLICY "Service role can manage market data" ON public.market_data
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Function to auto-create profile
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
    INSERT INTO public.profiles (id, email, name)
    VALUES (new.id, new.email, COALESCE(new.raw_user_meta_data->>'name', new.email));
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS trigger AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.trading_sessions
    FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.portfolios
    FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_trades_user_id ON public.trades(user_id);
CREATE INDEX IF NOT EXISTS idx_trades_symbol ON public.trades(symbol);
CREATE INDEX IF NOT EXISTS idx_trades_created_at ON public.trades(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_user_id ON public.ai_analysis(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_symbol ON public.ai_analysis(symbol);
CREATE INDEX IF NOT EXISTS idx_market_data_symbol ON public.market_data(symbol);

-- Setup complete!
SELECT 'CryptoAgent Pro database schema setup completed successfully!' as message;
