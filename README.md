# CryptoAgent Pro 🚀

**AI-Powered Cryptocurrency Trading Platform with Automated Agents**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![Next.js](https://img.shields.io/badge/Next.js-14-blue.svg)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)

## 🌟 Features

### 🤖 AI-Powered Trading
- **Technical Analysis Agent**: Advanced pattern recognition and technical indicators
- **Sentiment Analysis Agent**: Social media and news sentiment analysis
- **Risk Management Agent**: Automated risk assessment and position sizing
- **Portfolio Optimization Agent**: Modern Portfolio Theory implementation

### 📊 Multi-Exchange Support
- **Binance**: Spot trading, futures, staking
- **MEXC**: Altcoin trading, launchpad
- **KuCoin**: Wide range of trading pairs
- **Bybit**: Futures, spot, options

### 🔔 Real-time Monitoring
- **Price Alerts**: Custom price level notifications
- **Risk Alerts**: Portfolio risk threshold warnings
- **Performance Alerts**: Trading performance notifications
- **Multi-channel**: Email, push notifications, webhooks

### 📈 Advanced Analytics
- **Performance Tracking**: P&L, win rate, Sharpe ratio
- **Backtesting**: Historical strategy testing
- **Portfolio Optimization**: Efficient frontier analysis
- **Risk Management**: Position sizing and stop losses

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- LM Studio (for local AI models)
- Exchange API keys

### 1. Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/cryptoagent-pro.git
cd cryptoagent-pro

# Install dependencies
npm install

# Copy environment template
cp env.example .env
```

### 2. API Key Setup

#### Automated Setup (Recommended)
```bash
# Run the interactive setup script
npm run setup-api-keys
```

This will guide you through:
- Exchange API key configuration
- AI model setup
- Database configuration
- Trading parameters
- Alert settings

#### Manual Setup
Edit the `.env` file with your API keys:

```bash
# Exchange API Keys
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key
MEXC_API_KEY=your_mexc_api_key
MEXC_SECRET_KEY=your_mexc_secret_key
KUCOIN_API_KEY=your_kucoin_api_key
KUCOIN_SECRET_KEY=your_kucoin_secret_key
KUCOIN_PASSPHRASE=your_kucoin_passphrase
BYBIT_API_KEY=your_bybit_api_key
BYBIT_SECRET_KEY=your_bybit_secret_key

# AI Configuration
LM_STUDIO_BASE_URL=http://************:1235/v1
LM_STUDIO_MODEL=deepseek-coder-33b-instruct

# Database Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 3. Test Connections

```bash
# Test all API connections
npm run test-connections
```

### 4. Start Development

```bash
# Start development server
npm run dev

# Open http://localhost:3000
```

## 🔧 Configuration

### Trading Modes

#### Paper Trading (Recommended for Testing)
```bash
TRADING_MODE=paper
SANDBOX_MODE=true
```

#### Live Trading (Use with Caution)
```bash
TRADING_MODE=live
SANDBOX_MODE=false
```

### Risk Management

```bash
# Portfolio Risk Limits
MAX_PORTFOLIO_RISK=20        # Maximum 20% portfolio risk
MAX_POSITION_SIZE=5          # Maximum 5% per position
MAX_DAILY_LOSS=1000         # Maximum $1000 daily loss
STOP_LOSS_PERCENT=2         # 2% stop loss
TAKE_PROFIT_PERCENT=4       # 4% take profit
MAX_OPEN_POSITIONS=10       # Maximum 10 open positions
```

### AI Configuration

#### LM Studio (Local AI)
```bash
LM_STUDIO_BASE_URL=http://************:1235/v1
LM_STUDIO_MODEL=deepseek-coder-33b-instruct
LM_STUDIO_TIMEOUT=60000
```

#### OpenAI (Cloud AI)
```bash
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
```

#### Anthropic (Cloud AI)
```bash
ANTHROPIC_API_KEY=your_anthropic_api_key
ANTHROPIC_MODEL=claude-3-sonnet-********
```

## 📊 Exchange Setup

### Getting API Keys

#### Binance
1. Go to [Binance](https://www.binance.com)
2. Create account and complete KYC
3. Go to API Management
4. Create new API key
5. Enable spot trading permissions
6. **Important**: Enable IP whitelist for security

#### MEXC
1. Go to [MEXC](https://www.mexc.com)
2. Create account and complete verification
3. Go to API Management
4. Create new API key
5. Enable trading permissions

#### KuCoin
1. Go to [KuCoin](https://www.kucoin.com)
2. Create account and complete KYC
3. Go to API Management
4. Create new API key
5. Set passphrase (remember this!)

#### Bybit
1. Go to [Bybit](https://www.bybit.com)
2. Create account and complete verification
3. Go to API Management
4. Create new API key
5. Enable trading permissions

### Security Best Practices

1. **Use Testnet First**: Always test with testnet before live trading
2. **IP Whitelist**: Restrict API access to your IP address
3. **Read-Only Keys**: Use read-only keys for testing
4. **Secure Storage**: Never commit API keys to version control
5. **Regular Rotation**: Rotate API keys regularly

## 🤖 AI Models Setup

### LM Studio Configuration

1. **Download LM Studio**: [https://lmstudio.ai](https://lmstudio.ai)
2. **Download Models**:
   - `deepseek-coder-33b-instruct` (19.94 GB)
   - `qwen/qwen2.5-coder-14b` (8.33 GB)
   - `codellama-7b-kstack` (4.08 GB)

3. **Start LM Studio Server**:
   ```bash
   # In LM Studio
   - Load your models
   - Start local server
   - Note the URL (usually http://localhost:1235/v1)
   ```

4. **Configure in .env**:
   ```bash
   LM_STUDIO_BASE_URL=http://localhost:1235/v1
   LM_STUDIO_MODEL=deepseek-coder-33b-instruct
   ```

### Cloud AI Alternatives

If you don't want to run local AI models:

```bash
# OpenAI
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4

# Anthropic
ANTHROPIC_API_KEY=your_anthropic_api_key
ANTHROPIC_MODEL=claude-3-sonnet-********
```

## 🗄️ Database Setup

### Supabase (Recommended)

1. **Create Supabase Project**:
   - Go to [Supabase](https://supabase.com)
   - Create new project
   - Note your URL and API keys

2. **Run Database Setup**:
   ```bash
   # Run the database setup script
   npm run setup-database
   ```

3. **Configure in .env**:
   ```bash
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   ```

## 🔔 Alert Configuration

### Email Alerts

```bash
# SMTP Configuration
EMAIL_NOTIFICATIONS=true
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_app_password
```

### Push Notifications

```bash
# Firebase Configuration
PUSH_NOTIFICATIONS=true
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email
```

### Webhook Alerts

```bash
# Webhook Configuration
WEBHOOK_URL=your_webhook_url_here
```

## 📈 Trading Strategies

### Technical Analysis

The platform includes several technical analysis strategies:

- **Moving Average Crossover**: 10/20 EMA crossover
- **RSI Divergence**: RSI divergence detection
- **MACD Signal**: MACD signal line crossover
- **Bollinger Bands**: Price breakouts and reversals

### Sentiment Analysis

- **Social Media Sentiment**: Twitter, Reddit analysis
- **News Sentiment**: Crypto news sentiment analysis
- **Market Sentiment**: Overall market mood analysis

### Risk Management

- **Position Sizing**: Kelly Criterion implementation
- **Stop Loss**: Dynamic stop loss calculation
- **Portfolio Optimization**: Modern Portfolio Theory
- **Risk Limits**: Daily loss limits and position limits

## 🧪 Testing

### Connection Testing

```bash
# Test all API connections
npm run test-connections
```

### Backtesting

```bash
# Run backtest with historical data
npm run backtest
```

### Paper Trading

```bash
# Start paper trading mode
npm run paper-trading
```

## 📊 Performance Monitoring

### Dashboard Features

- **Real-time P&L**: Live profit/loss tracking
- **Portfolio Analytics**: Performance metrics
- **Risk Metrics**: VaR, drawdown, Sharpe ratio
- **Trade History**: Detailed trade logs
- **Agent Performance**: Individual agent statistics

### Reports

- **Daily Reports**: End-of-day performance summary
- **Monthly Reports**: Monthly performance analysis
- **Risk Reports**: Risk exposure analysis
- **Tax Reports**: Tax reporting for trades

## 🔒 Security

### API Key Security

- **Encryption**: API keys are encrypted at rest
- **Environment Variables**: Keys stored in .env file
- **Access Control**: IP whitelisting for exchanges
- **Audit Trail**: All actions logged for security

### Best Practices

1. **Never commit .env file**
2. **Use strong passwords**
3. **Enable 2FA on exchanges**
4. **Regular security audits**
5. **Monitor for suspicious activity**

## 🚨 Risk Disclaimer

**⚠️ IMPORTANT**: Cryptocurrency trading involves substantial risk of loss and is not suitable for all investors. The value of cryptocurrencies can go down as well as up, and you may lose some or all of your investment.

### Risk Warnings

- **High Volatility**: Crypto markets are highly volatile
- **Liquidity Risk**: Some assets may have low liquidity
- **Technical Risk**: Software bugs or system failures
- **Regulatory Risk**: Changing regulations may affect trading
- **Market Risk**: Overall market conditions affect performance

### Recommendations

1. **Start Small**: Begin with small amounts
2. **Test Thoroughly**: Use paper trading first
3. **Monitor Closely**: Watch your positions regularly
4. **Set Limits**: Use stop losses and position limits
5. **Diversify**: Don't put all funds in one strategy

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

```bash
# Fork the repository
git clone https://github.com/yourusername/cryptoagent-pro.git
cd cryptoagent-pro

# Install dependencies
npm install

# Set up development environment
npm run setup-dev

# Start development server
npm run dev
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Documentation
- [API Documentation](docs/API.md)
- [Trading Guide](docs/TRADING.md)
- [Troubleshooting](docs/TROUBLESHOOTING.md)

### Community
- [Discord](https://discord.gg/cryptoagent)
- [Telegram](https://t.me/cryptoagent)
- [Reddit](https://reddit.com/r/cryptoagent)

### Issues
- [GitHub Issues](https://github.com/yourusername/cryptoagent-pro/issues)
- [Bug Reports](https://github.com/yourusername/cryptoagent-pro/issues/new)

## 🙏 Acknowledgments

- **CCXT Library**: For exchange integrations
- **Supabase**: For database and authentication
- **Next.js**: For the web framework
- **Tailwind CSS**: For styling
- **LM Studio**: For local AI models

---

**Made with ❤️ by the CryptoAgent Pro Team**

*Remember: Trade responsibly and never invest more than you can afford to lose.*
