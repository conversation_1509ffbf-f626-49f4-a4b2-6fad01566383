# 🚀 CryptoAgent Pro

**AI-Powered Cryptocurrency Trading Platform with Multi-Agent Orchestration**

A sophisticated trading system that combines multiple specialized AI agents to analyze markets, manage risk, and execute trades automatically. Built with Next.js, TypeScript, and integrated with local AI models via LM Studio.

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![Next.js](https://img.shields.io/badge/Next.js-15-blue.svg)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)

## ✨ Features

### 🤖 Multi-Agent AI System
- **Technical Analyst**: Chart patterns, indicators, price action analysis
- **Sentiment Monitor**: Social media, news, and market sentiment tracking
- **Risk Manager**: Portfolio optimization and risk management
- **Market Scanner**: Opportunity detection and market screening
- **Arbitrage Hunter**: Cross-exchange arbitrage opportunities

### 📱 Mobile-First Interface
- **Real-time Dashboard**: Live portfolio tracking and agent status
- **Voice Commands**: Control agents with voice input
- **Touch-Optimized**: Designed for mobile trading on the go
- **Dark Theme**: Professional trading interface

### 🔒 Privacy & Security
- **Local AI Models**: Run AI models locally with LM Studio
- **Encrypted API Keys**: Secure storage of exchange credentials
- **Sandbox Mode**: Safe testing environment
- **Risk Controls**: Multiple layers of risk management

### 📊 Advanced Analytics
- **Real-time Data**: Live market data and price feeds
- **Performance Tracking**: Detailed trading metrics and analytics
- **Backtesting**: Historical strategy validation
- **Portfolio Optimization**: Automated rebalancing and allocation

## 🚀 Quick Start (Demo Mode)

Get up and running in 30 seconds with demo data:

```bash
# Clone the repository
git clone <your-repo-url>
cd cryptoagent-pro

# Install dependencies
npm install

# Start in demo mode (no setup required!)
npm run quick-start
```

Then visit: **http://localhost:3000/mobile-control**

### 🎯 Demo Features
- ✅ 5 AI agents with realistic behavior
- ✅ $34,241 demo portfolio with live positions
- ✅ Real-time market simulations
- ✅ Interactive mobile interface
- ✅ No API keys or database setup required

## 🛠️ Full Setup (Production Ready)

For real trading with live data and AI models:

```bash
# Run the complete setup wizard
npm run setup

# Follow the interactive setup guide for:
# - Database configuration (Supabase)
# - AI model setup (LM Studio/OpenAI/Anthropic)
# - Exchange API keys (Binance/Bybit/etc.)
# - Risk management settings
```

## 📋 Available Commands

```bash
# Development
npm run dev              # Start development server
npm run quick-start      # Demo mode (no setup required)
npm run setup           # Interactive setup wizard

# Trading Engine
npm run start:trading   # Start AI trading engine
npm run start:full      # Start complete system

# Testing
npm run test:lm-studio  # Test LM Studio connection
npm run test:ai         # Test AI providers
npm run test:connections # Test all connections
```

## 🏗️ Architecture

### AI Agent System
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Technical Agent │    │ Sentiment Agent │    │ Risk Manager    │
│                 │    │                 │    │                 │
│ • Chart Analysis│    │ • Social Media  │    │ • Position Size │
│ • Indicators    │    │ • News Analysis │    │ • Risk Metrics  │
│ • Price Action  │    │ • Market Fear   │    │ • Stop Losses   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Agent           │
                    │ Orchestrator    │
                    │                 │
                    │ • Coordination  │
                    │ • Decision      │
                    │ • Execution     │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Trading Engine  │
                    │                 │
                    │ • Order Exec    │
                    │ • Risk Control  │
                    │ • Portfolio Mgmt│
                    └─────────────────┘
```

### Technology Stack
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Node.js, Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **AI Models**: LM Studio, OpenAI, Anthropic, OpenRouter
- **Exchanges**: CCXT library (Binance, Bybit, KuCoin, MEXC)
- **Real-time**: WebSockets, Server-Sent Events

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Trading Mode
TRADING_MODE=paper          # paper, live, demo
SANDBOX_MODE=true          # Safe testing mode

# AI Configuration
LM_STUDIO_BASE_URL=http://localhost:1235/v1
OPENAI_API_KEY=your_key_here
ANTHROPIC_API_KEY=your_key_here

# Exchange APIs
BINANCE_API_KEY=your_key_here
BINANCE_SECRET_KEY=your_secret_here
BYBIT_API_KEY=your_key_here
BYBIT_SECRET_KEY=your_secret_here

# Database
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key

# Risk Management
MAX_PORTFOLIO_RISK=20      # Maximum portfolio risk %
MAX_POSITION_SIZE=5        # Maximum position size %
STOP_LOSS_PERCENT=2        # Default stop loss %
```

## 🚨 Important Security Notes

⚠️ **NEVER commit your `.env` file to git**
⚠️ **Always start with sandbox/testnet mode**
⚠️ **Use small amounts for initial testing**
⚠️ **Monitor your positions closely**

## 📱 Mobile Interface

The mobile interface provides full control over your AI trading agents:

- **Portfolio Overview**: Real-time balance and P&L
- **Agent Status**: Monitor all AI agents and their current tasks
- **Auto Trading Toggle**: Enable/disable autonomous trading
- **Voice Commands**: Control agents with voice input
- **Position Management**: View and manage open positions
- **Performance Metrics**: Track trading performance

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` folder
- **Issues**: Report bugs on GitHub Issues
- **Discussions**: Join GitHub Discussions for questions

## ⚡ Performance

- **Response Time**: < 100ms API responses
- **AI Analysis**: < 5s per market analysis
- **Real-time Updates**: < 1s latency
- **Concurrent Users**: Supports 1000+ users

---

**Built with ❤️ by the CryptoAgent Pro Team**
