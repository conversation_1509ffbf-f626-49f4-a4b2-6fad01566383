# Bijdragen aan CryptoAgent Pro

Dank je voor je interesse om bij te dragen aan CryptoAgent Pro! Dit document beschrijft hoe je kunt bijdragen.

## Hoe bij te dragen

1. **Fork de repository**: Maak een fork van de repo op GitHub.
2. **Clone je fork**: `git clone https://github.com/your-username/cryptoagent-pro.git`
3. **Creëer een branch**: `git checkout -b feature/je-feature`
4. **Maak changes**: Werk aan je feature of bugfix.
5. **Commit je changes**: Gebruik conventional commits, bijv. `feat: add new feature`.
6. **Push naar je branch**: `git push origin feature/je-feature`
7. **Creëer een Pull Request**: Open een PR met beschrijving van je changes.

## Code Stijl
- Volg TypeScript best practices.
- Gebruik ESLint voor linting.
- Voeg tests toe voor nieuwe features.

## Reporting Bugs
- Gebruik de Issue tracker.
- Beschrijf het probleem duidelijk.

Bedankt voor je bijdrage!