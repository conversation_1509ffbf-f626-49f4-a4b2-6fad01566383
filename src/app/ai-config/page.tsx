'use client'

import { useState, useEffect } from 'react'
import { 
  Brain, 
  Settings, 
  TestTube, 
  CheckCircle, 
  AlertCircle, 
  Zap,
  Globe,
  Shield,
  Cpu,
  Database
} from 'lucide-react'

interface AIProvider {
  id: string
  name: string
  description: string
  icon: string
  color: string
  models: string[]
  baseUrl?: string
  apiKey?: string
  isConfigured: boolean
  isConnected: boolean
  lastTested?: string
}

export default function AIConfigPage() {
  const [providers, setProviders] = useState<AIProvider[]>([
    {
      id: 'lm-studio',
      name: 'LM Studio',
      description: 'Local AI models for privacy-focused trading and development',
      icon: '🏠',
      color: 'bg-green-500',
      models: ['deepseek-coder-33b-instruct', 'qwen/qwen2.5-coder-14b', 'codellama-7b-kstack'],
      baseUrl: 'http://************:1235/v1',
      isConfigured: false,
      isConnected: false
    },
    {
      id: 'openrouter',
      name: '<PERSON>Router',
      description: 'Access to multiple AI models including Gemini and GLM-4.5',
      icon: '🌐',
      color: 'bg-blue-500',
      models: ['google/gemini-pro', 'zhipuai/glm-4.5', 'anthropic/claude-3.5-sonnet', 'openai/gpt-4'],
      isConfigured: false,
      isConnected: false
    },
    {
      id: 'gemini',
      name: 'Google Gemini',
      description: 'Google\'s advanced AI for trading analysis',
      icon: '🔷',
      color: 'bg-purple-500',
      models: ['gemini-pro', 'gemini-pro-vision'],
      isConfigured: false,
      isConnected: false
    },
    {
      id: 'openai',
      name: 'OpenAI',
      description: 'GPT models for comprehensive trading analysis',
      icon: '🤖',
      color: 'bg-indigo-500',
      models: ['gpt-4', 'gpt-3.5-turbo', 'gpt-4-turbo'],
      isConfigured: false,
      isConnected: false
    }
  ])

  const [selectedProvider, setSelectedProvider] = useState<AIProvider | null>(null)
  const [isTesting, setIsTesting] = useState(false)
  const [testResults, setTestResults] = useState<{[key: string]: any}>({})

  const testProvider = async (provider: AIProvider) => {
    setIsTesting(true)
    setTestResults(prev => ({ ...prev, [provider.id]: { status: 'testing' } }))

    try {
      const response = await fetch('/api/ai/test-provider', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          provider: provider.id,
          apiKey: provider.apiKey,
          baseUrl: provider.baseUrl
        })
      })

      const result = await response.json()
      
      setTestResults(prev => ({ 
        ...prev, 
        [provider.id]: { 
          status: 'success', 
          data: result,
          timestamp: new Date().toISOString()
        } 
      }))

      // Update provider status
      setProviders(prev => prev.map(p => 
        p.id === provider.id 
          ? { ...p, isConnected: true, lastTested: new Date().toISOString() }
          : p
      ))

    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        [provider.id]: { 
          status: 'error', 
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        } 
      }))
    } finally {
      setIsTesting(false)
    }
  }

  const saveProviderConfig = async (provider: AIProvider) => {
    try {
      const response = await fetch('/api/ai/save-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          provider: provider.id,
          apiKey: provider.apiKey,
          baseUrl: provider.baseUrl,
          selectedModel: provider.models[0]
        })
      })

      if (response.ok) {
        setProviders(prev => prev.map(p => 
          p.id === provider.id 
            ? { ...p, isConfigured: true }
            : p
        ))
      }
    } catch (error) {
      console.error('Failed to save provider config:', error)
    }
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <div className="bg-gray-900 border-b border-gray-800 p-6">
        <div className="flex items-center space-x-3">
          <Brain className="w-8 h-8 text-blue-400" />
          <div>
            <h1 className="text-2xl font-bold">AI Model Configuration</h1>
            <p className="text-gray-400">Configure AI providers for trading analysis</p>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Provider Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {providers.map((provider) => (
            <div
              key={provider.id}
              className={`
                relative p-6 rounded-xl border-2 transition-all duration-300 cursor-pointer
                ${selectedProvider?.id === provider.id 
                  ? 'border-blue-500 bg-blue-500/10' 
                  : 'border-gray-700 bg-gray-800/50 hover:border-gray-600'
                }
                ${provider.isConnected ? 'ring-2 ring-green-500/50' : ''}
              `}
              onClick={() => setSelectedProvider(provider)}
            >
              {/* Status Indicator */}
              <div className="absolute top-3 right-3">
                {provider.isConnected ? (
                  <CheckCircle className="w-5 h-5 text-green-400" />
                ) : provider.isConfigured ? (
                  <AlertCircle className="w-5 h-5 text-yellow-400" />
                ) : (
                  <div className="w-5 h-5 rounded-full bg-gray-600" />
                )}
              </div>

              {/* Provider Icon */}
              <div className={`w-12 h-12 rounded-lg ${provider.color} flex items-center justify-center text-2xl mb-4`}>
                {provider.icon}
              </div>

              {/* Provider Info */}
              <h3 className="text-lg font-semibold mb-2">{provider.name}</h3>
              <p className="text-gray-400 text-sm mb-4">{provider.description}</p>

              {/* Models */}
              <div className="space-y-2">
                <p className="text-xs text-gray-500 uppercase tracking-wide">Available Models</p>
                <div className="flex flex-wrap gap-1">
                  {provider.models.slice(0, 2).map((model, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-700 rounded text-xs font-mono"
                    >
                      {model.split('/').pop()}
                    </span>
                  ))}
                  {provider.models.length > 2 && (
                    <span className="px-2 py-1 bg-gray-700 rounded text-xs">
                      +{provider.models.length - 2} more
                    </span>
                  )}
                </div>
              </div>

              {/* Last Tested */}
              {provider.lastTested && (
                <div className="mt-4 pt-3 border-t border-gray-700">
                  <p className="text-xs text-gray-500">
                    Last tested: {new Date(provider.lastTested).toLocaleString()}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Configuration Panel */}
        {selectedProvider && (
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-lg ${selectedProvider.color} flex items-center justify-center text-xl`}>
                  {selectedProvider.icon}
                </div>
                <div>
                  <h2 className="text-xl font-bold">{selectedProvider.name} Configuration</h2>
                  <p className="text-gray-400">{selectedProvider.description}</p>
                </div>
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => testProvider(selectedProvider)}
                  disabled={isTesting}
                  className={`
                    px-4 py-2 rounded-lg font-medium transition-colors
                    ${isTesting 
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }
                  `}
                >
                  {isTesting ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Testing...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <TestTube className="w-4 h-4" />
                      <span>Test Connection</span>
                    </div>
                  )}
                </button>
                
                <button
                  onClick={() => saveProviderConfig(selectedProvider)}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
                >
                  Save Config
                </button>
              </div>
            </div>

            {/* Configuration Form */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* API Key */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  API Key
                </label>
                <input
                  type="password"
                  value={selectedProvider.apiKey || ''}
                  onChange={(e) => setProviders(prev => prev.map(p => 
                    p.id === selectedProvider.id 
                      ? { ...p, apiKey: e.target.value }
                      : p
                  ))}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
                  placeholder="Enter your API key"
                />
              </div>

              {/* Base URL (for LM Studio) */}
              {selectedProvider.id === 'lm-studio' && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Base URL
                  </label>
                  <input
                    type="text"
                    value={selectedProvider.baseUrl || ''}
                    onChange={(e) => setProviders(prev => prev.map(p => 
                      p.id === selectedProvider.id 
                        ? { ...p, baseUrl: e.target.value }
                        : p
                    ))}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
                    placeholder="http://localhost:1234/v1"
                  />
                </div>
              )}

              {/* Model Selection */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Selected Model
                </label>
                <select
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-blue-500 focus:outline-none"
                  aria-label="Select AI model"
                >
                  {selectedProvider.models.map((model) => (
                    <option key={model} value={model}>
                      {model}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Test Results */}
            {testResults[selectedProvider.id] && (
              <div className="mt-6 p-4 rounded-lg border">
                {testResults[selectedProvider.id].status === 'testing' && (
                  <div className="flex items-center space-x-2 text-blue-400">
                    <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
                    <span>Testing connection...</span>
                  </div>
                )}
                
                {testResults[selectedProvider.id].status === 'success' && (
                  <div className="flex items-center space-x-2 text-green-400">
                    <CheckCircle className="w-5 h-5" />
                    <span>Connection successful!</span>
                  </div>
                )}
                
                {testResults[selectedProvider.id].status === 'error' && (
                  <div className="flex items-center space-x-2 text-red-400">
                    <AlertCircle className="w-5 h-5" />
                    <span>Connection failed: {testResults[selectedProvider.id].error}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 bg-gray-800 hover:bg-gray-700 rounded-lg border border-gray-700 transition-colors">
            <div className="flex items-center space-x-3">
              <Zap className="w-6 h-6 text-yellow-400" />
              <div>
                <h3 className="font-semibold">Quick Test All</h3>
                <p className="text-sm text-gray-400">Test all configured providers</p>
              </div>
            </div>
          </button>
          
          <button className="p-4 bg-gray-800 hover:bg-gray-700 rounded-lg border border-gray-700 transition-colors">
            <div className="flex items-center space-x-3">
              <Shield className="w-6 h-6 text-green-400" />
              <div>
                <h3 className="font-semibold">Security Check</h3>
                <p className="text-sm text-gray-400">Verify API key security</p>
              </div>
            </div>
          </button>
          
          <button className="p-4 bg-gray-800 hover:bg-gray-700 rounded-lg border border-gray-700 transition-colors">
            <div className="flex items-center space-x-3">
              <Cpu className="w-6 h-6 text-blue-400" />
              <div>
                <h3 className="font-semibold">Performance Test</h3>
                <p className="text-sm text-gray-400">Benchmark AI models</p>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  )
} 