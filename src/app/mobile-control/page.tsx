'use client'

import { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>, 
  T<PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>
} from 'lucide-react'

// Type definitions for speech recognition
interface SpeechRecognitionEvent {
  results: {
    [index: number]: {
      [index: number]: {
        transcript: string
      }
    }
  }
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean
  interimResults: boolean
  lang: string
  onresult: (event: SpeechRecognitionEvent) => void
  onerror: () => void
  start: () => void
}

interface AIAgent {
  id: string
  name: string
  status: 'ACTIVE' | 'THINKING' | 'ERROR' | 'IDLE' | 'TRADING' | 'PAUSED'
  currentTask?: string
  performance: number
  specialization: string
  exchange: string
  isEnabled: boolean
}

interface Exchange {
  id: string
  name: string
  status: 'CONNECTED' | 'DISCONNECTED' | 'ERROR'
  balance: number
  activePositions: number
}

export default function MobileControlPage() {
  const [agents, setAgents] = useState<AIAgent[]>([
    {
      id: 'agent-1',
      name: 'Technical Analyst',
      status: 'ACTIVE',
      currentTask: 'Analyzing BTC/USDT patterns',
      performance: 75,
      specialization: 'technical_analysis',
      exchange: 'binance',
      isEnabled: true
    },
    {
      id: 'agent-2',
      name: 'Sentiment Monitor',
      status: 'THINKING',
      currentTask: 'Processing news sentiment',
      performance: 68,
      specialization: 'sentiment_analysis',
      exchange: 'coinbase',
      isEnabled: true
    },
    {
      id: 'agent-3',
      name: 'Risk Manager',
      status: 'ERROR',
      currentTask: 'Risk assessment failed',
      performance: 45,
      specialization: 'risk_management',
      exchange: 'kraken',
      isEnabled: false
    },
    {
      id: 'agent-4',
      name: 'Portfolio Optimizer',
      status: 'IDLE',
      currentTask: 'Waiting for signals',
      performance: 0,
      specialization: 'portfolio_optimization',
      exchange: 'binance',
      isEnabled: true
    }
  ])

  const [exchanges] = useState<Exchange[]>([
    {
      id: 'binance',
      name: 'Binance',
      status: 'CONNECTED',
      balance: 15420.50,
      activePositions: 3
    },
    {
      id: 'coinbase',
      name: 'Coinbase',
      status: 'CONNECTED',
      balance: 8920.75,
      activePositions: 2
    },
    {
      id: 'kraken',
      name: 'Kraken',
      status: 'ERROR',
      balance: 0,
      activePositions: 0
    }
  ])

  const [isListening, setIsListening] = useState(false)
  const [command, setCommand] = useState('')
  const [autoMode, setAutoMode] = useState(false)
  const [totalBalance, setTotalBalance] = useState(0)
  const [totalPositions, setTotalPositions] = useState(0)

  useEffect(() => {
    // Calculate totals
    const balance = exchanges.reduce((sum, ex) => sum + ex.balance, 0)
    const positions = exchanges.reduce((sum, ex) => sum + ex.activePositions, 0)
    setTotalBalance(balance)
    setTotalPositions(positions)
  }, [exchanges])

  const toggleAgent = (agentId: string) => {
    setAgents(prev => prev.map(agent => 
      agent.id === agentId 
        ? { ...agent, isEnabled: !agent.isEnabled }
        : agent
    ))
  }

  const toggleAutoMode = () => {
    setAutoMode(!autoMode)
    // Here you would trigger the AI to start/stop trading
  }

  const startVoiceRecording = () => {
    setIsListening(true)
    
    if ('webkitSpeechRecognition' in window) {
      const SpeechRecognition = (window as Window & typeof globalThis & {
        webkitSpeechRecognition: new () => SpeechRecognition
      }).webkitSpeechRecognition
      const recognition = new SpeechRecognition() as SpeechRecognition
      recognition.continuous = false
      recognition.interimResults = false
      recognition.lang = 'en-US'

      recognition.onresult = (event: SpeechRecognitionEvent) => {
        const transcript = event.results[0][0].transcript
        setCommand(transcript)
        executeVoiceCommand(transcript)
        setIsListening(false)
      }

      recognition.onerror = () => {
        setIsListening(false)
      }

      recognition.start()
    }
  }

  const executeVoiceCommand = async (voiceCommand: string) => {
    // Parse voice command and execute trading actions
    const command = voiceCommand.toLowerCase()
    
    if (command.includes('buy') || command.includes('long')) {
      // Execute buy order
      console.log('Executing buy order...')
    } else if (command.includes('sell') || command.includes('short')) {
      // Execute sell order
      console.log('Executing sell order...')
    } else if (command.includes('stop') || command.includes('exit')) {
      // Execute stop order
      console.log('Executing stop order...')
    } else if (command.includes('analyze') || command.includes('scan')) {
      // Trigger analysis
      console.log('Triggering market analysis...')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'border-orange-500 bg-orange-500/10 shadow-orange-500/20'
      case 'THINKING': return 'border-blue-500 bg-blue-500/10 shadow-blue-500/20'
      case 'TRADING': return 'border-green-500 bg-green-500/10 shadow-green-500/20'
      case 'ERROR': return 'border-red-500 bg-red-500/10 shadow-red-500/20'
      case 'PAUSED': return 'border-yellow-500 bg-yellow-500/10 shadow-yellow-500/20'
      default: return 'border-gray-600 bg-gray-800/50 shadow-gray-800/20'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE': return '🟡'
      case 'THINKING': return '🔵' 
      case 'TRADING': return '🟢'
      case 'ERROR': return '🔴'
      case 'PAUSED': return '🟡'
      default: return '⚫'
    }
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Status Bar */}
      <div className="bg-gray-900 p-4 flex justify-between items-center border-b border-gray-800">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full animate-pulse ${
            autoMode ? 'bg-green-400' : 'bg-red-400'
          }`} />
          <span className={`text-sm font-mono ${
            autoMode ? 'text-green-400' : 'text-red-400'
          }`}>
            {autoMode ? 'AUTO TRADING' : 'MANUAL MODE'}
          </span>
        </div>
        <h1 className="text-lg font-bold">AGENT ZERO CONTROL</h1>
        <div className="text-xs text-gray-400 font-mono">
          {new Date().toLocaleTimeString()}
        </div>
      </div>

      {/* Portfolio Summary */}
      <div className="p-4 bg-gradient-to-r from-gray-900 to-gray-800 border-b border-gray-700">
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <p className="text-gray-400 text-xs uppercase tracking-wide">Total Balance</p>
            <p className="text-white text-xl font-bold">${totalBalance.toLocaleString()}</p>
          </div>
          <div className="text-center">
            <p className="text-gray-400 text-xs uppercase tracking-wide">Active Positions</p>
            <p className="text-white text-xl font-bold">{totalPositions}</p>
          </div>
        </div>
        
        {/* Exchange Status */}
        <div className="mt-4 space-y-2">
          {exchanges.map((exchange) => (
            <div key={exchange.id} className="flex justify-between items-center bg-gray-800 rounded-lg p-2">
              <div>
                <p className="text-sm font-medium">{exchange.name}</p>
                <p className="text-xs text-gray-400">${exchange.balance.toLocaleString()}</p>
              </div>
              <div className="text-right">
                <div className={`w-2 h-2 rounded-full ${
                  exchange.status === 'CONNECTED' ? 'bg-green-400' : 
                  exchange.status === 'ERROR' ? 'bg-red-400' : 'bg-gray-400'
                }`} />
                <p className="text-xs text-gray-400">{exchange.activePositions} pos</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Auto Mode Toggle */}
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold">Auto Trading Mode</h3>
            <p className="text-sm text-gray-400">
              {autoMode ? 'AI agents are actively trading' : 'Manual control enabled'}
            </p>
          </div>
          <button
            onClick={toggleAutoMode}
            aria-label={`${autoMode ? 'Disable' : 'Enable'} auto trading mode`}
            className={`relative w-16 h-8 rounded-full transition-all duration-300 ${
              autoMode 
                ? 'bg-green-500 shadow-lg shadow-green-500/30' 
                : 'bg-gray-600'
            }`}
          >
            <div className={`absolute w-6 h-6 bg-white rounded-full top-1 transition-all duration-300 ${
              autoMode ? 'left-9' : 'left-1'
            }`} />
            <div className="absolute inset-0 flex items-center justify-center text-xs font-bold">
              {autoMode ? '🤖' : '⏸️'}
            </div>
          </button>
        </div>
      </div>

      {/* Agent Status Cards */}
      <div className="p-4 space-y-3">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <span className="mr-2">🤖</span>
          AI Agents
        </h3>
        
        {agents.map((agent) => (
          <div
            key={agent.id}
            className={`
              rounded-xl p-4 border-2 transition-all duration-300 shadow-lg
              ${getStatusColor(agent.status)}
              backdrop-blur-sm
            `}
          >
            {/* Agent Header */}
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center space-x-2">
                <span className="text-lg">{getStatusIcon(agent.status)}</span>
                <h4 className="font-semibold">{agent.name}</h4>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`
                  px-2 py-1 rounded-full text-xs font-bold uppercase tracking-wide
                  ${agent.status === 'ACTIVE' ? 'bg-orange-500 text-black' :
                    agent.status === 'THINKING' ? 'bg-blue-500 text-white' :
                    agent.status === 'TRADING' ? 'bg-green-500 text-white' :
                    agent.status === 'ERROR' ? 'bg-red-500 text-white' :
                    agent.status === 'PAUSED' ? 'bg-yellow-500 text-black' :
                    'bg-gray-500 text-white'}
                `}>
                  {agent.status}
                </span>
                <button
                  onClick={() => toggleAgent(agent.id)}
                  aria-label={`${agent.isEnabled ? 'Disable' : 'Enable'} ${agent.name} agent`}
                  className={`w-6 h-6 rounded-full border-2 ${
                    agent.isEnabled 
                      ? 'bg-green-500 border-green-400' 
                      : 'bg-gray-600 border-gray-500'
                  }`}
                />
              </div>
            </div>

            {/* Current Task */}
            {agent.currentTask && (
              <div className="mb-3">
                <p className="text-yellow-400 text-xs font-medium uppercase tracking-wide">
                  Current Task:
                </p>
                <p className="text-gray-300 text-sm">
                  {agent.currentTask}
                </p>
              </div>
            )}

            {/* Performance Bar */}
            {agent.performance > 0 && (
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-gray-400 uppercase tracking-wide">Performance</span>
                  <span className="font-bold">
                    {agent.performance}%
                  </span>
                </div>
                <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className={`h-full transition-all duration-1000 ${
                      agent.performance >= 80 ? 'bg-green-400' :
                      agent.performance >= 60 ? 'bg-yellow-400' :
                      agent.performance >= 40 ? 'bg-orange-400' :
                      'bg-red-400'
                    }`}
                    className="performance-bar"
                    style={{ width: `${agent.performance}%` }}
                  />
                </div>
              </div>
            )}

            {/* Exchange & Specialization */}
            <div className="mt-3 pt-2 border-t border-gray-700 flex justify-between">
              <span className="inline-block bg-blue-600/20 text-blue-400 px-2 py-1 rounded text-xs font-medium">
                {agent.specialization.replace('_', ' ').toUpperCase()}
              </span>
              <span className="inline-block bg-purple-600/20 text-purple-400 px-2 py-1 rounded text-xs font-medium">
                {agent.exchange.toUpperCase()}
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Voice Command Interface */}
      <div className="p-4 border-t border-gray-800">
        <div className="relative">
          <input
            type="text"
            value={command}
            onChange={(e) => setCommand(e.target.value)}
            placeholder="Enter agent command..."
            className="
              w-full bg-gray-800 text-white rounded-xl px-4 py-3 pr-16
              border border-gray-600 focus:border-blue-500 focus:outline-none
              placeholder-gray-400
            "
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                executeVoiceCommand(command)
                setCommand('')
              }
            }}
          />
          
          {/* Voice Input Button */}
          <button
            onClick={startVoiceRecording}
            disabled={isListening}
            aria-label={isListening ? 'Stop voice recording' : 'Start voice recording'}
            className={`
              absolute right-2 top-1/2 transform -translate-y-1/2
              w-10 h-10 rounded-full flex items-center justify-center
              transition-all duration-200
              ${isListening 
                ? 'bg-red-500 animate-pulse' 
                : 'bg-blue-600 hover:bg-blue-700'
              }
            `}
          >
            {isListening ? <MicOff size={20} /> : <Mic size={20} />}
          </button>
        </div>
        
        {isListening && (
          <p className="text-center text-sm text-blue-400 mt-2 animate-pulse">
            🎤 Listening... Speak your command
          </p>
        )}
      </div>

      {/* Quick Action Buttons */}
      <div className="p-4 grid grid-cols-2 gap-3">
        <button 
          className="bg-gray-800 hover:bg-gray-700 text-white py-4 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2"
          aria-label="Scan market for trading opportunities"
          onClick={() => executeVoiceCommand('analyze market')}
        >
          <TrendingUp size={20} />
          <span>Market Scan</span>
        </button>
        <button 
          className="bg-gray-800 hover:bg-gray-700 text-white py-4 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2"
          aria-label="Check portfolio risk levels"
          onClick={() => executeVoiceCommand('risk check')}
        >
          <AlertTriangle size={20} />
          <span>Risk Check</span>
        </button>
        <button 
          className="bg-purple-700 hover:bg-purple-600 text-white py-4 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2"
          aria-label="Analyze market sentiment with AI"
          onClick={() => window.open('/sentiment', '_blank')}
        >
          <Brain size={20} />
          <span>AI Sentiment</span>
        </button>
        <button 
          className="bg-red-700 hover:bg-red-600 text-white py-4 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2"
          aria-label="Emergency stop all trading activities"
          onClick={() => executeVoiceCommand('emergency stop')}
        >
          <Power size={20} />
          <span>Emergency Stop</span>
        </button>
      </div>

      {/* Bottom Safe Area */}
      <div className="h-8" />
    </div>
  )
} 