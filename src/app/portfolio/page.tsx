'use client'

import ProtectedRoute from '../../../components/auth/ProtectedRoute'
import { useAuth } from '../../../lib/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Badge } from '../../../components/ui/badge'
import { 
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  DollarSign,
  PieChart,
  BarChart3
} from 'lucide-react'
import Link from 'next/link'

function PortfolioContent() {
  const { user } = useAuth()

  // Mock portfolio data
  const portfolioData = {
    totalValue: 12345.67,
    totalChange: 2.5,
    holdings: [
      { symbol: 'BTC', name: 'Bitcoin', amount: 0.5, value: 8500, change: 3.2 },
      { symbol: 'ETH', name: 'Ethereum', amount: 2.1, value: 2800, change: -1.5 },
      { symbol: 'ADA', name: '<PERSON><PERSON>', amount: 1000, value: 1045.67, change: 5.8 }
    ]
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <h1 className="text-xl font-semibold text-gray-900 ml-4">Portfolio</h1>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Portfolio Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Portfolio Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${portfolioData.totalValue.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <span className={portfolioData.totalChange >= 0 ? 'text-green-600' : 'text-red-600'}>
                  {portfolioData.totalChange >= 0 ? '+' : ''}{portfolioData.totalChange}%
                </span> from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Holdings</CardTitle>
              <PieChart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{portfolioData.holdings.length}</div>
              <p className="text-xs text-muted-foreground">
                Different cryptocurrencies
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Performance</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">78.5%</div>
              <p className="text-xs text-muted-foreground">
                Success rate this month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Holdings Table */}
        <Card>
          <CardHeader>
            <CardTitle>Your Holdings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {portfolioData.holdings.map((holding, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{holding.symbol}</span>
                    </div>
                    <div>
                      <p className="font-medium">{holding.name}</p>
                      <p className="text-sm text-gray-500">{holding.amount} {holding.symbol}</p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="font-medium">${holding.value.toLocaleString()}</p>
                    <div className="flex items-center space-x-2">
                      {holding.change >= 0 ? (
                        <TrendingUp className="h-4 w-4 text-green-500" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-red-500" />
                      )}
                      <Badge 
                        variant={holding.change >= 0 ? 'default' : 'destructive'}
                        className="text-xs"
                      >
                        {holding.change >= 0 ? '+' : ''}{holding.change}%
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}

export default function PortfolioPage() {
  return (
    <ProtectedRoute>
      <PortfolioContent />
    </ProtectedRoute>
  )
}
