'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '../../../../lib/supabase'
import { Eye, EyeOff, Mail, Lock, AlertCircle } from 'lucide-react'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      // For development: allow test user login without email verification
      if (email === '<EMAIL>' && password === 'TestPassword123!') {
        console.log('🔧 Development login bypass for test user')
        router.push('/dashboard')
        return
      }

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        // If email not confirmed, show helpful message for test user
        if (error.message.includes('Email not confirmed') && email === '<EMAIL>') {
          setError('Email verification required. For testing, the login will be bypassed.')
          setTimeout(() => {
            router.push('/dashboard')
          }, 2000)
          return
        }
        setError(error.message)
        return
      }

      if (data.user) {
        // Redirect to dashboard
        router.push('/dashboard')
      }
    } catch (error) {
      setError('An unexpected error occurred')
      console.error('Login error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleLogin = async () => {
    setLoading(true)
    setError('')

    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/dashboard`
        }
      })

      if (error) {
        setError(error.message)
      }
    } catch (error) {
      setError('An unexpected error occurred')
      console.error('Google login error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">CryptoAgent Pro</h1>
          <p className="text-gray-400">Sign in to your account</p>

          {/* Development Helper */}
          <div className="mt-4 p-3 bg-blue-900/50 border border-blue-700 rounded-lg text-left">
            <p className="text-blue-300 text-sm font-medium mb-2">🔧 Development Test Account:</p>
            <button
              type="button"
              onClick={() => {
                setEmail('<EMAIL>')
                setPassword('TestPassword123!')
              }}
              className="text-left w-full hover:bg-blue-800/50 p-2 rounded transition-colors"
            >
              <p className="text-blue-200 text-xs">Email: <EMAIL></p>
              <p className="text-blue-200 text-xs">Password: TestPassword123!</p>
              <p className="text-blue-400 text-xs mt-1">Click here to auto-fill</p>
            </button>
          </div>
        </div>

        {/* Login Form */}
        <div className="bg-gray-900 rounded-xl p-6 border border-gray-800">
          <form onSubmit={handleLogin} className="space-y-4">
            {/* Email Input */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                Email Address
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full bg-gray-800 text-white rounded-lg pl-10 pr-4 py-3 border border-gray-700 focus:border-blue-500 focus:outline-none"
                  placeholder="Enter your email"
                  required
                />
              </div>
            </div>

            {/* Password Input */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full bg-gray-800 text-white rounded-lg pl-10 pr-12 py-3 border border-gray-700 focus:border-blue-500 focus:outline-none"
                  placeholder="Enter your password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="flex items-center space-x-2 text-red-400 text-sm">
                <AlertCircle size={16} />
                <span>{error}</span>
              </div>
            )}

            {/* Login Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white font-semibold py-3 rounded-lg transition-colors"
            >
              {loading ? 'Signing in...' : 'Sign In'}
            </button>
          </form>

          {/* Divider */}
          <div className="my-6 flex items-center">
            <div className="flex-1 border-t border-gray-700"></div>
            <span className="px-4 text-gray-400 text-sm">or</span>
            <div className="flex-1 border-t border-gray-700"></div>
          </div>

          {/* Google Login */}
          <button
            onClick={handleGoogleLogin}
            disabled={loading}
            className="w-full bg-gray-800 hover:bg-gray-700 disabled:bg-gray-600 text-white font-semibold py-3 rounded-lg border border-gray-700 transition-colors"
          >
            Continue with Google
          </button>

          {/* Links */}
          <div className="mt-6 text-center space-y-2">
            <a href="/auth/register" className="text-blue-400 hover:text-blue-300 text-sm">
              Don&apos;t have an account? Sign up
            </a>
            <br />
            <a href="/auth/forgot-password" className="text-gray-400 hover:text-gray-300 text-sm">
              Forgot your password?
            </a>
          </div>
        </div>
      </div>
    </div>
  )
} 