'use client'

import LoginForm from '../../../../components/auth/LoginForm'
import { useRedirectIfAuthenticated } from '../../../../lib/hooks/useAuth'
import Link from 'next/link'

export default function LoginPage() {
  // Redirect if already authenticated
  useRedirectIfAuthenticated('/dashboard')

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">CA</span>
            </div>
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">
            Welcome Back
          </h1>
          <p className="text-gray-300">
            Sign in to your CryptoAgent Pro account
          </p>
        </div>
        
        <LoginForm />
        
        <div className="mt-6 text-center">
          <p className="text-gray-400 text-sm">
            Don't have an account?{' '}
            <Link 
              href="/auth/register" 
              className="text-blue-400 hover:text-blue-300 font-medium"
            >
              Create one now
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
