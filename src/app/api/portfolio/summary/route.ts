import { NextRequest, NextResponse } from 'next/server'
import { demoPortfolioSummary, demoPositions } from '../../../../../lib/demo-data'

export async function GET(request: NextRequest) {
  try {
    let portfolioData = demoPortfolioSummary;

    try {
      // Try to get real data from Supabase if configured
      const { supabase } = await import('../../../../../lib/supabase')

      const { data: positions, error } = await supabase
        .from('trading_positions')
        .select('*')
        .eq('status', 'OPEN')

      if (!error && positions && positions.length > 0) {
        // Calculate real portfolio metrics
        let totalValue = 0
        let totalPnL = 0

        positions.forEach((position: any) => {
          const positionValue = position.size * (position.current_price || position.entry_price)
          totalValue += positionValue
          totalPnL += position.pnl || 0
        })

        portfolioData = {
          totalValue: totalValue,
          totalPnL: totalPnL,
          currency: 'USD',
          openPositions: positions.length,
          winRate: 78.5, // This would be calculated from historical data
          sharpeRatio: 1.85,
          maxDrawdown: -8.2,
          lastUpdated: new Date().toISOString(),
          assets: portfolioData.assets, // Keep demo assets for now
          positions: positions.map((pos: any) => ({
            symbol: pos.symbol,
            size: pos.size,
            entryPrice: pos.entry_price,
            currentPrice: pos.current_price || pos.entry_price,
            pnl: pos.pnl || 0,
            pnlPercentage: pos.pnl_percentage || 0,
            side: pos.side,
            status: pos.status
          }))
        }
      } else {
        console.log('Using demo portfolio data (no real positions found)')
      }
    } catch (dbError) {
      console.log('Using demo portfolio data (database not configured)')
    }

    return NextResponse.json(portfolioData)

  } catch (error) {
    console.error('❌ Portfolio summary error:', error)
    return NextResponse.json(
      { error: 'Failed to get portfolio summary', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}