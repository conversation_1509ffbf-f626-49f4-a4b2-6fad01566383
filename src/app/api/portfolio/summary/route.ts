import { NextRequest, NextResponse } from 'next/server'
import { supabase, TradingPosition } from '../../../../../lib/supabase'

export async function GET(request: NextRequest) {
  try {
    // Fetch open positions from database
    const { data: positions, error } = await supabase
      .from('trading_positions')
      .select('*')
      .eq('status', 'OPEN')

    if (error) {
      console.error('❌ Error fetching positions:', error)
      return NextResponse.json(
        { error: 'Failed to fetch portfolio data', details: error.message },
        { status: 500 }
      )
    }

    // Calculate portfolio metrics
    const openPositions = positions || []
    let totalValue = 0
    let totalPnL = 0

    openPositions.forEach((position: TradingPosition) => {
      const positionValue = position.size * (position.current_price || position.entry_price)
      totalValue += positionValue
      totalPnL += position.pnl || 0
    })

    // Mock some additional data for demo purposes
    const portfolioData = {
      totalValue: totalValue || 52840.50, // Fallback to mock data if no positions
      totalPnL: totalPnL || 1240.75,
      currency: 'USD',
      positions: openPositions.map((pos: any) => ({
        symbol: pos.symbol,
        size: pos.size,
        entryPrice: pos.entry_price,
        currentPrice: pos.current_price || pos.entry_price,
        pnl: pos.pnl || 0,
        pnlPercentage: pos.pnl_percentage || 0,
        side: pos.side,
        status: pos.status
      })),
      openPositions: openPositions.length,
      lastUpdated: new Date().toISOString()
    }

    return NextResponse.json(portfolioData)

  } catch (error) {
    console.error('❌ Portfolio summary error:', error)
    return NextResponse.json(
      { error: 'Failed to get portfolio summary', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 