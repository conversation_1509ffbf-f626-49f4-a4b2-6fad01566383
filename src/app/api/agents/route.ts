import { NextRequest, NextResponse } from 'next/server'
import { demoAgents } from '../../../../lib/demo-data'

export async function GET(request: NextRequest) {
  try {
    let agents = demoAgents;

    try {
      // Try to get real agents from Supabase if configured
      const { supabase } = await import('../../../../lib/supabase')

      const { data: realAgents, error } = await supabase
        .from('trading_agents')
        .select('*')
        .order('created_at', { ascending: false })

      if (!error && realAgents && realAgents.length > 0) {
        agents = realAgents.map((agent: any) => ({
          id: agent.id,
          name: agent.name,
          specialization: agent.specialization,
          status: agent.status,
          performance: agent.performance_score || 0,
          currentTask: agent.current_task,
          ai_provider: agent.ai_provider,
          ai_model: agent.ai_model,
          last_activity: agent.last_activity,
          created_at: agent.created_at
        }))
      } else {
        console.log('Using demo agents data (no real agents found)')
      }
    } catch (dbError) {
      console.log('Using demo agents data (database not configured)')
    }

    return NextResponse.json({
      agents: agents,
      count: agents.length
    })

  } catch (error) {
    console.error('❌ Agents API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, specialization, ai_provider, ai_model, organization_id } = body

    // Validate required fields
    if (!name || !specialization || !ai_provider || !ai_model || !organization_id) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const { data: agent, error } = await supabase
      .from('trading_agents')
      .insert({
        name,
        specialization,
        ai_provider,
        ai_model,
        status: 'IDLE',
        performance_score: 0,
        organization_id
      })
      .select()
      .single()

    if (error) {
      console.error('❌ Error creating agent:', error)
      return NextResponse.json(
        { error: 'Failed to create agent', details: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      agent
    })

  } catch (error) {
    console.error('❌ Create agent error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 