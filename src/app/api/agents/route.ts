import { NextRequest, NextResponse } from 'next/server'
import { supabase, TradingAgent } from '../../../../lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { data: agents, error } = await supabase
      .from('trading_agents')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ Error fetching agents:', error)
      return NextResponse.json(
        { error: 'Failed to fetch agents', details: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      agents: agents || [],
      count: agents?.length || 0
    })

  } catch (error) {
    console.error('❌ Agents API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, specialization, ai_provider, ai_model, organization_id } = body

    // Validate required fields
    if (!name || !specialization || !ai_provider || !ai_model || !organization_id) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const { data: agent, error } = await supabase
      .from('trading_agents')
      .insert({
        name,
        specialization,
        ai_provider,
        ai_model,
        status: 'IDLE',
        performance_score: 0,
        organization_id
      })
      .select()
      .single()

    if (error) {
      console.error('❌ Error creating agent:', error)
      return NextResponse.json(
        { error: 'Failed to create agent', details: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      agent
    })

  } catch (error) {
    console.error('❌ Create agent error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 