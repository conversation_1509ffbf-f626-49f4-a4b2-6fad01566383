import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../../../lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { agent_id, status, current_task, performance_score } = body

    // Validate required fields
    if (!agent_id || !status) {
      return NextResponse.json(
        { error: 'Missing required fields: agent_id and status' },
        { status: 400 }
      )
    }

    // Validate status values
    const validStatuses = ['ACTIVE', 'THINKING', 'ERROR', 'IDLE', 'TRADING']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status value' },
        { status: 400 }
      )
    }

    const updateData: any = {
      status,
      last_activity: new Date().toISOString()
    }

    if (current_task !== undefined) {
      updateData.current_task = current_task
    }

    if (performance_score !== undefined) {
      updateData.performance_score = performance_score
    }

    const { data: agent, error } = await supabase
      .from('trading_agents')
      .update(updateData)
      .eq('id', agent_id)
      .select()
      .single()

    if (error) {
      console.error('❌ Error updating agent status:', error)
      return NextResponse.json(
        { error: 'Failed to update agent status', details: error.message },
        { status: 500 }
      )
    }

    // Log the status change
    await supabase
      .from('trading_logs')
      .insert({
        agent_id,
        log_level: 'INFO',
        message: `Agent status changed to ${status}`,
        metadata: { previous_status: agent?.status, new_status: status },
        organization_id: agent?.organization_id
      })

    return NextResponse.json({
      success: true,
      agent,
      message: `Agent ${agent?.name} status updated to ${status}`
    })

  } catch (error) {
    console.error('❌ Update agent status error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 