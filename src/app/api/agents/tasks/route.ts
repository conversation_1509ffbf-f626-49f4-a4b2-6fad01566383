import { NextRequest, NextResponse } from 'next/server'
import { agentOrchestrator } from '../../../../lib/agents/AgentOrchestrator'
import { supabase } from '../../../../lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { agent_id, type, symbol, parameters } = body

    // Get user's organization from session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user's organization
    const { data: profile } = await supabase
      .from('profiles')
      .select('organization_id')
      .eq('id', session.user.id)
      .single()

    if (!profile?.organization_id) {
      return NextResponse.json(
        { error: 'User not associated with organization' },
        { status: 400 }
      )
    }

    // Validate required fields
    if (!agent_id || !type || !symbol) {
      return NextResponse.json(
        { error: 'Missing required fields: agent_id, type, symbol' },
        { status: 400 }
      )
    }

    // Add organization_id to parameters
    const taskParameters = {
      ...parameters,
      organization_id: profile.organization_id
    }

    // Add task to orchestrator
    const taskId = await agentOrchestrator.addTask({
      agent_id,
      type,
      symbol,
      parameters: taskParameters,
      status: 'PENDING'
    })

    return NextResponse.json({
      success: true,
      task_id: taskId,
      message: `Task added to queue: ${type} for ${symbol}`
    })

  } catch (error) {
    console.error('❌ Add task error:', error)
    return NextResponse.json(
      { error: 'Failed to add task', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const agent_id = searchParams.get('agent_id')
    const status = searchParams.get('status')

    // Get user's organization from session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user's organization
    const { data: profile } = await supabase
      .from('profiles')
      .select('organization_id')
      .eq('id', session.user.id)
      .single()

    if (!profile?.organization_id) {
      return NextResponse.json(
        { error: 'User not associated with organization' },
        { status: 400 }
      )
    }

    // Build query
    let query = supabase
      .from('ai_analysis')
      .select('*')
      .eq('organization_id', profile.organization_id)

    if (agent_id) {
      query = query.eq('agent_id', agent_id)
    }

    if (status) {
      query = query.eq('analysis_type', status)
    }

    const { data: analyses, error } = await query
      .order('created_at', { ascending: false })
      .limit(50)

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch analyses', details: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      analyses: analyses || [],
      count: analyses?.length || 0
    })

  } catch (error) {
    console.error('❌ Get analyses error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch analyses', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 