import { NextRequest, NextResponse } from 'next/server'
import { demoEngineStatus } from '../../../../../lib/demo-data'

export async function GET(request: NextRequest) {
  try {
    // Try to get real engine status, fallback to demo data
    let engineStatus = demoEngineStatus;

    try {
      // Uncomment when trading engine is fully implemented
      // const { tradingEngine } = await import('../../../../../lib/trading/TradingEngine')
      // const { agentOrchestrator } = await import('../../../../../lib/agents/AgentOrchestrator')
      // engineStatus = {
      //   ...tradingEngine.getStatus(),
      //   ...agentOrchestrator.getStatus()
      // }
    } catch (engineError) {
      console.log('Using demo engine status (trading engine not yet initialized)')
    }

    return NextResponse.json({
      running: engineStatus.running,
      autoMode: engineStatus.autoMode,
      uptime: engineStatus.uptime,
      activeAgents: engineStatus.activeAgents,
      totalTrades: engineStatus.totalTrades,
      successRate: engineStatus.successRate,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Engine status error:', error)
    return NextResponse.json(
      { error: 'Failed to get engine status', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}