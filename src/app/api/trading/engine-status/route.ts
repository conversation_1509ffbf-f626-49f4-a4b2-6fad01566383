import { NextRequest, NextResponse } from 'next/server'
import { tradingEngine } from '../../../../../lib/trading/TradingEngine'
import { agentOrchestrator } from '../../../../../lib/agents/AgentOrchestrator'

export async function GET(request: NextRequest) {
  try {
    const engineStatus = tradingEngine.getStatus()
    const orchestratorStatus = agentOrchestrator.getStatus()

    return NextResponse.json({
      engine: engineStatus,
      orchestrator: orchestratorStatus,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Engine status error:', error)
    return NextResponse.json(
      { error: 'Failed to get engine status', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 