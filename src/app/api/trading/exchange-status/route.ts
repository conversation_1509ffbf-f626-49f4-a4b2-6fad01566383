import { NextRequest, NextResponse } from 'next/server'

// Mock data voor nu, totdat we de exchange manager he<PERSON><PERSON> geconfigureerd
const mockExchangeData = {
  exchanges: [
    {
      id: 'binance',
      name: 'Binance',
      status: 'CONNECTED',
      balance: 15420.50,
      activePositions: 3
    },
    {
      id: 'mexc',
      name: 'MEXC',
      status: 'CONNECTED',
      balance: 8920.75,
      activePositions: 2
    },
    {
      id: 'kucoin',
      name: '<PERSON><PERSON>oi<PERSON>',
      status: 'CONNECTED',
      balance: 5670.25,
      activePositions: 1
    },
    {
      id: 'bybit',
      name: 'Bybit',
      status: 'CONNECTED',
      balance: 4230.00,
      activePositions: 2
    }
  ],
  totalBalance: 34241.50,
  totalPositions: 8,
  connectedExchanges: 4
}

export async function GET(request: NextRequest) {
  try {
    // Voor nu gebruiken we mock data
    // Later kunnen we de echte exchange manager geb<PERSON>iken
    return NextResponse.json(mockExchangeData)

  } catch (error) {
    console.error('❌ Exchange status error:', error)
    return NextResponse.json(
      { error: 'Failed to get exchange status' },
      { status: 500 }
    )
  }
} 