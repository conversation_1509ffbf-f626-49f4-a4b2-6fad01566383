import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../../../lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const { command } = await request.json()

    if (!command) {
      return NextResponse.json(
        { error: 'No command provided' },
        { status: 400 }
      )
    }

    console.log('🎤 Voice command received:', command)

    // Parse and execute voice commands
    const lowerCommand = command.toLowerCase()
    let response = { success: false, message: 'Command not recognized' }

    // Trading commands
    if (lowerCommand.includes('buy') || lowerCommand.includes('purchase')) {
      response = await handleBuyCommand(command)
    } else if (lowerCommand.includes('sell') || lowerCommand.includes('close')) {
      response = await handleSellCommand(command)
    } else if (lowerCommand.includes('stop') || lowerCommand.includes('emergency')) {
      response = await handleStopCommand(command)
    } else if (lowerCommand.includes('status') || lowerCommand.includes('check')) {
      response = await handleStatusCommand(command)
    } else if (lowerCommand.includes('auto') || lowerCommand.includes('automatic')) {
      response = await handleAutoModeCommand(command)
    } else {
      response = {
        success: false,
        message: `Command not recognized: "${command}". Try: "buy bitcoin", "sell ethereum", "check status", "emergency stop"`
      }
    }

    // Log the voice command
    await supabase
      .from('trading_logs')
      .insert({
        log_level: 'INFO',
        message: `Voice command: ${command}`,
        metadata: { command, response },
        organization_id: 'default' // TODO: Get from auth context
      })

    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ Voice command error:', error)
    return NextResponse.json(
      { error: 'Failed to process voice command', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

async function handleBuyCommand(command: string): Promise<any> {
  // Extract symbol from command (e.g., "buy bitcoin" -> "BTC/USDT")
  const symbols = extractSymbols(command)
  
  if (symbols.length === 0) {
    return {
      success: false,
      message: 'Please specify what to buy (e.g., "buy bitcoin" or "buy ethereum")'
    }
  }

  // Mock buy order for now
  return {
    success: true,
    message: `Buy order placed for ${symbols.join(', ')}`,
    action: 'BUY',
    symbols
  }
}

async function handleSellCommand(command: string): Promise<any> {
  const symbols = extractSymbols(command)
  
  if (symbols.length === 0) {
    return {
      success: false,
      message: 'Please specify what to sell (e.g., "sell bitcoin" or "sell ethereum")'
    }
  }

  return {
    success: true,
    message: `Sell order placed for ${symbols.join(', ')}`,
    action: 'SELL',
    symbols
  }
}

async function handleStopCommand(command: string): Promise<any> {
  return {
    success: true,
    message: 'Emergency stop activated. All trading paused.',
    action: 'EMERGENCY_STOP'
  }
}

async function handleStatusCommand(command: string): Promise<any> {
  // Fetch current portfolio status
  const { data: positions } = await supabase
    .from('trading_positions')
    .select('*')
    .eq('status', 'OPEN')

  return {
    success: true,
    message: `Portfolio status: ${positions?.length || 0} open positions`,
    action: 'STATUS_CHECK',
    positions: positions?.length || 0
  }
}

async function handleAutoModeCommand(command: string): Promise<any> {
  const lowerCommand = command.toLowerCase()
  
  if (lowerCommand.includes('enable') || lowerCommand.includes('on') || lowerCommand.includes('start')) {
    return {
      success: true,
      message: 'Auto trading mode enabled',
      action: 'AUTO_MODE_ENABLE'
    }
  } else if (lowerCommand.includes('disable') || lowerCommand.includes('off') || lowerCommand.includes('stop')) {
    return {
      success: true,
      message: 'Auto trading mode disabled',
      action: 'AUTO_MODE_DISABLE'
    }
  } else {
    return {
      success: false,
      message: 'Please specify: "enable auto mode" or "disable auto mode"'
    }
  }
}

function extractSymbols(command: string): string[] {
  const symbolMap: Record<string, string> = {
    'bitcoin': 'BTC/USDT',
    'btc': 'BTC/USDT',
    'ethereum': 'ETH/USDT',
    'eth': 'ETH/USDT',
    'cardano': 'ADA/USDT',
    'ada': 'ADA/USDT',
    'solana': 'SOL/USDT',
    'sol': 'SOL/USDT',
    'polkadot': 'DOT/USDT',
    'dot': 'DOT/USDT'
  }

  const symbols: string[] = []
  const lowerCommand = command.toLowerCase()

  for (const [name, symbol] of Object.entries(symbolMap)) {
    if (lowerCommand.includes(name)) {
      symbols.push(symbol)
    }
  }

  return symbols
} 