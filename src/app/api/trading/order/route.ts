import { NextRequest, NextResponse } from 'next/server'
import { tradingEngine, OrderRequest } from '../../../../../lib/trading/TradingEngine'
import { supabase } from '../../../../../lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { symbol, side, size, price, type, agent_id } = body

    // Get user's organization from session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user's organization
    const { data: profile } = await supabase
      .from('profiles')
      .select('organization_id')
      .eq('id', session.user.id)
      .single()

    if (!profile?.organization_id) {
      return NextResponse.json(
        { error: 'User not associated with organization' },
        { status: 400 }
      )
    }

    // Create order request
    const orderRequest: OrderRequest = {
      symbol,
      side,
      size,
      price,
      type: type || 'MARKET',
      agent_id,
      organization_id: profile.organization_id
    }

    // Place order
    const result = await tradingEngine.placeOrder(orderRequest)

    if (result.success) {
      return NextResponse.json({
        success: true,
        order_id: result.order_id,
        position: result.position,
        message: `Order placed successfully for ${symbol}`
      })
    } else {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('❌ Order placement error:', error)
    return NextResponse.json(
      { error: 'Failed to place order', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || 'OPEN'

    // Get user's organization from session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user's organization
    const { data: profile } = await supabase
      .from('profiles')
      .select('organization_id')
      .eq('id', session.user.id)
      .single()

    if (!profile?.organization_id) {
      return NextResponse.json(
        { error: 'User not associated with organization' },
        { status: 400 }
      )
    }

    // Fetch positions
    const { data: positions, error } = await supabase
      .from('trading_positions')
      .select('*')
      .eq('organization_id', profile.organization_id)
      .eq('status', status)
      .order('created_at', { ascending: false })

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch positions', details: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      positions: positions || [],
      count: positions?.length || 0
    })

  } catch (error) {
    console.error('❌ Get positions error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch positions', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 