import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../../lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const { enabled } = await request.json()

    // Update trading mode in database or environment
    if (enabled) {
      console.log('🤖 Auto trading mode ENABLED')
      // Here you would start the trading engine
      // For now, just log the action
    } else {
      console.log('🛑 Auto trading mode DISABLED')
      // Here you would stop the trading engine
    }

    return NextResponse.json({
      success: true,
      autoMode: enabled,
      message: enabled ? 'Auto trading enabled' : 'Auto trading disabled'
    })

  } catch (error) {
    console.error('❌ Auto mode error:', error)
    return NextResponse.json(
      { error: 'Failed to update auto mode', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    // Get current auto mode status
    const autoMode = process.env.ENABLE_AUTO_MODE === 'true'
    
    return NextResponse.json({
      autoMode,
      message: autoMode ? 'Auto trading is enabled' : 'Auto trading is disabled'
    })

  } catch (error) {
    console.error('❌ Get auto mode error:', error)
    return NextResponse.json(
      { error: 'Failed to get auto mode status' },
      { status: 500 }
    )
  }
} 