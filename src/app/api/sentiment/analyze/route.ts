import { NextRequest, NextResponse } from 'next/server'
import { SentimentAnalysisAgentV3 } from '../../../../../lib/agents/SentimentAnalysisAgentV3'

export async function POST(request: NextRequest) {
  try {
    const { symbol, options = {} } = await request.json()

    if (!symbol) {
      return NextResponse.json({ 
        error: 'Symbol is required' 
      }, { status: 400 })
    }

    // Validate symbol format
    const symbolRegex = /^[A-Z]{1,10}$/
    if (!symbolRegex.test(symbol.toUpperCase())) {
      return NextResponse.json({ 
        error: 'Invalid symbol format' 
      }, { status: 400 })
    }

    console.log(`🚀 Starting advanced sentiment analysis for ${symbol}...`)

    const agent = new SentimentAnalysisAgentV3()
    const analysis = await agent.analyze(symbol.toUpperCase())

    return NextResponse.json({
      success: true,
      data: analysis,
      metadata: {
        version: 'v3',
        analyzedAt: new Date().toISOString(),
        symbol: symbol.toUpperCase(),
        providersUsed: analysis.data.aiConsensus ? Object.keys(analysis.data.aiConsensus).length : 0
      }
    })

  } catch (error) {
    console.error('❌ Sentiment analysis error:', error)
    
    return NextResponse.json({
      error: 'Sentiment analysis failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const symbol = searchParams.get('symbol')

  if (!symbol) {
    return NextResponse.json({
      error: 'Symbol parameter is required',
      example: '/api/sentiment/analyze?symbol=BTC'
    }, { status: 400 })
  }

  try {
    const agent = new SentimentAnalysisAgentV3()
    const analysis = await agent.analyze(symbol.toUpperCase())

    return NextResponse.json({
      success: true,
      data: analysis,
      metadata: {
        version: 'v3',
        analyzedAt: new Date().toISOString(),
        symbol: symbol.toUpperCase()
      }
    })

  } catch (error) {
    console.error('❌ GET sentiment analysis error:', error)
    
    return NextResponse.json({
      error: 'Sentiment analysis failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}