import { NextRequest, NextResponse } from 'next/server'
import { SentimentAnalysisAgentV3 } from '../../../../../lib/agents/SentimentAnalysisAgentV3'

export async function POST(request: NextRequest) {
  try {
    const { symbols, maxConcurrency = 3 } = await request.json()

    if (!symbols || !Array.isArray(symbols) || symbols.length === 0) {
      return NextResponse.json({ 
        error: 'Symbols array is required and must not be empty',
        example: { symbols: ['BTC', 'ETH', 'ADA'] }
      }, { status: 400 })
    }

    if (symbols.length > 10) {
      return NextResponse.json({ 
        error: 'Maximum 10 symbols allowed per batch',
        provided: symbols.length
      }, { status: 400 })
    }

    // Validate all symbols
    const symbolRegex = /^[A-Z]{1,10}$/
    const invalidSymbols = symbols.filter(s => !symbolRegex.test(s.toUpperCase()))
    if (invalidSymbols.length > 0) {
      return NextResponse.json({ 
        error: 'Invalid symbol formats',
        invalidSymbols
      }, { status: 400 })
    }

    console.log(`🚀 Starting bulk sentiment analysis for ${symbols.length} symbols...`)
    
    const agent = new SentimentAnalysisAgentV3()
    const results = []
    const errors = []

    // Process in batches to avoid overwhelming APIs
    const batches = []
    for (let i = 0; i < symbols.length; i += maxConcurrency) {
      batches.push(symbols.slice(i, i + maxConcurrency))
    }

    for (const batch of batches) {
      const batchPromises = batch.map(async (symbol: string) => {
        try {
          const analysis = await agent.analyze(symbol.toUpperCase())
          return { symbol: symbol.toUpperCase(), success: true, data: analysis }
        } catch (error) {
          const errorInfo = {
            symbol: symbol.toUpperCase(),
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
          errors.push(errorInfo)
          return errorInfo
        }
      })

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults.filter(r => r.success))
      
      // Small delay between batches to be respectful to APIs
      if (batches.indexOf(batch) < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        results,
        summary: {
          totalRequested: symbols.length,
          successful: results.length,
          failed: errors.length,
          errors: errors.length > 0 ? errors : undefined
        }
      },
      metadata: {
        analyzedAt: new Date().toISOString(),
        version: 'v3-bulk',
        batchSize: maxConcurrency
      }
    })

  } catch (error) {
    console.error('❌ Bulk sentiment analysis error:', error)
    
    return NextResponse.json({
      error: 'Bulk sentiment analysis failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}