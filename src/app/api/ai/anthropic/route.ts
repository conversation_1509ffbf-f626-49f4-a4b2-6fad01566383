import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { model, prompt, temperature = 0.3, maxTokens = 2048 } = await request.json()

    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.ANTHROPIC_API_KEY!,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: model || 'claude-3-sonnet-20240229',
        max_tokens: maxTokens,
        temperature: temperature,
        messages: [
          {
            role: 'user',
            content: `You are a professional crypto trading analyst. Provide accurate, well-reasoned analysis and trading recommendations.

${prompt}`
          }
        ]
      })
    })

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.status}`)
    }

    const data = await response.json()
    
    return NextResponse.json({
      content: data.content[0]?.text || 'No response from AI model',
      model: model,
      tokens_used: data.usage?.input_tokens + data.usage?.output_tokens || 0
    })

  } catch (error) {
    console.error('❌ Anthropic API error:', error)
    return NextResponse.json(
      { error: 'Failed to get AI response', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 