import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { provider, apiKey, baseUrl } = await request.json()

    // Validate input
    if (!provider) {
      return NextResponse.json({ error: 'Provider is required' }, { status: 400 })
    }

    // Test different providers
    switch (provider) {
      case 'lm-studio':
        return await testLMStudio(baseUrl)
      
      case 'openrouter':
        return await testOpenRouter(apiKey)
      
      case 'gemini':
        return await testGemini(apiKey)
      
      case 'openai':
        return await testOpenAI(apiKey)
      
      default:
        return NextResponse.json({ error: 'Unknown provider' }, { status: 400 })
    }

  } catch (error) {
    console.error('Error testing provider:', error)
    return NextResponse.json(
      { error: 'Failed to test provider' }, 
      { status: 500 }
    )
  }
}

async function testLMStudio(baseUrl: string) {
  try {
    if (!baseUrl) {
      return NextResponse.json({ error: 'Base URL is required for LM Studio' }, { status: 400 })
    }

    const response = await fetch(`${baseUrl}/models`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const models = await response.json()
    
    return NextResponse.json({
      success: true,
      message: 'LM Studio connection successful',
      models: models.data || [],
      provider: 'lm-studio'
    })

  } catch (error) {
    return NextResponse.json({
      error: `LM Studio connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 })
  }
}

async function testOpenRouter(apiKey: string) {
  try {
    if (!apiKey) {
      return NextResponse.json({ error: 'API key is required for OpenRouter' }, { status: 400 })
    }

    const response = await fetch('https://openrouter.ai/api/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const models = await response.json()
    
    return NextResponse.json({
      success: true,
      message: 'OpenRouter connection successful',
      models: models.data || [],
      provider: 'openrouter'
    })

  } catch (error) {
    return NextResponse.json({
      error: `OpenRouter connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 })
  }
}

async function testGemini(apiKey: string) {
  try {
    if (!apiKey) {
      return NextResponse.json({ error: 'API key is required for Gemini' }, { status: 400 })
    }

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const models = await response.json()
    
    return NextResponse.json({
      success: true,
      message: 'Gemini connection successful',
      models: models.models || [],
      provider: 'gemini'
    })

  } catch (error) {
    return NextResponse.json({
      error: `Gemini connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 })
  }
}

async function testOpenAI(apiKey: string) {
  try {
    if (!apiKey) {
      return NextResponse.json({ error: 'API key is required for OpenAI' }, { status: 400 })
    }

    const response = await fetch('https://api.openai.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const models = await response.json()
    
    return NextResponse.json({
      success: true,
      message: 'OpenAI connection successful',
      models: models.data || [],
      provider: 'openai'
    })

  } catch (error) {
    return NextResponse.json({
      error: `OpenAI connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 })
  }
} 