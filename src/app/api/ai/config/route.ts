import { NextRequest, NextResponse } from 'next/server'
import { aiConfigManager } from '../../../../../lib/ai/AIConfigManager'
import { aiProviderManager } from '../../../../../lib/ai/AIProviderManager'

export async function GET() {
  try {
    const agentConfigs = aiConfigManager.getAllAgentConfigs()
    const providerConfigs = aiConfigManager.getAllProviderConfigs()
    const globalConfig = aiConfigManager.getGlobalConfig()
    const usage = aiConfigManager.getUsage()
    const costLimits = aiConfigManager.checkCostLimits()
    const validation = aiConfigManager.validateConfiguration()

    return NextResponse.json({
      agents: agentConfigs,
      providers: providerConfigs,
      global: globalConfig,
      usage: Object.fromEntries(usage),
      costLimits,
      validation,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('AI config GET error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch AI configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { type, config } = await request.json()

    if (!type || !config) {
      return NextResponse.json(
        { error: 'Type and config are required' },
        { status: 400 }
      )
    }

    switch (type) {
      case 'agent':
        if (!config.agentId) {
          return NextResponse.json(
            { error: 'Agent ID is required' },
            { status: 400 }
          )
        }
        aiConfigManager.setAgentConfig(config)
        break

      case 'provider':
        if (!config.providerId) {
          return NextResponse.json(
            { error: 'Provider ID is required' },
            { status: 400 }
          )
        }
        aiConfigManager.setProviderConfig(config.providerId, config)
        break

      case 'global':
        aiConfigManager.setGlobalConfig(config)
        break

      default:
        return NextResponse.json(
          { error: 'Invalid config type. Must be: agent, provider, or global' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      type,
      message: `${type} configuration saved successfully`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('AI config POST error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to save AI configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { providerId, apiKey } = await request.json()

    if (!providerId || !apiKey) {
      return NextResponse.json(
        { error: 'Provider ID and API key are required' },
        { status: 400 }
      )
    }

    // Set API key in provider manager
    aiProviderManager.setApiKey(providerId, apiKey)

    // Update provider config
    aiConfigManager.setProviderConfig(providerId, {
      providerId,
      apiKey,
      enabled: true
    })

    // Test the connection
    const isAvailable = await aiProviderManager.checkProviderAvailability(providerId)

    return NextResponse.json({
      success: true,
      providerId,
      available: isAvailable,
      message: isAvailable 
        ? 'API key set and connection verified'
        : 'API key set but connection could not be verified',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('AI config PUT error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to update API key',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const id = searchParams.get('id')

    if (!type || !id) {
      return NextResponse.json(
        { error: 'Type and ID are required' },
        { status: 400 }
      )
    }

    switch (type) {
      case 'agent':
        // Reset agent to default configuration
        const defaultAgents = [
          {
            agentId: id,
            agentName: `${id} Agent`,
            primaryModel: 'anthropic/claude-3.5-sonnet',
            temperature: 0.3,
            maxTokens: 2048,
            systemPrompt: `You are a ${id} specialist.`,
            enabled: false,
            priority: 1
          }
        ]
        if (defaultAgents[0]) {
          aiConfigManager.setAgentConfig(defaultAgents[0])
        }
        break

      case 'provider':
        aiConfigManager.setProviderConfig(id, {
          providerId: id,
          enabled: false
        })
        break

      default:
        return NextResponse.json(
          { error: 'Invalid type. Must be: agent or provider' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      type,
      id,
      message: `${type} configuration reset successfully`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('AI config DELETE error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to reset configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
