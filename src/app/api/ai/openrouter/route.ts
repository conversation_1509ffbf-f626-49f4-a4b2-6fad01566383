import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { model, prompt, temperature = 0.3, maxTokens = 2048 } = await request.json()

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'CryptoAgent Pro'
      },
      body: JSON.stringify({
        model: model || 'meta-llama/llama-3.1-405b-instruct',
        messages: [
          {
            role: 'system',
            content: 'You are a professional crypto trading analyst. Provide accurate, well-reasoned analysis and trading recommendations.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: temperature,
        max_tokens: maxTokens,
        stream: false
      })
    })

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`)
    }

    const data = await response.json()
    
    return NextResponse.json({
      content: data.choices[0]?.message?.content || 'No response from AI model',
      model: model,
      tokens_used: data.usage?.total_tokens || 0
    })

  } catch (error) {
    console.error('❌ OpenRouter API error:', error)
    return NextResponse.json(
      { error: 'Failed to get AI response', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 