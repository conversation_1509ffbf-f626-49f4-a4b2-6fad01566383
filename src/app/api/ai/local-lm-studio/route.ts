import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { model, prompt, temperature = 0.3, maxTokens = 2048 } = await request.json()

    const lmStudioUrl = process.env.LM_STUDIO_BASE_URL || 'http://192.168.0.33:1235/v1'

    const response = await fetch(`${lmStudioUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model || process.env.LM_STUDIO_MODEL || 'deepseek-coder-33b-instruct',
        messages: [
          {
            role: 'system',
            content: 'You are a professional crypto trading analyst and software developer. Provide accurate, well-reasoned analysis, trading recommendations, and help with coding tasks.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: temperature,
        max_tokens: maxTokens,
        stream: false
      })
    })

    if (!response.ok) {
      throw new Error(`LM Studio API error: ${response.status}`)
    }

    const data = await response.json()
    
    return NextResponse.json({
      content: data.choices[0]?.message?.content || 'No response from AI model',
      model: model,
      tokens_used: data.usage?.total_tokens || 0
    })

  } catch (error) {
    console.error('❌ LM Studio API error:', error)
    return NextResponse.json(
      { error: 'Failed to get AI response', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 