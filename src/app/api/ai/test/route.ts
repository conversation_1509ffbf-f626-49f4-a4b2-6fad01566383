import { NextRequest, NextResponse } from 'next/server'
import { aiProviderManager } from '../../../../../lib/ai/AIProviderManager'
import { aiConfigManager } from '../../../../../lib/ai/AIConfigManager'

export async function POST(request: NextRequest) {
  try {
    const { providerId, modelId, testPrompt } = await request.json()

    if (!providerId) {
      return NextResponse.json(
        { error: 'Provider ID is required' },
        { status: 400 }
      )
    }

    // Check if provider is available
    const isAvailable = await aiProviderManager.checkProviderAvailability(providerId)
    if (!isAvailable) {
      return NextResponse.json(
        { 
          error: `Provider ${providerId} is not available`,
          available: false,
          timestamp: new Date().toISOString()
        },
        { status: 503 }
      )
    }

    // Get a test model if none specified
    const testModelId = modelId || aiConfigManager.getBestModelForAgent('technical-analysis')
    if (!testModelId) {
      return NextResponse.json(
        { error: 'No available models for testing' },
        { status: 400 }
      )
    }

    // Prepare test request
    const testRequest = {
      model: testModelId,
      messages: [
        {
          role: 'system' as const,
          content: 'You are a helpful AI assistant. Respond briefly and clearly.'
        },
        {
          role: 'user' as const,
          content: testPrompt || 'Hello! Please respond with a brief test message to confirm you are working correctly.'
        }
      ],
      temperature: 0.3,
      maxTokens: 100
    }

    const startTime = Date.now()
    
    try {
      // Make test request
      const response = await aiProviderManager.makeRequest(testModelId, testRequest)
      const endTime = Date.now()
      const responseTime = endTime - startTime

      // Track usage if successful
      if (response.usage) {
        const model = aiProviderManager.getModel(testModelId)
        if (model) {
          const cost = (response.usage.promptTokens / 1000000) * model.pricing.input +
                      (response.usage.completionTokens / 1000000) * model.pricing.output
          aiConfigManager.trackUsage(testModelId, response.usage.totalTokens, cost)
        }
      }

      return NextResponse.json({
        success: true,
        providerId,
        modelId: testModelId,
        response: {
          content: response.content,
          usage: response.usage
        },
        performance: {
          responseTime,
          timestamp: new Date().toISOString()
        },
        available: true
      })

    } catch (aiError) {
      return NextResponse.json(
        {
          error: `AI request failed: ${aiError instanceof Error ? aiError.message : 'Unknown error'}`,
          providerId,
          modelId: testModelId,
          available: true,
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('AI test endpoint error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    // Return available providers and models
    const providers = aiProviderManager.getProviders()
    const models = aiProviderManager.getModels()
    const recommendations = aiProviderManager.getRecommendedModels()
    
    // Check availability of each provider
    const providerStatus = await Promise.all(
      providers.map(async (provider) => ({
        ...provider,
        available: await aiProviderManager.checkProviderAvailability(provider.id)
      }))
    )

    return NextResponse.json({
      providers: providerStatus,
      models,
      recommendations,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('AI providers endpoint error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch AI providers',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
