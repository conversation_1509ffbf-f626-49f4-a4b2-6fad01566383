import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { model, prompt, temperature = 0.3, maxTokens = 2048 } = await request.json()

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: model || 'gpt-4-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a professional crypto trading analyst. Provide accurate, well-reasoned analysis and trading recommendations.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: temperature,
        max_tokens: maxTokens,
        stream: false
      })
    })

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`)
    }

    const data = await response.json()
    
    return NextResponse.json({
      content: data.choices[0]?.message?.content || 'No response from AI model',
      model: model,
      tokens_used: data.usage?.total_tokens || 0
    })

  } catch (error) {
    console.error('❌ OpenAI API error:', error)
    return NextResponse.json(
      { error: 'Failed to get AI response', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 