import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { provider, apiKey, baseUrl, selectedModel } = await request.json()

    // Validate input
    if (!provider) {
      return NextResponse.json({ error: 'Provider is required' }, { status: 400 })
    }

    // In a real application, you would save this to a database
    // For now, we'll just validate and return success
    const config = {
      provider,
      apiKey: apiKey ? '***' + apiKey.slice(-4) : undefined, // Mask API key
      baseUrl,
      selectedModel,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // Validate provider-specific requirements
    switch (provider) {
      case 'lm-studio':
        if (!baseUrl) {
          return NextResponse.json({ error: 'Base URL is required for LM Studio' }, { status: 400 })
        }
        break
      
      case 'openrouter':
      case 'gemini':
      case 'openai':
        if (!apiKey) {
          return NextResponse.json({ error: 'API key is required' }, { status: 400 })
        }
        break
      
      default:
        return NextResponse.json({ error: 'Unknown provider' }, { status: 400 })
    }

    // Here you would typically save to database
    // For now, we'll just return success
    console.log('Saving AI config:', { ...config, apiKey: '[HIDDEN]' })

    return NextResponse.json({
      success: true,
      message: `${provider} configuration saved successfully`,
      config: {
        provider,
        baseUrl,
        selectedModel,
        createdAt: config.createdAt
      }
    })

  } catch (error) {
    console.error('Error saving AI config:', error)
    return NextResponse.json(
      { error: 'Failed to save configuration' }, 
      { status: 500 }
    )
  }
} 