import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin, getTableStatus } from '../../../../lib/supabase-admin'

export async function GET() {
  try {
    // Test database connection using admin client
    const tableStatus = await getTableStatus()

    const tablesExist = Object.values(tableStatus).filter(Boolean).length
    const totalTables = Object.keys(tableStatus).length

    return NextResponse.json({
      success: true,
      message: 'Supabase connection successful!',
      details: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL,
        tablesExist: `${tablesExist}/${totalTables}`,
        tableStatus,
        needsSchema: tablesExist === 0,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
