import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function GET() {
  try {
    // Get environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json({
        success: false,
        error: 'Missing Supabase environment variables',
        details: {
          hasUrl: !!supabaseUrl,
          hasServiceKey: !!supabaseServiceKey,
          hasAnonKey: !!supabaseAnonKey
        }
      }, { status: 500 })
    }

    // Create Supabase client with service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Test database connection by checking tables
    const tables = ['profiles', 'trading_sessions', 'trades', 'portfolios', 'alerts']
    const tableStatus: Record<string, boolean> = {}

    for (const table of tables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('count')
          .limit(0)

        tableStatus[table] = !error || error.code !== 'PGRST116' // PGRST116 = table doesn't exist
      } catch (error) {
        tableStatus[table] = false
      }
    }

    const tablesExist = Object.values(tableStatus).filter(Boolean).length
    const totalTables = tables.length

    return NextResponse.json({
      success: true,
      message: 'Supabase connection successful!',
      details: {
        url: supabaseUrl,
        tablesExist: `${tablesExist}/${totalTables}`,
        tableStatus,
        needsSchema: tablesExist === 0,
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
