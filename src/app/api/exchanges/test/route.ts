import { NextRequest, NextResponse } from 'next/server'
import * as ccxt from 'ccxt'

export async function POST(request: NextRequest) {
  try {
    const { exchangeId, credentials, testMode } = await request.json()

    if (!exchangeId || !credentials) {
      return NextResponse.json(
        { error: 'Exchange ID and credentials are required' },
        { status: 400 }
      )
    }

    // Validate credentials
    if (!credentials.apiKey || !credentials.apiSecret) {
      return NextResponse.json(
        { error: 'API key and secret are required' },
        { status: 400 }
      )
    }

    // Special validation for KuCoin
    if (exchangeId === 'kucoin' && !credentials.passphrase) {
      return NextResponse.json(
        { error: 'Passphrase is required for KuCoin' },
        { status: 400 }
      )
    }

    try {
      // Initialize exchange
      const exchangeClass = ccxt[exchangeId as keyof typeof ccxt] as any
      if (!exchangeClass) {
        return NextResponse.json(
          { error: `Exchange ${exchangeId} is not supported` },
          { status: 400 }
        )
      }

      const exchangeConfig: any = {
        apiKey: credentials.apiKey,
        secret: credentials.apiSecret,
        enableRateLimit: true,
        timeout: 10000
      }

      // Only set sandbox for exchanges that support it
      if (exchangeId === 'binance' || exchangeId === 'bybit') {
        exchangeConfig.sandbox = testMode || credentials.testnet || false
      }

      // MEXC and KuCoin don't have sandbox mode, use live API with testnet flag
      if (exchangeId === 'mexc' || exchangeId === 'kucoin') {
        exchangeConfig.testnet = credentials.testnet || false
      }

      // Add passphrase for exchanges that require it (KuCoin)
      if (credentials.passphrase && exchangeId === 'kucoin') {
        exchangeConfig.passphrase = credentials.passphrase
      }

      // Add sub-account if specified
      if (credentials.subAccount) {
        exchangeConfig.headers = {
          'X-MBX-APIKEY-SUBACCOUNT': credentials.subAccount
        }
      }

      // Exchange-specific configurations
      if (exchangeId === 'mexc') {
        exchangeConfig.urls = {
          api: testMode ? 'https://api.mexc.com' : 'https://api.mexc.com'
        }
      }

      const exchange = new exchangeClass(exchangeConfig)

      // Test connection by fetching account balance
      const startTime = Date.now()
      const balance = await exchange.fetchBalance()
      const endTime = Date.now()
      const responseTime = endTime - startTime

      // Get exchange info
      const markets = await exchange.loadMarkets()
      const marketCount = Object.keys(markets).length

      // Get some basic account info
      const accountInfo = {
        totalBalance: 0,
        currencies: [] as string[],
        permissions: [] as string[]
      }

      if (balance && balance.total) {
        accountInfo.totalBalance = Object.values(balance.total as Record<string, number>)
          .reduce((sum, amount) => sum + (amount || 0), 0)
        accountInfo.currencies = Object.keys(balance.total).filter(currency => 
          (balance.total as Record<string, number>)[currency] > 0
        )
      }

      // Test a simple API call to verify permissions
      try {
        await exchange.fetchTicker('BTC/USDT')
        accountInfo.permissions.push('read_public')
      } catch (error) {
        // Public data should always work
      }

      try {
        await exchange.fetchMyTrades('BTC/USDT', undefined, 1)
        accountInfo.permissions.push('read_private')
      } catch (error) {
        // Private data might not work if no trades exist
      }

      return NextResponse.json({
        success: true,
        exchangeId,
        testMode: exchangeConfig.sandbox,
        responseTime,
        accountInfo,
        marketCount,
        message: 'Connection successful',
        timestamp: new Date().toISOString()
      })

    } catch (exchangeError: any) {
      console.error(`Exchange ${exchangeId} connection error:`, exchangeError)
      
      let errorMessage = 'Connection failed'
      let errorCode = 'UNKNOWN_ERROR'

      if (exchangeError.message) {
        if (exchangeError.message.includes('Invalid API-key')) {
          errorMessage = 'Invalid API key'
          errorCode = 'INVALID_API_KEY'
        } else if (exchangeError.message.includes('Invalid signature')) {
          errorMessage = 'Invalid API secret or signature'
          errorCode = 'INVALID_SIGNATURE'
        } else if (exchangeError.message.includes('IP not allowed')) {
          errorMessage = 'IP address not whitelisted'
          errorCode = 'IP_NOT_ALLOWED'
        } else if (exchangeError.message.includes('Timestamp')) {
          errorMessage = 'Timestamp synchronization error'
          errorCode = 'TIMESTAMP_ERROR'
        } else if (exchangeError.message.includes('Permission')) {
          errorMessage = 'Insufficient API permissions'
          errorCode = 'INSUFFICIENT_PERMISSIONS'
        } else {
          errorMessage = exchangeError.message
        }
      }

      return NextResponse.json(
        {
          success: false,
          exchangeId,
          error: errorMessage,
          errorCode,
          details: exchangeError.message,
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Exchange test endpoint error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    // Return supported exchanges and their capabilities
    const supportedExchanges = [
      {
        id: 'binance',
        name: 'Binance',
        features: ['spot', 'futures', 'margin'],
        testnetSupported: true,
        requiredCredentials: ['apiKey', 'apiSecret'],
        optionalCredentials: ['subAccount']
      },
      {
        id: 'bybit',
        name: 'Bybit',
        features: ['spot', 'futures'],
        testnetSupported: true,
        requiredCredentials: ['apiKey', 'apiSecret'],
        optionalCredentials: []
      },
      {
        id: 'mexc',
        name: 'MEXC',
        features: ['spot', 'futures'],
        testnetSupported: false,
        requiredCredentials: ['apiKey', 'apiSecret'],
        optionalCredentials: []
      },
      {
        id: 'kucoin',
        name: 'KuCoin',
        features: ['spot', 'futures'],
        testnetSupported: true,
        requiredCredentials: ['apiKey', 'apiSecret', 'passphrase'],
        optionalCredentials: []
      },
      {
        id: 'okx',
        name: 'OKX',
        features: ['spot', 'futures'],
        testnetSupported: true,
        requiredCredentials: ['apiKey', 'apiSecret', 'passphrase'],
        optionalCredentials: []
      }
    ]

    return NextResponse.json({
      supportedExchanges,
      totalSupported: supportedExchanges.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Exchange info endpoint error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch exchange information',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
