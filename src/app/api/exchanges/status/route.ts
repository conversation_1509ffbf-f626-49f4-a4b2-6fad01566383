import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Get exchange configurations from environment variables
    const exchanges = [
      {
        id: 'mexc',
        name: 'MEXC',
        displayName: 'MEXC',
        description: 'Global digital asset trading platform',
        enabled: !!(process.env.MEXC_API_KEY && process.env.MEXC_SECRET_KEY),
        testMode: process.env.MEXC_TESTNET === 'true',
        connectionStatus: getConnectionStatus('mexc'),
        credentials: {
          hasApiKey: !!process.env.MEXC_API_KEY,
          hasSecretKey: !!process.env.MEXC_SECRET_KEY,
          testnet: process.env.MEXC_TESTNET === 'true'
        },
        fees: { maker: 0.002, taker: 0.002 },
        features: { spot: true, futures: true, margin: true }
      },
      {
        id: 'kucoin',
        name: '<PERSON><PERSON>oi<PERSON>',
        displayName: 'KuCoin',
        description: 'The People\'s Exchange',
        enabled: !!(process.env.KUCOIN_API_KEY && process.env.KUCOIN_SECRET_KEY && process.env.KUCOIN_PASSPHRASE),
        testMode: process.env.KUCOIN_TESTNET === 'true',
        connectionStatus: getConnectionStatus('kucoin'),
        credentials: {
          hasApiKey: !!process.env.KUCOIN_API_KEY,
          hasSecretKey: !!process.env.KUCOIN_SECRET_KEY,
          hasPassphrase: !!process.env.KUCOIN_PASSPHRASE,
          testnet: process.env.KUCOIN_TESTNET === 'true'
        },
        fees: { maker: 0.001, taker: 0.001 },
        features: { spot: true, futures: true, margin: true }
      },
      {
        id: 'binance',
        name: 'Binance',
        displayName: 'Binance',
        description: 'World\'s leading cryptocurrency exchange',
        enabled: !!(process.env.BINANCE_API_KEY && process.env.BINANCE_SECRET_KEY),
        testMode: process.env.BINANCE_TESTNET === 'true',
        connectionStatus: getConnectionStatus('binance'),
        credentials: {
          hasApiKey: !!process.env.BINANCE_API_KEY,
          hasSecretKey: !!process.env.BINANCE_SECRET_KEY,
          testnet: process.env.BINANCE_TESTNET === 'true'
        },
        fees: { maker: 0.001, taker: 0.001 },
        features: { spot: true, futures: true, margin: true }
      },
      {
        id: 'bybit',
        name: 'Bybit',
        displayName: 'Bybit',
        description: 'Crypto derivatives exchange',
        enabled: !!(process.env.BYBIT_API_KEY && process.env.BYBIT_SECRET_KEY),
        testMode: process.env.BYBIT_TESTNET === 'true',
        connectionStatus: getConnectionStatus('bybit'),
        credentials: {
          hasApiKey: !!process.env.BYBIT_API_KEY,
          hasSecretKey: !!process.env.BYBIT_SECRET_KEY,
          testnet: process.env.BYBIT_TESTNET === 'true'
        },
        fees: { maker: 0.001, taker: 0.001 },
        features: { spot: true, futures: true, margin: true }
      }
    ]

    const enabledExchanges = exchanges.filter(ex => ex.enabled)
    const connectedExchanges = exchanges.filter(ex => ex.connectionStatus === 'connected')

    return NextResponse.json({
      exchanges,
      summary: {
        total: exchanges.length,
        enabled: enabledExchanges.length,
        connected: connectedExchanges.length,
        testMode: process.env.SANDBOX_MODE === 'true'
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Exchange status error:', error)
    return NextResponse.json(
      { error: 'Failed to get exchange status' },
      { status: 500 }
    )
  }
}

function getConnectionStatus(exchangeId: string): 'connected' | 'disconnected' | 'error' {
  // Check if credentials are configured
  switch (exchangeId) {
    case 'mexc':
      return (process.env.MEXC_API_KEY && process.env.MEXC_SECRET_KEY) ? 'connected' : 'disconnected'
    case 'kucoin':
      return (process.env.KUCOIN_API_KEY && process.env.KUCOIN_SECRET_KEY && process.env.KUCOIN_PASSPHRASE) ? 'connected' : 'disconnected'
    case 'binance':
      return (process.env.BINANCE_API_KEY && process.env.BINANCE_SECRET_KEY) ? 'connected' : 'disconnected'
    case 'bybit':
      return (process.env.BYBIT_API_KEY && process.env.BYBIT_SECRET_KEY) ? 'connected' : 'disconnected'
    default:
      return 'disconnected'
  }
}
