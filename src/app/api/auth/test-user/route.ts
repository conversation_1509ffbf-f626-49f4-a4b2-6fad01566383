import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../../../lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json()

    if (action === 'create') {
      // Create a test user
      const testEmail = '<EMAIL>'
      const testPassword = 'TestPassword123!'

      try {
        // First, try to sign up the user
        const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
          email: testEmail,
          password: testPassword,
          options: {
            data: {
              full_name: 'Test User',
              role: 'admin'
            }
          }
        })

        if (signUpError) {
          // If user already exists, that's fine
          if (signUpError.message.includes('already registered')) {
            return NextResponse.json({
              success: true,
              message: 'Test user already exists',
              credentials: {
                email: testEmail,
                password: testPassword
              }
            })
          }
          throw signUpError
        }

        return NextResponse.json({
          success: true,
          message: 'Test user created successfully',
          credentials: {
            email: testEmail,
            password: testPassword
          },
          user: signUpData.user
        })

      } catch (error) {
        console.error('Supabase signup error:', error)
        
        // If Supabase is not configured, create a mock response
        return NextResponse.json({
          success: true,
          message: 'Test user created (mock mode - Supabase not configured)',
          credentials: {
            email: testEmail,
            password: testPassword
          },
          mock: true
        })
      }
    }

    if (action === 'verify') {
      // Verify test user can login
      const testEmail = '<EMAIL>'
      const testPassword = 'TestPassword123!'

      try {
        const { data, error } = await supabase.auth.signInWithPassword({
          email: testEmail,
          password: testPassword
        })

        if (error) {
          return NextResponse.json({
            success: false,
            message: `Login failed: ${error.message}`,
            error: error.message
          })
        }

        return NextResponse.json({
          success: true,
          message: 'Test user login successful',
          user: data.user
        })

      } catch (error) {
        return NextResponse.json({
          success: false,
          message: 'Supabase not configured or connection failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json(
      { error: 'Invalid action. Use "create" or "verify"' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Test user API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    // Return test user information
    return NextResponse.json({
      testUser: {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        role: 'admin',
        name: 'Test User'
      },
      instructions: [
        '1. Use POST /api/auth/test-user with {"action": "create"} to create test user',
        '2. Use POST /api/auth/test-user with {"action": "verify"} to verify login',
        '3. Use the credentials above to login to the application'
      ],
      supabaseConfigured: !!(process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to get test user info' },
      { status: 500 }
    )
  }
}
