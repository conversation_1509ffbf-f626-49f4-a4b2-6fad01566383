import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../../lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const results: any = {
      timestamp: new Date().toISOString(),
      status: 'testing',
      checks: {}
    }

    // Test 1: Basic connection
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('count')
        .limit(1)
      
      results.checks.connection = {
        success: !error,
        error: error?.message || null
      }
    } catch (error) {
      results.checks.connection = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }

    // Test 2: Organizations table
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .limit(5)
      
      results.checks.organizations = {
        success: !error,
        count: data?.length || 0,
        error: error?.message || null
      }
    } catch (error) {
      results.checks.organizations = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }

    // Test 3: Trading agents table
    try {
      const { data, error } = await supabase
        .from('trading_agents')
        .select('*')
        .limit(5)
      
      results.checks.trading_agents = {
        success: !error,
        count: data?.length || 0,
        error: error?.message || null
      }
    } catch (error) {
      results.checks.trading_agents = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }

    // Test 4: Portfolio snapshots table
    try {
      const { data, error } = await supabase
        .from('portfolio_snapshots')
        .select('*')
        .limit(5)
      
      results.checks.portfolio_snapshots = {
        success: !error,
        count: data?.length || 0,
        error: error?.message || null
      }
    } catch (error) {
      results.checks.portfolio_snapshots = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }

    // Test 5: RLS policies (if authenticated)
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session) {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .limit(1)
        
        results.checks.rls_policies = {
          success: !error,
          authenticated: true,
          error: error?.message || null
        }
      } else {
        results.checks.rls_policies = {
          success: true,
          authenticated: false,
          message: 'No session, RLS policies working correctly'
        }
      }
    } catch (error) {
      results.checks.rls_policies = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }

    // Determine overall status
    const allChecks = Object.values(results.checks)
    const failedChecks = allChecks.filter((check: any) => !check.success)
    
    results.status = failedChecks.length === 0 ? 'healthy' : 'issues_found'
    results.summary = {
      total_checks: allChecks.length,
      passed_checks: allChecks.length - failedChecks.length,
      failed_checks: failedChecks.length
    }

    return NextResponse.json(results)

  } catch (error) {
    console.error('❌ Database test error:', error)
    return NextResponse.json(
      { 
        error: 'Database test failed', 
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
} 