import { NextRequest, NextResponse } from 'next/server'
import ccxt from 'ccxt'
import { logger, classifyExchangeError, retryWithBackoff } from '../../../lib/utils/logger'

// Cache for exchange instances
const exchangeCache = new Map<string, ccxt.Exchange>()

// Initialize exchange instance
function getExchange(exchangeId: string): ccxt.Exchange | null {
  if (exchangeCache.has(exchangeId)) {
    return exchangeCache.get(exchangeId)!
  }

  try {
    let config: any = {
      enableRateLimit: true,
      timeout: 10000
    }

    // Add credentials if available
    if (exchangeId === 'mexc') {
      config.apiKey = process.env.MEXC_API_KEY
      config.secret = process.env.MEXC_SECRET_KEY
    } else if (exchangeId === 'kucoin') {
      config.apiKey = process.env.KUCOIN_API_KEY
      config.secret = process.env.KUCOIN_SECRET_KEY
      config.passphrase = process.env.KUCOIN_PASSPHRASE
    } else if (exchangeId === 'binance') {
      config.apiKey = process.env.BINANCE_API_KEY
      config.secret = process.env.BINANCE_SECRET_KEY
    } else if (exchangeId === 'bybit') {
      config.apiKey = process.env.BYBIT_API_KEY
      config.secret = process.env.BYBIT_SECRET_KEY
    }

    const exchangeClass = (ccxt as any)[exchangeId]
    if (!exchangeClass) {
      return null
    }

    const exchange = new exchangeClass(config)
    exchangeCache.set(exchangeId, exchange)
    return exchange
  } catch (error) {
    console.error(`Failed to initialize ${exchangeId}:`, error)
    return null
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const exchange = searchParams.get('exchange') || 'mexc'
  const symbol = searchParams.get('symbol') || 'BTC/USDT'
  const type = searchParams.get('type') || 'ticker'

  logger.info('MarketData', `Fetching ${type} data for ${symbol} from ${exchange}`)

  try {
    const exchangeInstance = getExchange(exchange)
    if (!exchangeInstance) {
      logger.error('MarketData', `Exchange ${exchange} not supported or not configured`)
      return NextResponse.json(
        { error: `Exchange ${exchange} not supported or not configured` },
        { status: 400 }
      )
    }

    // Load markets if not already loaded
    if (!exchangeInstance.markets || Object.keys(exchangeInstance.markets).length === 0) {
      await exchangeInstance.loadMarkets()
    }

    // Check if symbol exists
    if (!exchangeInstance.markets[symbol]) {
      return NextResponse.json(
        { error: `Symbol ${symbol} not found on ${exchange}` },
        { status: 400 }
      )
    }

    let data: any = {}

    switch (type) {
      case 'ticker':
        const ticker = await exchangeInstance.fetchTicker(symbol)
        data = {
          symbol,
          exchange,
          price: ticker.last,
          bid: ticker.bid,
          ask: ticker.ask,
          volume: ticker.baseVolume,
          change24h: ticker.change,
          percentage24h: ticker.percentage,
          high24h: ticker.high,
          low24h: ticker.low,
          timestamp: ticker.timestamp,
          datetime: ticker.datetime
        }
        break

      case 'orderbook':
        const limit = parseInt(searchParams.get('limit') || '10')
        const orderbook = await exchangeInstance.fetchOrderBook(symbol, limit)
        data = {
          symbol,
          exchange,
          bids: orderbook.bids.slice(0, limit),
          asks: orderbook.asks.slice(0, limit),
          timestamp: orderbook.timestamp,
          datetime: orderbook.datetime
        }
        break

      case 'trades':
        const tradesLimit = parseInt(searchParams.get('limit') || '50')
        const trades = await exchangeInstance.fetchTrades(symbol, undefined, tradesLimit)
        data = {
          symbol,
          exchange,
          trades: trades.map(trade => ({
            id: trade.id,
            price: trade.price,
            amount: trade.amount,
            side: trade.side,
            timestamp: trade.timestamp,
            datetime: trade.datetime
          })),
          count: trades.length
        }
        break

      case 'ohlcv':
        const timeframe = searchParams.get('timeframe') || '1m'
        const ohlcvLimit = parseInt(searchParams.get('limit') || '100')
        const ohlcv = await exchangeInstance.fetchOHLCV(symbol, timeframe, undefined, ohlcvLimit)
        data = {
          symbol,
          exchange,
          timeframe,
          candles: ohlcv.map(candle => ({
            timestamp: candle[0],
            datetime: new Date(candle[0]).toISOString(),
            open: candle[1],
            high: candle[2],
            low: candle[3],
            close: candle[4],
            volume: candle[5]
          })),
          count: ohlcv.length
        }
        break

      case 'markets':
        const markets = Object.values(exchangeInstance.markets)
          .filter((market: any) => market.active)
          .slice(0, 100)
          .map((market: any) => ({
            symbol: market.symbol,
            base: market.base,
            quote: market.quote,
            active: market.active,
            type: market.type,
            spot: market.spot,
            future: market.future,
            limits: market.limits
          }))
        
        data = {
          exchange,
          markets,
          count: markets.length,
          total: Object.keys(exchangeInstance.markets).length
        }
        break

      default:
        return NextResponse.json(
          { error: `Data type ${type} not supported. Use: ticker, orderbook, trades, ohlcv, markets` },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      data,
      timestamp: Date.now(),
      cached: false
    })

  } catch (error) {
    console.error(`Market data error for ${exchange}:`, error)
    
    return NextResponse.json(
      { 
        error: `Failed to fetch ${type} data from ${exchange}`,
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST endpoint for batch requests
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { requests } = body

    if (!Array.isArray(requests)) {
      return NextResponse.json(
        { error: 'Requests must be an array' },
        { status: 400 }
      )
    }

    const results = await Promise.allSettled(
      requests.map(async (req: any) => {
        const { exchange, symbol, type } = req
        const exchangeInstance = getExchange(exchange)
        
        if (!exchangeInstance) {
          throw new Error(`Exchange ${exchange} not supported`)
        }

        if (!exchangeInstance.markets || Object.keys(exchangeInstance.markets).length === 0) {
          await exchangeInstance.loadMarkets()
        }

        switch (type) {
          case 'ticker':
            return await exchangeInstance.fetchTicker(symbol)
          case 'orderbook':
            return await exchangeInstance.fetchOrderBook(symbol, 10)
          default:
            throw new Error(`Type ${type} not supported in batch`)
        }
      })
    )

    const data = results.map((result, index) => ({
      request: requests[index],
      success: result.status === 'fulfilled',
      data: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason.message : null
    }))

    return NextResponse.json({
      success: true,
      results: data,
      timestamp: Date.now()
    })

  } catch (error) {
    return NextResponse.json(
      { error: 'Batch request failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
