'use client'

import { useState, useEffect } from 'react'
import ProtectedRoute from '../../../components/auth/ProtectedRoute'
import { useAuth } from '../../../lib/hooks/useAuth'
import { getUserProfile, getUserTrades } from '../../../lib/supabase'
import TradingInterface from '../../../components/trading/TradingInterface'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Badge } from '../../../components/ui/badge'
import { Button } from '../../../components/ui/button'
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Activity,
  Users,
  BarChart3,
  Settings,
  Bell,
  Plus,
  LogOut,
  User
} from 'lucide-react'

interface DashboardData {
  profile: any
  trades: any[]
  loading: boolean
}

function DashboardContent() {
  const { user, signOut } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    profile: null,
    trades: [],
    loading: true
  })

  useEffect(() => {
    const loadDashboardData = async () => {
      if (!user) return

      try {
        const [profile, trades] = await Promise.all([
          getUserProfile(user.id),
          getUserTrades(user.id, 10)
        ])

        setDashboardData({
          profile,
          trades,
          loading: false
        })
      } catch (error) {
        console.error('Error loading dashboard data:', error)
        setDashboardData(prev => ({ ...prev, loading: false }))
      }
    }

    loadDashboardData()
  }, [user])

  const handleSignOut = async () => {
    await signOut()
  }

  if (dashboardData.loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                <span className="text-white font-bold text-sm">CA</span>
              </div>
              <h1 className="text-xl font-semibold text-gray-900">CryptoAgent Pro</h1>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex space-x-1">
                <Button
                  variant={activeTab === 'overview' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setActiveTab('overview')}
                >
                  Overview
                </Button>
                <Button
                  variant={activeTab === 'trading' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setActiveTab('trading')}
                >
                  Trading
                </Button>
                <Button
                  variant={activeTab === 'portfolio' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setActiveTab('portfolio')}
                >
                  Portfolio
                </Button>
              </div>
              <Button variant="outline" size="sm">
                <Bell className="h-4 w-4 mr-2" />
                Alerts
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
              <Button variant="outline" size="sm" onClick={handleSignOut}>
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome back, {dashboardData.profile?.name || user?.email}!
          </h2>
          <p className="text-gray-600">
            {activeTab === 'overview' && "Here's what's happening with your trading portfolio today."}
            {activeTab === 'trading' && "Manage your AI trading sessions and strategies."}
            {activeTab === 'portfolio' && "View and analyze your cryptocurrency holdings."}
          </p>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Portfolio</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">$12,345.67</div>
                  <p className="text-xs text-muted-foreground">
                    <span className="text-green-600">+2.5%</span> from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Trades</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{dashboardData.trades.length}</div>
                  <p className="text-xs text-muted-foreground">
                    3 profitable, 1 pending
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">AI Agents</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">3</div>
                  <p className="text-xs text-muted-foreground">
                    2 active, 1 learning
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">78.5%</div>
                  <p className="text-xs text-muted-foreground">
                    <span className="text-green-600">+5.2%</span> this week
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Recent Trades and Profile */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Trades</CardTitle>
                </CardHeader>
                <CardContent>
                  {dashboardData.trades.length > 0 ? (
                    <div className="space-y-4">
                      {dashboardData.trades.slice(0, 5).map((trade, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className={`w-2 h-2 rounded-full ${trade.side === 'buy' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                            <div>
                              <p className="font-medium">{trade.symbol}</p>
                              <p className="text-sm text-gray-500">{trade.exchange}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">${trade.amount}</p>
                            <Badge variant={trade.side === 'buy' ? 'default' : 'secondary'}>
                              {trade.side.toUpperCase()}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">No trades yet</p>
                      <Button className="mt-4" onClick={() => setActiveTab('trading')}>
                        <Plus className="h-4 w-4 mr-2" />
                        Start Trading
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Profile Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <User className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="font-medium">{dashboardData.profile?.name || 'Not set'}</p>
                        <p className="text-sm text-gray-500">Full Name</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Badge variant="outline">{dashboardData.profile?.role || 'USER'}</Badge>
                      <span className="text-sm text-gray-500">Account Type</span>
                    </div>
                    <div className="pt-4">
                      <Button variant="outline" className="w-full">
                        <Settings className="h-4 w-4 mr-2" />
                        Edit Profile
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        )}

        {activeTab === 'trading' && (
          <TradingInterface />
        )}

        {activeTab === 'portfolio' && (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">Portfolio view coming soon!</p>
            <Button onClick={() => window.open('/portfolio', '_blank')}>
              Open Full Portfolio
            </Button>
          </div>
        )}
      </main>
    </div>
  )
}

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  )
}