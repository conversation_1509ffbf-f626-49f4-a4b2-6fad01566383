'use client'

import { Brain, <PERSON><PERSON><PERSON>, TrendingUp, Shield, Zap, Menu, X, Bitcoin, Repeat } from 'lucide-react'
import Link from 'next/link'
import { useState } from 'react'
import { motion } from 'framer-motion';

export default function Home() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <div className="bg-gray-900 border-b border-gray-800 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">CryptoAgent Pro</h1>
              <p className="text-gray-400">AI-Powered Trading Platform</p>
            </div>
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4">
            <Link 
              href="/ai-config"
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
            >
              <Brain className="w-5 h-5" />
              <span>AI Config</span>
            </Link>
            <Link 
              href="/auth/login"
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
            >
              <span>Login</span>
            </Link>
            <Link 
              href="/auth/register"
              className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
            >
              <span>Register</span>
            </Link>
            <Link 
              href="/settings"
              className="flex items-center space-x-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
            >
              <Settings className="w-5 h-5" />
              <span>Settings</span>
            </Link>
          </div>

          {/* AI Robot Icons */}
          <div className="hidden md:flex items-center space-x-4">
            <Link href="/crypto-dashboard" className="relative">
              <motion.div
                animate={{ y: [0, -3, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              >
                <Bitcoin className="w-8 h-8 text-yellow-500" />
              </motion.div>
            </Link>
            <Link href="/ai-config" className="relative">
              <motion.div
                animate={{ y: [0, -3, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut", delay: 0.2 }}
              >
                <Brain className="w-8 h-8 text-blue-500 opacity-70" />  // Verhoogde transparantie voor doorschijnend effect
              </motion.div>
            </Link>
            <Link href="/exchange-config" className="relative">
              <motion.div
                animate={{ y: [0, -3, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut", delay: 0.4 }}
              >
                <Repeat className="w-8 h-8 text-green-500" />  // Icoon voor exchange, ondersteunt configuratie voor meerdere
              </motion.div>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-lg bg-gray-800 hover:bg-gray-700"
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden mt-4 space-y-2">
            <Link 
              href="/ai-config"
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              <Brain className="w-5 h-5" />
              <span>AI Config</span>
            </Link>
            <Link 
              href="/auth/login"
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              <span>Login</span>
            </Link>
            <Link 
              href="/auth/register"
              className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              <span>Register</span>
            </Link>
            <Link 
              href="/settings"
              className="flex items-center space-x-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              <Settings className="w-5 h-5" />
              <span>Settings</span>
            </Link>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* AI Trading Dashboard */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="flex items-center space-x-3 mb-4">
              <Brain className="w-8 h-8 text-blue-400" />
              <h2 className="text-xl font-bold">AI Trading</h2>
            </div>
            <p className="text-gray-400 mb-4">
              Configure AI models for advanced trading analysis and automated decisions.
            </p>
            <Link 
              href="/ai-config"
              className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
            >
              <span>Configure AI</span>
              <Zap className="w-4 h-4" />
            </Link>
          </div>

          {/* Trading Engine */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="flex items-center space-x-3 mb-4">
              <TrendingUp className="w-8 h-8 text-green-400" />
              <h2 className="text-xl font-bold">Trading Engine</h2>
            </div>
            <p className="text-gray-400 mb-4">
              Monitor and control automated trading operations with real-time analytics.
            </p>
            <div className="flex items-center space-x-2 text-green-400">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-sm">Engine Running</span>
            </div>
          </div>

          {/* Security & Monitoring */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="flex items-center space-x-3 mb-4">
              <Shield className="w-8 h-8 text-yellow-400" />
              <h2 className="text-xl font-bold">Security</h2>
            </div>
            <p className="text-gray-400 mb-4">
              Advanced security monitoring and risk management for your trading operations.
            </p>
            <div className="flex items-center space-x-2 text-yellow-400">
              <div className="w-2 h-2 bg-yellow-400 rounded-full" />
              <span className="text-sm">All Systems Secure</span>
            </div>
          </div>

          {/* Mobile Control */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">📱</span>
              </div>
              <h2 className="text-xl font-bold">Mobile Control</h2>
            </div>
            <p className="text-gray-400 mb-4">
              Control your trading operations from anywhere with voice commands.
            </p>
            <div className="flex items-center space-x-2 text-purple-400">
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" />
              <span className="text-sm">Voice Ready</span>
            </div>
          </div>

          {/* Portfolio Overview */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">📊</span>
              </div>
              <h2 className="text-xl font-bold">Portfolio</h2>
            </div>
            <p className="text-gray-400 mb-4">
              Real-time portfolio tracking with AI-powered insights and recommendations.
            </p>
            <div className="text-2xl font-bold text-green-400">
              $0.00
            </div>
            <p className="text-sm text-gray-500">Total Value</p>
          </div>

          {/* AI Agents Status */}
          <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">🤖</span>
              </div>
              <h2 className="text-xl font-bold">AI Agents</h2>
            </div>
            <p className="text-gray-400 mb-4">
              Monitor the status of your AI trading agents and their performance.
            </p>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Technical Agent</span>
                <div className="w-2 h-2 bg-green-400 rounded-full" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Sentiment Agent</span>
                <div className="w-2 h-2 bg-blue-400 rounded-full" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Risk Agent</span>
                <div className="w-2 h-2 bg-yellow-400 rounded-full" />
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
          <Link 
            href="/auth/login"
            className="p-4 bg-green-600 hover:bg-green-700 rounded-lg text-center transition-colors"
          >
            <div className="text-lg font-semibold">Login</div>
            <div className="text-sm text-green-200">Access your account</div>
          </Link>
          
          <Link 
            href="/auth/register"
            className="p-4 bg-purple-600 hover:bg-purple-700 rounded-lg text-center transition-colors"
          >
            <div className="text-lg font-semibold">Register</div>
            <div className="text-sm text-purple-200">Create new account</div>
          </Link>
          
          <Link 
            href="/ai-config"
            className="p-4 bg-blue-600 hover:bg-blue-700 rounded-lg text-center transition-colors"
          >
            <div className="text-lg font-semibold">AI Config</div>
            <div className="text-sm text-blue-200">Configure AI models</div>
          </Link>
          
          <Link 
            href="/settings"
            className="p-4 bg-gray-700 hover:bg-gray-600 rounded-lg text-center transition-colors"
          >
            <div className="text-lg font-semibold">Settings</div>
            <div className="text-sm text-gray-300">Configure platform</div>
          </Link>
        </div>

        {/* Status Bar */}
        <div className="mt-8 bg-gray-800 rounded-xl p-4 border border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                <span className="text-sm text-green-400">System Online</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full" />
                <span className="text-sm text-blue-400">AI Ready</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full" />
                <span className="text-sm text-yellow-400">Mock Mode</span>
              </div>
            </div>
            <div className="text-sm text-gray-400">
              Port: 3002 | Environment: Development
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
