
import { vi, describe, it, expect } from 'vitest';
import { tradingEngine, OrderRequest } from '../lib/trading/TradingEngine';
import { supabase } from '../lib/supabase';

// Mock supabase voor tests
vi.mock('../lib/supabase', () => ({
  supabase: {
    from: vi.fn().mockReturnValue({
      insert: vi.fn().mockReturnValue({ select: vi.fn().mockReturnValue({ single: vi.fn().mockResolvedValue({ data: { id: 'test-id' } }) }) }),
      update: vi.fn().mockReturnValue({ eq: vi.fn().mockResolvedValue({}) }),
      select: vi.fn().mockReturnValue({ eq: vi.fn().mockReturnValue({ single: vi.fn().mockResolvedValue({ data: { symbol: 'BTC', side: 'BUY', entry_price: 50000, size: 1 } }) }) })
    })
  }
}));

describe('TradingEngine', () => {
  it('should place an order successfully', async () => {
    const orderRequest: OrderRequest = {
      symbol: 'BTC',
      side: 'BUY',
      size: 1,
      type: 'MARKET',
      agent_id: 'test-agent',
      organization_id: 'test-org'
    };

    const result = await tradingEngine.placeOrder(orderRequest);

    expect(result.success).toBe(true);
    expect(result.order_id).toBeDefined();
  });

  it('should close a position successfully', async () => {
    const positionId = 'test-position';
    const result = await tradingEngine.closePosition(positionId);

    expect(result.success).toBe(true);
    expect(result.order_id).toBe(positionId);
  });
});
