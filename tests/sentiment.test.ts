import { SentimentAnalysisAgentV3 } from '../lib/agents/SentimentAnalysisAgentV3';
import { describe, it, expect, vi } from 'vitest';

describe('SentimentAnalysisAgentV3', () => {
  // Test voor basis sentiment analyse
  it('should analyze sentiment correctly', async () => {
    const agent = new SentimentAnalysisAgentV3();
    const symbol = 'BTC';
    vi.spyOn(agent, 'analyze').mockResolvedValue({
      type: 'sentiment_analysis_v3',
      symbol: 'BTC',
      recommendation: 'BUY',
      confidence: 0.8,
      reasoning: 'Positive market sentiment',
      data: {},
      timestamp: new Date()
    });
    const result = await agent.analyze(symbol);
    expect(result.recommendation).toBe('BUY');
  });
});