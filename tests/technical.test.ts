import { vi, describe, it, expect } from 'vitest';
import { TechnicalAnalysisAgent } from '../lib/agents/TechnicalAnalysisAgent';
import { MarketData } from '../lib/agents/TechnicalAnalysisAgent'; // Importeer de MarketData interface

// Mock data voor de test
// Dit is voorbeeld market data om de analyze methode te testen.
const mockMarketData: MarketData = {
  symbol: 'BTC',
  currentPrice: 50000,
  volume: 1000000,
  ohlcv: [[Date.now(), 49000, 51000, 48000, 50000, 1000000]],
  indicators: {
    rsi: 60,
    macd: { macd: 0.5, signal: 0.3, histogram: 0.2 },
    bollinger: { upper: 52000, middle: 50000, lower: 48000 },
    movingAverages: { sma20: 49500, sma50: 49000 }
  }
};

// Basis test suite voor TechnicalAnalysisAgent
describe('TechnicalAnalysisAgent', () => {
  it('should perform technical analysis', async () => {
    // Maak een instantie van de agent
    const mockConfig: AgentConfig = {
  id: 'test-technical',
  name: 'Test Technical Agent',
  specialization: 'Technical Analysis Testing',
  aiProvider: 'mock',
  aiModel: 'mock-model'
};
const agent = new TechnicalAnalysisAgent(mockConfig);

    // Mock de callAI methode om een gecontroleerde response te retourneren
    vi.spyOn(agent, 'callAI').mockResolvedValueOnce(JSON.stringify({
      recommendation: 'BUY',
      confidence: 80,
      reasoning: 'Test reasoning'
    }));

    // Roep de analyze methode aan
    const result = await agent.analyze(mockMarketData);

    // Controleer de resultaten
    expect(result.recommendation).toBe('BUY');
    expect(result.recommendation).toBe('BUY');
    expect(result.confidence).toBe(80);
    expect(result.reasoning).toContain('Test reasoning');
    expect(result.type).toBeDefined();
    expect(result.symbol).toBe('BTC');
    expect(result.timestamp).toBeInstanceOf(Date);
  });
});