import { vi, describe, it, expect } from 'vitest';
import { TechnicalAnalysisAgent } from '../lib/agents/TechnicalAnalysisAgent';
import { MarketData } from '../lib/agents/TechnicalAnalysisAgent'; // Importeer de MarketData interface

// Mock data voor de test
// Dit is voorbeeld market data om de analyze methode te testen.
const mockMarketData: MarketData = {
  symbol: 'BTC',
  currentPrice: 50000,
  volume: 1000000,
  ohlcv: [[Date.now(), 49000, 51000, 48000, 50000, 1000000]],
  indicators: {
    rsi: 60,
    macd: { macd: 0.5, signal: 0.3, histogram: 0.2 },
    bollinger: { upper: 52000, middle: 50000, lower: 48000 },
    movingAverages: { sma20: 49500, sma50: 49000 }
  }
};

// Basis test suite voor TechnicalAnalysisAgent
describe('TechnicalAnalysisAgent', () => {
  it('should perform technical analysis', async () => {
    // Maak een instantie van de agent
    const agent = new TechnicalAnalysisAgent(); // Voeg config toe als nodig

    // Mock de callAI methode om een gecontroleerde response te retourneren
    vi.spyOn(agent, 'callAI').mockResolvedValueOnce(JSON.stringify({
      recommendation: 'BUY',
      confidence: 80,
      entryPrice: 50500,
      stopLoss: 49000,
      takeProfit: 52000,
      reasoning: 'Test reasoning',
      keyLevels: { support: [49000], resistance: [52000] },
      timeframe: '4h',
      riskLevel: 'MEDIUM',
      technicalSignals: { trend: 'BULLISH', momentum: 'STRONG', volume: 'HIGH' },
      patternAnalysis: 'Test pattern',
      marketStructure: 'Test structure'
    }));

    // Roep de analyze methode aan
    const result = await agent.analyze(mockMarketData);

    // Controleer de resultaten
    expect(result.recommendation).toBe('BUY');
    expect(result.confidence).toBe(80);
    expect(result.reasoning).toContain('Test reasoning');
  });
});